service:
  expose:
  - 13000
deployment:
  tolerations:
  - key: graviton
    operator: Equal
    value: 'true'
    effect: NoSchedule
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - key: kubernetes.io/arch
          operator: In
          values:
          - arm64
  labels:
    billing: platforms
    sub-billing: ticketing-system
  probePort: 13000
  probePath: /actuator/health
  podAnnotations:
    iam.amazonaws.com/role: arn:aws:iam::035243212545:role/k8s-ticketing-system-stage
  resources:
    limits:
      cpu: 1
      memory: 2Gi
    requests:
      cpu: 1
      memory: 1Gi
  env:
  - name: LOG_LEVEL
    value: DEBUG
  - name: WORKER_THREADS
    value: 40
  - name: ENVIRONMENT
    value: stage
istio:
  internal:
    hosts:
    - ticketing-system.stage.cure.fit.internal
  external:
    hosts:
    - ticketing-system.stage.curefit.co
    match:
    - prefix: /ticket/external
    - prefix: /comment/external
    - exact: /google/oauth/callback
externalSecrets:
  enabled: 'true'
pager:
  provider: opsgenie
  service-name: ticketing-system
bugtracker:
  provider: rollbar
  service-name: ticketing-system
support:
  slack-channel: supply-on-call
  mailing-list: <EMAIL>
apm:
  provider: datadog
  service-name: ticketing-system
pod-id: supply
tags:
  billing: supply
scaling:
  scaleUpAtCPU: 1.0
  minReplicas: 1
  maxReplicas: 2
  keda:
    triggers:
    - type: cpu
      metadata:
        value: 1000m
      metricType: AverageValue
    - type: cron
      metadata:
        timezone: Asia/Kolkata
        start: 40 8 * * *
        end: 20 18 * * *
        desiredReplicas: '1'
