version: v2beta1

name: updated-by-kdever-master

pipelines:
  # This is the pipeline for the main command: `devspace dev` (or `devspace run-pipeline dev`)
  dev:
    run: |-
      start_dev app

dev:
  app:
    workingDir: /app
    # Search for the container that runs this image
    labelSelector:
      app: ${APP_NAME}
      version: ${VIRTUAL_CLUSTER}
      VC: ${VIRTUAL_CLUSTER}
    resources:
      limits:
        cpu: 2
        memory: 4Gi
      requests:
        cpu: 100m
        memory: 100Mi
    args:
      - "-c"
    container: ${APP_NAME}
    command: ["/bin/bash"]
    # Replace the container image with this dev-optimized image (allows to skip image building during development)
    # devImage: ghcr.io/loft-sh/devspace-containers/go:1.18-alpine
    # Forward the following ports to be able access your application via localhost
    restartHelper:
      inject: true
    env:
      - name: VERSION
        value: ${VIRTUAL_CLUSTER}

    # Inject a lightweight SSH server into the container (so your IDE can connect to the remote dev env)
    ssh:
      enabled: true
      useInclude: true
    logs: {}
