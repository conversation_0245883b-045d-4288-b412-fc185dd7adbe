<?xml version="1.0" encoding="UTF-8"?>
<project
	xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.7.18</version>
		<relativePath /> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.curefit</groupId>
	<artifactId>ticketing-system</artifactId>
	<version>3.0.0</version>
	<name>Ticketing System</name>
	<packaging>pom</packaging>

	<properties>
		<maven.compiler.source>21</maven.compiler.source>
		<maven.compiler.target>21</maven.compiler.target>
		<java.version>21</java.version>
		<spring.cloud-version>2021.0.9</spring.cloud-version>
		<spring-boot.version>2.7.18</spring-boot.version>
		<curefit.common.version>2.14.1</curefit.common.version>
		<cf.common.version>1.2.28</cf.common.version>
		<aws.sdk.version>1.12.710</aws.sdk.version>
		<jackson-bom.version>2.16.1</jackson-bom.version>
		<gymfit.version>2.0.10</gymfit.version>
	</properties>

	<modules>
		<module>ticket-management-core</module>
		<module>ticket-management-commons</module>
		<module>ticket-management-client</module>
	</modules>

	<dependencies>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.32</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.flywaydb</groupId>
			<artifactId>flyway-core</artifactId>
			<version>9.22.0</version> <!-- Use the latest compatible version -->
		</dependency>

		<dependency>
			<groupId>org.flywaydb</groupId>
			<artifactId>flyway-mysql</artifactId>
			<version>9.22.0</version> <!-- Use the latest compatible version -->
		</dependency>

		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
			<version>9.3.0</version>
		</dependency>
	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring.cloud-version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<dependency>
				<groupId>com.fasterxml.jackson</groupId>
				<artifactId>jackson-bom</artifactId>
				<version>${jackson-bom.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<dependency>
				<groupId>com.amazonaws</groupId>
				<artifactId>aws-java-sdk-bom</artifactId>
				<version>${aws.sdk.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<dependency>
				<groupId>software.amazon.awssdk</groupId>
				<artifactId>bom</artifactId>
				<version>2.25.40</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<extensions>
			<extension>
				<groupId>org.springframework.build</groupId>
				<artifactId>aws-maven</artifactId>
				<version>5.0.0.RELEASE</version>
			</extension>
		</extensions>

		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.11.0</version>
				<configuration>
					<proc>full</proc>
					<source>21</source>
					<target>21</target>
					<compilerArgs>--enable-preview</compilerArgs>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>0.7.9</version>
				<executions>
					<execution>
						<id>prepare-unit-tests</id>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
					<execution>
						<id>prepare-agent</id>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
						<phase>pre-integration-test</phase>
						<configuration>
							<propertyName>itCoverageAgent</propertyName>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>

		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.jacoco</groupId>
					<artifactId>jacoco-maven-plugin</artifactId>
					<version>0.8.11</version>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>

	<repositories>
		<repository>
			<id>github</id>
			<url>https://maven.pkg.github.com/curefit/*</url>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>

		<repository>
			<id>curefit-s3-release-repo</id>
			<name>S3 Release Repository for Curefit</name>
			<url>s3://repo.mvn.com.curefit/release</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>

		<repository>
			<id>curefit-s3-snapshot-repo</id>
			<name>S3 Snapshot Repository for Curefit</name>
			<url>s3://repo.mvn.com.curefit/snapshot</url>
			<releases>
				<enabled>false</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>

		<repository>
			<id>spring-releases</id>
			<url>https://repo.spring.io/libs-release</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
	</repositories>

	<pluginRepositories>
		<pluginRepository>
			<id>spring-releases</id>
			<url>https://repo.spring.io/libs-release</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</pluginRepository>
	</pluginRepositories>

	<scm>
		<developerConnection>scm:git:https://github.com/curefit/ticketing-system.git</developerConnection>
		<tag>HEAD</tag>
	</scm>

	<distributionManagement>
		<repository>
			<id>github</id>
			<name>GitHub curefit Apache Maven Packages</name>
			<url>https://maven.pkg.github.com/curefit/ticketing-system</url>
		</repository>
	</distributionManagement>

</project>
