package com.curefit.odin.audit.pojo;

import com.curefit.cf.commons.pojo.BaseEntry;
import com.curefit.cf.commons.pojo.audit.Change;
import com.curefit.cf.commons.pojo.audit.EventType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */

@Setter
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuditEventEntry extends BaseEntry {

  String parentId;

  String parentEntity;

  String entityId;

  String entity;

  EventType eventType;

  List<Change> changeList;

  String modifiedBy;

  Date modifiedOn;
}
