package com.curefit.odin.audit.pojo;

import java.util.Date;
import java.util.List;

import com.curefit.cf.commons.pojo.audit.Change;
import com.curefit.cf.commons.pojo.audit.EventType;
import com.curefit.odin.admin.pojo.UserEntry;
import lombok.Data;

@Data
public class TicketRevision {

  String entity;

  UserEntry user;

  EventType action;

  String actionText;

  List<Change> changes;

  Date modifiedOn;
}
