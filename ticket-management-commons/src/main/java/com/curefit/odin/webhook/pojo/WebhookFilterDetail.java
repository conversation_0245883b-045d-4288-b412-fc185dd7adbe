package com.curefit.odin.webhook.pojo;

import com.curefit.odin.admin.pojo.CategoryEntry;
import com.curefit.odin.admin.pojo.SubCategoryEntry;
import com.curefit.odin.admin.pojo.UserEntry;
import com.curefit.odin.enums.Priority;
import com.curefit.odin.enums.Status;
import com.curefit.odin.enums.TicketSource;
import com.curefit.odin.utils.pojo.TenantEntry;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WebhookFilterDetail implements Serializable {

  List<TenantEntry> tenants;

  List<CategoryEntry> categories;

  List<SubCategoryEntry> subCategories;

  List<Status> status;

  @JsonFormat(pattern = "yyyy-MM-dd")
  Date dueDateFrom;

  @JsonFormat(pattern = "yyyy-MM-dd")
  Date dueDateTo;

  List<String> cities;

  List<String> locations;

  List<Priority> priorities;

  List<UserEntry> assignedUsers;

  List<UserEntry> createdBy;

  @JsonFormat(pattern = "yyyy-MM-dd")
  Date createdOnFrom;

  @JsonFormat(pattern = "yyyy-MM-dd")
  Date createdOnTo;

  Map<String, List<String>> fields;

  TicketSource source;

  List<String> sourceRefIds;
}
