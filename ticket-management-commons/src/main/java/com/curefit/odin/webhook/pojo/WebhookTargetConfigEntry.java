package com.curefit.odin.webhook.pojo;

import com.curefit.odin.enums.EndpointType;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 */

@ToString
@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WebhookTargetConfigEntry<T> extends BaseOdinEntry {

  EndpointType endpointType;

  T config;
}
