package com.curefit.odin.webhook.configs;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class HTTPConfig extends EndPointConfig {

  String endPoint;

  Map<String, String> headers = new HashMap<>();
}
