package com.curefit.odin.webhook.pojo;

import com.curefit.odin.enums.EntityEvent;
import com.curefit.odin.enums.WebhookEntity;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

@FieldDefaults(level = AccessLevel.PRIVATE)
@Getter
@Setter
@ToString
public class WebhookMeta {

    WebhookEntity entity;

    Map<EntityEvent, List<EventMeta>> events;

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EventMeta {
        String key;
        String newValue;
    }
}
