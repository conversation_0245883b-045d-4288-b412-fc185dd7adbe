package com.curefit.odin.webhook.pojo;

import com.curefit.odin.enums.EntityEvent;
import com.curefit.odin.enums.WebhookEntity;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
@ToString
public class WebhookConfigEntry extends BaseOdinEntry {

  String name;

  String description;

  WebhookFilter webhookFilter;

  WebhookFilterDetail webhookFilterDetail;

  Map<WebhookEntity, Set<EntityEvent>> events;

  List<WebhookMeta> webhookEventsMeta;

  Boolean excludeBody;

  WebhookTargetConfigEntry webhookTargetConfigEntry;

  Map<String, Object> customPayload;
}
