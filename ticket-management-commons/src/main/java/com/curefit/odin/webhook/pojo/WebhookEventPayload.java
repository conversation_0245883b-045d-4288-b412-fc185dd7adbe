package com.curefit.odin.webhook.pojo;

import com.curefit.cf.commons.pojo.audit.Change;
import com.curefit.odin.user.pojo.AttachmentEntry;
import com.curefit.odin.user.pojo.CommentEntry;
import com.curefit.odin.user.pojo.TicketEntry;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 */

@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WebhookEventPayload {

  TicketEntry ticket;

  CommentEntry comment;

  AttachmentEntry attachment;

  @JsonIgnore
  List<Change> changes;
}
