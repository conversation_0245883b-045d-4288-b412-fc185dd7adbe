package com.curefit.odin.webhook.pojo;

import com.curefit.odin.enums.Priority;
import com.curefit.odin.enums.Status;
import com.curefit.odin.enums.TicketSource;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class WebhookFilter implements Serializable {

  List<Long> tenantIds;

  List<Long> categoryIds;

  List<Long> subCategoryIds;

  List<Status> status;

  @JsonFormat(pattern = "yyyy-MM-dd")
  Date dueDateFrom;

  @JsonFormat(pattern = "yyyy-MM-dd")
  Date dueDateTo;

  List<String> cities;

  List<String> locations;

  List<Priority> priorities;

  List<Long> assignedQueueIds;

  List<String> assignedUserIds;

  List<String> createdBy;

  @JsonFormat(pattern = "yyyy-MM-dd")
  Date createdOnFrom;

  @JsonFormat(pattern = "yyyy-MM-dd")
  Date createdOnTo;

  Map<String, List<String>> fields;

  TicketSource source;

  List<String> sourceRefIds;

  List<String> watcherUserIds;
}
