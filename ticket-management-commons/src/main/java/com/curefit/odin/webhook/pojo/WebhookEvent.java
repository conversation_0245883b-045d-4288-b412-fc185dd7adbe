package com.curefit.odin.webhook.pojo;

import com.curefit.odin.admin.pojo.UserEntry;
import com.curefit.odin.enums.EntityEvent;
import com.curefit.odin.enums.WebhookEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */

@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WebhookEvent implements Serializable {

  WebhookEntity entity;

  EntityEvent entityEvent;

  WebhookEventPayload payload;

  UserEntry user;

  Long modifiedOn;

  @JsonIgnore
  List<FieldChange> changeList;

}
