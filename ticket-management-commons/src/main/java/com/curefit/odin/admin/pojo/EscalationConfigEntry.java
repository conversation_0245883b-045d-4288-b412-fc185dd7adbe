package com.curefit.odin.admin.pojo;

import com.curefit.odin.user.pojo.BaseOdinEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 */


@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
public class EscalationConfigEntry extends BaseOdinEntry {

  Long categoryId;

  Long subCategoryId;

  List<EscalationRule> escalationRules;
}
