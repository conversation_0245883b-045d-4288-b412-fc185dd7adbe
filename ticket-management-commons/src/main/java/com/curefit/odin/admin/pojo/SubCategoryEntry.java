package com.curefit.odin.admin.pojo;

import java.util.List;
import java.util.Map;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import com.curefit.odin.enums.Priority;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(Include.NON_NULL)
public class SubCategoryEntry extends BaseOdinEntry {

	/**
	*
	*/
	private static final long serialVersionUID = 5699427708920911544L;

	@NotEmpty
	String name;

	@NotNull
	Long categoryId;

	Long tenantId;

	List<CustomPrioritySLAEntry> prioritySLAEntries;

	AssigneeQueueEntry assigneeQueueEntry;

	List<EscalationRule> escalationRules;

	AssigneeQueueEntry watchersQueue;

	AssigneeQueueEntry approvalQueue;

	Map<String, Object> meta;

	Boolean refreshOpenTickets = false;

}
