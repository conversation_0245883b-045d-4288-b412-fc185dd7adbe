package com.curefit.odin.admin.pojo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.Map;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomFieldTreeResponse implements Serializable {

  CustomFieldTreeNode customFieldTree;

  Map<Long, CustomFieldEntry> fields;
}
