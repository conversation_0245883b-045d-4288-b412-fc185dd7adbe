package com.curefit.odin.admin.pojo;

import javax.validation.constraints.NotNull;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import java.util.List;


@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(Include.NON_NULL)
public class DefaultAssigneeEntry extends BaseOdinEntry {

  /**
  *
  */
  private static final long serialVersionUID = 8247114108804782903L;

  @NotNull
  AssigneeQueueEntry assigneeQueueEntry;
  
  Long subCategoryId;

  Long categoryId;

  List<UserEntry> assignedUsers;
}
