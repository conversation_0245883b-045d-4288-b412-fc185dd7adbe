package com.curefit.odin.admin.pojo;

import javax.validation.constraints.NotNull;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(Include.NON_NULL)
public class TenantLocationEntry extends BaseOdinEntry {
  /**
   * 
   */
  private static final long serialVersionUID = 1451569426975148885L;
  
  @NotNull
  Long tenantId;
  
  @NotNull
  Long locationDataSourceId;
  
  String type;
}
