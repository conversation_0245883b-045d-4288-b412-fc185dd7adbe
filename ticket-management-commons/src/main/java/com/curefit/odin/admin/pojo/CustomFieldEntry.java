package com.curefit.odin.admin.pojo;

import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import com.curefit.odin.enums.DataType;
import com.curefit.odin.enums.FieldType;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import com.curefit.odin.user.pojo.FieldEntry;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(Include.NON_NULL)
@Getter
@Setter
@NoArgsConstructor
public class CustomFieldEntry extends BaseOdinEntry {
  
  /**
  * 
  */
  private static final long serialVersionUID = 685199843099367630L;

  @NotEmpty
  String name;

  Long categoryId;

  Long subCategoryId;

  Long tenantId;

  @NotNull
  DataType dataType;

  boolean isMandatory;

  Long dataSourceId;

  DataSourceEntry dataSourceEntry;

  List<FieldEntry> values;

  Long order;

  FieldType type;

  boolean mandatoryOnClosing;

  boolean showOnCreation;

  boolean showOnUpdation;

  public CustomFieldEntry(@NotEmpty String name, @NotNull DataType dataType, Long tenantId, Long order, FieldType type) {
    super();
    this.name = name;
    this.dataType = dataType;
    this.tenantId = tenantId;
    this.order = order;
    this.type = type;
  }
}
