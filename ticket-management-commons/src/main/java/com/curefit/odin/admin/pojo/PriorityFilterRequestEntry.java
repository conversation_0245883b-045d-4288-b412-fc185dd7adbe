package com.curefit.odin.admin.pojo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 */

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@AllArgsConstructor
public class PriorityFilterRequestEntry implements Serializable {

  /**
   *
   */
  private static final long serialVersionUID = -855622737598030450L;

  Long tenantId;

  Long categoryId;

  Long subCategoryId;

}