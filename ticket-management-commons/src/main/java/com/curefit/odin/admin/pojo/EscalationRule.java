package com.curefit.odin.admin.pojo;

import com.curefit.odin.enums.EscalationLevel;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EscalationRule implements Serializable {

  EscalationLevel escalationLevel;

  int escalationTimeInHours;

  Long queueId;

  AssigneeQueueEntry queueEntry;

  // Used for storing user for a location
  List<UserEntry> escalationUsers;

}
