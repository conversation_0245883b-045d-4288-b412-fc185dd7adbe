package com.curefit.odin.admin.pojo;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotEmpty;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
public class UserEntry extends BaseOdinEntry {

  /**
  * 
  */
  private static final long serialVersionUID = 2135761950549685379L;

  @NotEmpty
  String name;

  @Email
  String emailId;
  
  String mobileNumber;
  
  String tenant;
  
  String designation;

  Boolean isDl = false;

  Integer memberCount = 1;

  public UserEntry(String name, String emailId, Boolean isDl, Integer memberCount){
    this.name = name;
    this.emailId = emailId;
    this.isDl = isDl;
    this.memberCount = memberCount;
  }

  public UserEntry(String name, String emailId, String mobileNumber, Boolean isDl, Integer memberCount){
    this.name = name;
    this.emailId = emailId;
    this.mobileNumber = mobileNumber;
    this.isDl = isDl;
    this.memberCount = memberCount;
  }

  public UserEntry(String name, String emailId){
    this.name = name;
    this.emailId = emailId;
  }

  public UserEntry(String emailId){
    this.emailId = emailId;
  }
}
