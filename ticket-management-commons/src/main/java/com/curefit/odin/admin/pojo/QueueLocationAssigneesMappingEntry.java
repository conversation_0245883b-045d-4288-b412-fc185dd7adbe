package com.curefit.odin.admin.pojo;

import com.curefit.odin.enums.LocationType;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import com.curefit.odin.utils.pojo.LocationHierarchyNode;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */

@Setter
@Getter
@ToString
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
public class QueueLocationAssigneesMappingEntry extends BaseOdinEntry {

  Long assigneeQueueId;

  LocationHierarchyNode<Set<UserEntry>> locationsAssignees;

  List<LocationHierarchyNode<Set<UserEntry>>> locationHierarchyNodesAssignees;

  @JsonIgnore
  LocationType locationType;

  @JsonIgnore
  String locationCode;

  @JsonIgnore
  Set<String> assignees;

  public QueueLocationAssigneesMappingEntry(Long assigneeQueueId, LocationHierarchyNode locationsAssignees) {
    this.assigneeQueueId = assigneeQueueId;
    this.locationsAssignees = locationsAssignees;
  }

  public QueueLocationAssigneesMappingEntry(Long assigneeQueueId, List<LocationHierarchyNode<Set<UserEntry>>> locationHierarchyNodesAssignees) {
    this.assigneeQueueId = assigneeQueueId;
    this.locationHierarchyNodesAssignees = locationHierarchyNodesAssignees;
  }

  public QueueLocationAssigneesMappingEntry(Long assigneeQueueId, LocationType locationType, String locationCode, Set<String> assignees) {
    this.assigneeQueueId = assigneeQueueId;
    this.locationType = locationType;
    this.locationCode = locationCode;
    this.assignees = assignees;
  }

  public String getUniqueCode() {
    return this.getLocationCode() + "__" + this.getLocationType();
  }
}
