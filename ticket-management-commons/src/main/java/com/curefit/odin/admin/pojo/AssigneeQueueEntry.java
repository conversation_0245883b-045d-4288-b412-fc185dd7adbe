package com.curefit.odin.admin.pojo;

import com.curefit.odin.enums.AssigneeQueueSource;
import com.curefit.odin.enums.AssigneeQueueType;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */

@Setter
@Getter
@ToString
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssigneeQueueEntry extends BaseOdinEntry {
  private static final long serialVersionUID = -4487727930181839793L;

  String name;

  AssigneeQueueSource source;

  Long tenantId;

  String type;

}
