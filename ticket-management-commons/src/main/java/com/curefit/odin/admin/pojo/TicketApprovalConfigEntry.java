package com.curefit.odin.admin.pojo;

import com.curefit.odin.user.pojo.BaseOdinEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 */

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TicketApprovalConfigEntry extends BaseOdinEntry {

    Long categoryId;

    Long subCategoryId;

    AssigneeQueueEntry approvalQueue;
}
