package com.curefit.odin.admin.pojo;

import com.curefit.odin.enums.Priority;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(Include.NON_NULL)
public class IssueTemplateEntry extends BaseOdinEntry {

    /**
     *
     */
    private static final long serialVersionUID = 5699427708920911544L;

    @NotEmpty
    String title;

    @NotNull
    Long subCategoryId;

    Priority priority;

    Boolean refreshOpenTickets = false;
}
