/**
 * 
 */
package com.curefit.odin.admin.pojo;

import java.io.Serializable;
import java.util.List;
import com.curefit.cf.commons.pojo.BaseEntry;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 *
 */
public class PagedResultEntryV2<E extends BaseEntry> implements Serializable {

  /**
   * 
   */
  private static final long serialVersionUID = -7313350238443923131L;

  private int offset;
  private int limit;
  private long totalElements;

  @JsonProperty("content")
  private List<E> elements;

  /**
   * 
   */
  public PagedResultEntryV2() {}

  public PagedResultEntryV2(List<E> elements, long totalElements, int offset, int limit) {
    this.elements = elements;
    this.totalElements = totalElements;
    this.offset = offset;
    this.limit = limit;
  }

  public boolean hasMore() {
    return totalElements > offset + limit;
  }

  public boolean hasPrevious() {
    return offset > 0 && totalElements > 0;
  }

  public long getTotalElements() {
    return totalElements;
  }

  public int getOffset() {
    return offset;
  }

  public int getLimit() {
    return limit;
  }

  public List<E> getElements() {
    return elements;
  }

  /**
   * @param offset the offset to set
   */
  public void setOffset(int offset) {
    this.offset = offset;
  }

  /**
   * @param limit the limit to set
   */
  public void setLimit(int limit) {
    this.limit = limit;
  }

  /**
   * @param totalElements the totalElements to set
   */
  public void setTotalElements(long totalElements) {
    this.totalElements = totalElements;
  }



}
