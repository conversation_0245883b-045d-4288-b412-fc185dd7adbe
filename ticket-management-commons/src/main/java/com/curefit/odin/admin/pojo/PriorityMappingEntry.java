package com.curefit.odin.admin.pojo;

import com.curefit.odin.enums.Priority;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 */


@Getter
@Setter
@NoArgsConstructor
@ToString
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PriorityMappingEntry extends BaseOdinEntry {

    /**
     *
     */
    private static final long serialVersionUID = 685199843099367630L;

    @NotEmpty
    Priority name;

    Long categoryId;

    Long subCategoryId;

    Long tenantId;

    Integer order;

    Long dueDate;

}