package com.curefit.odin.admin.pojo;

import javax.validation.constraints.NotEmpty;

import com.curefit.odin.user.pojo.BaseOdinEntry;
import com.curefit.odin.user.pojo.TicketFilterRequestEntry;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(Include.NON_NULL)
@Getter
@Setter
public class FilterEntry extends BaseOdinEntry {

  /**
  * 
  */
  private static final long serialVersionUID = -4450393638630734878L;

  @NotEmpty
  String name;

  TicketFilterRequestEntry filter;

}
