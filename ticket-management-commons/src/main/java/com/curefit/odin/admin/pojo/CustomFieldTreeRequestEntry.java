package com.curefit.odin.admin.pojo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;


@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@ToString
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@AllArgsConstructor
public class CustomFieldTreeRequestEntry implements Serializable {

  private static final long serialVersionUID = -855622737598030450L;

  List<Long> tenantIds;

  List<Long> categoryIds;

  List<Long> subCategoryIds;

}
