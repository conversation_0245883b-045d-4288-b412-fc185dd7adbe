package com.curefit.odin.admin.pojo;

import com.curefit.odin.user.pojo.BaseOdinEntry;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
public class DataSourceValueEntry extends BaseOdinEntry {

  /**
   * 
   */
  private static final long serialVersionUID = 5715013708890943463L;

  protected Long dataSourceId;

  protected String referenceId;

  protected String value;

  protected JsonNode metadata;

  public DataSourceValueEntry(String value) {
    this.value = value;
  }

  public DataSourceValueEntry(String value, String valueReferenceId) {
    this.value = value;
    this.referenceId = valueReferenceId;
  }

}
