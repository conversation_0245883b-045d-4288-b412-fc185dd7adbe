package com.curefit.odin.admin.pojo;

import com.curefit.cf.commons.pojo.BaseEntry;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 */

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class WatcherConfigEntry extends BaseOdinEntry {

    Long categoryId;

    Long subCategoryId;

    AssigneeQueueEntry watchersQueue;
}
