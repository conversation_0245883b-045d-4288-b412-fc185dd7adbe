package com.curefit.odin.admin.pojo;

import com.curefit.odin.enums.CustomFieldTreeNodeType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.*;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomFieldTreeNode implements Serializable {

  String name;

  CustomFieldTreeNodeType type;

  @JsonIgnore
  Long code;

  List<Long> fieldIds;

  List<CustomFieldTreeNode> children;

  @JsonProperty("key")
  public String getKey(){
    return type + "_" + code;
  }

  public CustomFieldTreeNode(CustomFieldTreeNodeType type) {
    this.type = type;
    this.children = new ArrayList<>();
  }

  public CustomFieldTreeNode(CustomFieldTreeNodeType type, String name, Long code) {
    this.type = type;
    this.name = name;
    this.code = code;
    this.children = new ArrayList<>();
  }

  @Override
  public boolean equals(Object obj) {
    if (obj instanceof CustomFieldTreeNode) {
      CustomFieldTreeNode locationHierarchyNode = (CustomFieldTreeNode) obj;
      if (this.getCode() == locationHierarchyNode.getCode() && this.getType() == locationHierarchyNode.getType() && this.getName() == locationHierarchyNode.getName()) {
        return true;
      }
      return this.getCode().equals(locationHierarchyNode.getCode()) && this.getName().equals(locationHierarchyNode.getName()) && this.getType().equals(locationHierarchyNode.getType());
    }
    return false;
  }

  @Override
  public int hashCode() {
    return Objects.hash(this.getCode(), this.getName(), this.getType());
  }
}
