package com.curefit.odin.admin.pojo;

import java.util.List;
import java.util.Map;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataSourceEntry extends BaseOdinEntry {

  /**
   * 
   */
  private static final long serialVersionUID = -4485174800969922574L;

  Long tenantId;

  String url;
  
  String rootPath;
  
  Map<String, String> headerObject;
  
  Map<String, String> mappingObject;
  
  int syncDurationInHours;
  
  boolean staticList;
  
  String name;
  
  List<DataSourceValueEntry> dataSourceValues;
  
}
