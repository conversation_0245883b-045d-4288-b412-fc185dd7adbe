package com.curefit.odin.admin.pojo;

import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Setter
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(Include.NON_NULL)
public class CategoryEntry extends BaseOdinEntry {

	/**
	*
	*/
	private static final long serialVersionUID = -698949249629870418L;

	@NotEmpty
	String name;

	@NotNull
	Long tenantId;

	Integer maxStatusChangeTime;

	BusinessHours businessHours;

	Boolean isConfidential;

	List<CustomPrioritySLAEntry> prioritySLAEntries;

	AssigneeQueueEntry assigneeQueueEntry;

	List<EscalationRule> escalationRules;

	AssigneeQueueEntry watchersQueue;

	AssigneeQueueEntry approvalQueue;

	Boolean refreshOpenTickets = false;
}
