package com.curefit.odin.enums;

import com.fasterxml.jackson.annotation.JsonValue;

import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */

public enum TicketSource {

  AUDIT_PLATFORM("Audit Platform"),
  CUSTOMER_SUPPORT("Customer Support"),
  GYMFIT("Gymfit"),
  EMAIL("Email"),
  HR_SUPPORT("HR Support"),
  PARTNER_SUPPORT("Partner Support"),
  SUGARFIT("SUGARFIT"),
  TELESALES("Telesales"),
  KRONOS("Kronos"),
  D2C_ENGINE("D2C Engine"),
  ODIN("Odin"),
  WOOQER("Wooqer"),
  SPRINKLR("Sprinklr"),
  EMR("EMR"),
  ORDER_SYSTEM("Order System");

  public static final Map<String, TicketSource> SOURCE_MAP;

  static {
    Map<String, TicketSource> map = new ConcurrentHashMap<>();
    for (TicketSource instance : TicketSource.values()) {
      map.put(instance.getName(), instance);
    }
    SOURCE_MAP = Collections.unmodifiableMap(map);
  }

  private String name;

  TicketSource(String name) {
    this.name = name;
  }

  @JsonValue
  public String getName() {
    return name;
  }
}
