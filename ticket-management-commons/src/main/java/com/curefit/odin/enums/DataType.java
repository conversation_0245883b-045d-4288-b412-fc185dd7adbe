package com.curefit.odin.enums;

public enum DataType {

  TEXT("TEXT"), DATE("DATE"), TIME("TIME"), DATETIME("DATETIME"), NUMBER("NUMBER"), LIST("LIST");

  private String displayName;

  DataType(String displayName) {
    this.displayName = displayName;
  }

  public String displayName() {
    return displayName;
  }

  // Optionally and/or additionally, toString.
  @Override
  public String toString() {
    return displayName;
  }
}
