package com.curefit.odin.enums;

import com.fasterxml.jackson.annotation.JsonValue;

import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public enum Priority {
  P0("Code Red"), P2("High"), P5("Medium"), P10("Low"), NA("");

  private static final Map<String, Priority> PRIORITY_MAP;

  static {
    Map<String, Priority> map = new ConcurrentHashMap<>();
    for (Priority instance : Priority.values()) {
      map.put(instance.getName(), instance);
    }
    PRIORITY_MAP = Collections.unmodifiableMap(map);
  }

  public static Priority get(String name) {
    return PRIORITY_MAP.get(name);
  }


  private String name;

  Priority(String name) {
    this.name = name;
  }

  @JsonValue
  public String getName() {
    return name;

  }
}
