package com.curefit.odin.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */


public enum LocationType {
  ALL(),
  COUNTRY(ALL),
  CITY(ALL, COUNTRY),
  CLUSTER(ALL, COUNTRY, CITY),
  CENTRE(ALL, COUNTRY, CITY, CLUSTER);

  private LocationType[] locationHierarchy;

  LocationType(LocationType... locationHierarchy) {
    this.locationHierarchy = locationHierarchy;
  }

  public List<LocationType> getHierarchy() {
    List<LocationType> hierarchy = new ArrayList<>(Arrays.asList(locationHierarchy));
    hierarchy.add(this);
    return hierarchy;
  }

  public static List<LocationType> getAllOrdered() {
    return Arrays.asList(ALL, COUNTRY, CITY, CLUSTER, CENTRE);
  }

  public static List<LocationType> getAllReverseOrdered() {
    return Arrays.asList(CENTRE, CLUSTER, CITY, COUNTRY, ALL);
  }
}
