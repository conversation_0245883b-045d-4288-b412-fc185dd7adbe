package com.curefit.odin.enums;

import com.curefit.odin.webhook.configs.HTTPConfig;

/**
 * <AUTHOR>
 */

public enum EndpointType {
  HTTP(HTTPConfig.class);
  //QUEUE(QueueConfig.class), SLACK(SlackConfig.class);

  Class<?> endPointConfigClass;

  EndpointType(Class<?> endPointConfigClass) {
    this.endPointConfigClass = endPointConfigClass;
  }

  public Class<?> getEndPointConfigClass() {
    return endPointConfigClass;
  }
}
