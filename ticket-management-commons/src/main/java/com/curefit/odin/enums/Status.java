package com.curefit.odin.enums;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import com.fasterxml.jackson.annotation.JsonValue;

public enum Status {

  OPEN("Open"),
  IN_PROGRESS("In Progress"),
  RESOLVED("Resolved"),
  ON_HOLD("On Hold"),
  REJECTED("Rejected"),
  DONE("Done");


  private static final Map<String, Status> STATUS_MAP;

  static {
    Map<String, Status> map = new ConcurrentHashMap<String, Status>();
    for (Status instance : Status.values()) {
      map.put(instance.getName(), instance);
    }
    STATUS_MAP = Collections.unmodifiableMap(map);
  }

  public static Status get(String name) {
    return STATUS_MAP.get(name);
  }

  private String name;

  Status(String name) {
    this.name = name;
  }

  @JsonValue
  public String getName() {
    return name;

  }

  public static List<Status> approvalRequiredTicketStatus() {
    return Arrays.asList(OPEN, IN_PROGRESS, DONE, ON_HOLD, REJECTED);
  }

  public static List<Status> approversTicketStatus() {
    return Arrays.asList(OPEN, IN_PROGRESS, DONE, RESOLVED, ON_HOLD, REJECTED);
  }

  public static List<Status> closedTicketStatus() {
    return Arrays.asList(RESOLVED, REJECTED);
  }

  public static List<Status> closedTicketStatusV2() {
    return Arrays.asList(RESOLVED, REJECTED,DONE);
  }

  public static List<Status> slaDisabledStatus() {
    return Arrays.asList(RESOLVED, REJECTED, ON_HOLD);
  }

  public static List<Status> fetchAll() {
    return Arrays.asList(OPEN, IN_PROGRESS, RESOLVED, ON_HOLD, REJECTED);
  }
}
