package com.curefit.odin.enums;

import java.io.Serializable;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.curefit.odin.enums.EntityEvent.*;

/**
 * <AUTHOR>
 */

public enum WebhookEntity {
  TICKET(CREATE, UPDATE),
  COMMENT(CREATE, UPDATE, DELETE),
  ATTACHMENT(CREATE, UPDATE, DELETE),
  TICKETWATCHER(CREATE, UPDATE);

  Set<EntityEvent> entityEvents;

  WebhookEntity(EntityEvent... entityEvents) {
    this.entityEvents = new HashSet<>(Arrays.asList(entityEvents));
  }

  public Set<EntityEvent> getEntityEvents() {
    return entityEvents;
  }
}
