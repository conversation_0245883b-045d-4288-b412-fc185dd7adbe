package com.curefit.odin.enums;

import com.fasterxml.jackson.annotation.JsonValue;

import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */

public enum TicketDest {

  CUSTOMER_SUPPORT("Customer Support"),
  JIRA("Jira"),
  SOS("SOS"),
  SUGARFIT("SUGARFIT"),
  SPRINKLR("Sprinklr");

  public static final Map<String, TicketDest> DEST_MAP;

  static {
    Map<String, TicketDest> map = new ConcurrentHashMap<>();
    for (TicketDest instance : TicketDest.values()) {
      map.put(instance.getName(), instance);
    }
    DEST_MAP = Collections.unmodifiableMap(map);
  }

  private String name;

  TicketDest(String name) {
    this.name = name;
  }

  @JsonValue
  public String getName() {
    return name;
  }

}
