package com.curefit.odin.enums;

public enum RevisionType {
  ADDED("ADD"), MODIFIED("MOD"), DELETED("DEL");
  private String name;

  RevisionType(String name) {
    this.name = name;
  }

  public String getName() {
    return this.name;
  }

  public static RevisionType getByName(String name) {
    RevisionType matchingType = DELETED;
    for (RevisionType revisionType : RevisionType.values()) {
      if (revisionType.name.equals(name)) {
        matchingType = revisionType;
      }
    }
    return matchingType;
  }
}
