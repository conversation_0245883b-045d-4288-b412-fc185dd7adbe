package com.curefit.odin.sprinklr.pojo.message;

import com.curefit.odin.sprinklr.pojo.SprinklrAttachment;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;


@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SprinklrMessageCreateRequest {
    String from;
    String subject;
    String body;
    List<SprinklrAttachment> attachments;
    @NotEmpty
    String caseNumber;
    Map<String, List<Object>> customProperties;
    String email;
}
