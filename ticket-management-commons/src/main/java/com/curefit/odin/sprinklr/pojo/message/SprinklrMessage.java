package com.curefit.odin.sprinklr.pojo.message;

import com.curefit.odin.sprinklr.pojo.SprinklrContextualInformation;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;


@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SprinklrMessage {
    String sourceType;
    Long sourceId;
    SprinklrMessageContent content;
    String channelMessageId;
    String channelType;
    String accountType;
    Long channelCreatedTime;
    SprinklrMessageProfile senderProfile;
    SprinklrMessageProfile receiverProfile;
    String language;
    String messageId;
    Boolean brandPost; // true or false denoting agent or customer reply, respectively.
    Long createdTime;
    Long modifiedTime;
    String conversationId;
    String parentMessageId;
    Boolean autoImported;
    Boolean autoResponse;
    Long associatedCaseNumber;
    List<SprinklrContextualInformation> contextualInformation;
}
