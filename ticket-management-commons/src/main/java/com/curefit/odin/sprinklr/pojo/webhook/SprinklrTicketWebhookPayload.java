package com.curefit.odin.sprinklr.pojo.webhook;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;


@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SprinklrTicketWebhookPayload {
    String id;
    Object caseNumber;
    String subject;
    String description;
    Long version;
    String status;
    String priority;
    String caseType;
    ExternalCaseInfo externalCaseInfo; // Represents JIRA details
    Workflow workflow;
    List<Object> channelCustomProperties;
    SprinklrContact contact;
    Long createdTime;
    Long modifiedTime;
    String firstMessageId;
    String conversationId;
    Long sentiment;
    Long firstMessageAssociatedTime;
    Long latestMessageAssociatedTime;
    Long latestProfileMessageAssociatedTime;
    Long totalProcessingClockTime;
    List<Object> allEngagedUsersList;
    Long associatedFanMessageCount;
    Long associatedBrandMessageCount;
    Long associatedUserBrandMessageCount;
    Boolean deleted;
}
