package com.curefit.odin.sprinklr.pojo.webhook;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.Map;

@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SprinklrCommentWebhookRequest {
    String id;
    String type;
    SprinklrCommentWebhookPayload payload;
    Long eventTime;
    Map<String, String> subscriptionDetails;
}
