package com.curefit.odin.sprinklr.pojo;

import com.curefit.odin.sprinklr.pojo.webhook.SprinklrContact;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Map;


@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SprinklrTicketEntry extends BaseOdinEntry {

    String userId;
    String caseId;
    String caseNumber;
    String subject;
    String description;
    String status;
    Map<String, List<Object>> customProperties;
    SprinklrContact contact;
    String firstMessageId;
    String conversationId;
    Boolean isTicket;
    String odinTicketId;
    String email;
}
