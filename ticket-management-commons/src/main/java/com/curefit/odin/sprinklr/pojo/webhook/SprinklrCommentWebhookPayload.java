package com.curefit.odin.sprinklr.pojo.webhook;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SprinklrCommentWebhookPayload {
    String id;
    String text;
    Long commentingUser;
    Long createdTime;
    Long modifiedTime;
    String entityType;
    String entityId;
    String conversationId;
    Map<String,String> externalComment;
}
