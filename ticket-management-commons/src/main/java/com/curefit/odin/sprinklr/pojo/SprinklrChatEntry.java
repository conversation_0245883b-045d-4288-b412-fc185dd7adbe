package com.curefit.odin.sprinklr.pojo;

import com.curefit.odin.sprinklr.pojo.message.SprinklrMessage;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;


@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SprinklrChatEntry extends BaseOdinEntry {
    String text;
    Boolean brandPost;
    String conversationId;
    String parentMessageId;
    String messageId;
    Long associatedCaseNumber;
    SprinklrMessage message;
}
