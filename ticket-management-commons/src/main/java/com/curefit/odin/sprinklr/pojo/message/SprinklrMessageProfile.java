package com.curefit.odin.sprinklr.pojo.message;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;


@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SprinklrMessageProfile {
    String name;
    String channelType;
    String channelId;
    String permalink;
    Long followers;
    Long following;
    String username;
    Boolean unSubscribed;
    Boolean deleted;
    Long snCreatedTime;
    Long snModifiedTime;
    Long statusCount;
}
