package com.curefit.odin.sprinklr.pojo;

import com.curefit.odin.sprinklr.pojo.webhook.Workflow;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SprinklrCaseCreatePayload {
    String subject;
    String description;
    Workflow workflow;
    String channelType;
    SprinklrContactInfo contactInfo;
}
