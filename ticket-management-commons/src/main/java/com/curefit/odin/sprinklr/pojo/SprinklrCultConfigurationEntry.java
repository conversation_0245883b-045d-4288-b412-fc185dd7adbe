package com.curefit.odin.sprinklr.pojo;

import com.curefit.odin.enums.SprinklrConfigurationActionType;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;


@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SprinklrCultConfigurationEntry extends BaseOdinEntry {

    String l1;
    String subL1;
    String status;
    String l2;
    SprinklrConfigurationActionType actionType;
    SprinklrCultConfigurationAction actionValue;
    String image;
}
