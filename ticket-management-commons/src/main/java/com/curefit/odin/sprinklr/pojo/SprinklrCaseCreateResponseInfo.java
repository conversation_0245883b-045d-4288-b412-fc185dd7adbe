package com.curefit.odin.sprinklr.pojo;

import com.curefit.odin.sprinklr.pojo.webhook.ExternalCaseInfo;
import com.curefit.odin.sprinklr.pojo.webhook.SprinklrContact;
import com.curefit.odin.sprinklr.pojo.webhook.Workflow;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SprinklrCaseCreateResponseInfo {
    String id;
    Long caseNumber;
    String subject;
    String description;
    int version;
    ExternalCaseInfo externalCaseInfo;
    Workflow workflow;
    List<Object> channelCustomProperties;
    SprinklrContact contact;
    long createdTime;
    long modifiedTime;
    long latestMessageAssociatedTime;
    long totalProcessingClockTime;
    List<Object> allEngagedUsersList;
    int associatedFanMessageCount;
    int associatedBrandMessageCount;
    int associatedUserBrandMessageCount;
    boolean deleted;
}
