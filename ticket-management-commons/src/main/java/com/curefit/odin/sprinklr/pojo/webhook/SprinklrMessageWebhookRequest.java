package com.curefit.odin.sprinklr.pojo.webhook;

import com.curefit.odin.sprinklr.pojo.message.SprinklrMessage;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.Map;


@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SprinklrMessageWebhookRequest {
    String id;
    String type;
    SprinklrMessage payload;
    Long eventTime;
    Map<String, String> subscriptionDetails;
}
