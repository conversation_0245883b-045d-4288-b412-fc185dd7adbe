/**
 *
 */
package com.curefit.odin.integrations.pojo;

import com.curefit.odin.admin.pojo.CategoryEntry;
import com.curefit.odin.admin.pojo.SubCategoryEntry;
import com.curefit.odin.enums.Priority;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import com.curefit.odin.utils.pojo.TenantEntry;
import lombok.*;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 *
 */
@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@ToString
@NoArgsConstructor
public class FDTicketTypeCategoryMapEntry extends BaseOdinEntry {


    /**
     *
     */
    private static final long serialVersionUID = -3836477550416318292L;

    String fdCFType;

    String fdLevel1;

    String fdLevel2;

    String fdLevel3;

    TenantEntry tenant;

    Long tenantId;

    CategoryEntry category;

    Long categoryId;

    SubCategoryEntry subCategory;

    Long subCategoryId;

    Priority priority;

    String mappingType;
}
