/**
 * 
 */
package com.curefit.odin.integrations.pojo;

import com.curefit.cf.commons.pojo.BaseEntry;
import com.curefit.odin.admin.pojo.CategoryEntry;
import com.curefit.odin.admin.pojo.SubCategoryEntry;
import com.curefit.odin.utils.pojo.TenantEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 *
 */
@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@ToString
@NoArgsConstructor
public class OdinTypeFDTypeMapEntry extends BaseEntry{
  

  /**
   * 
   */
  private static final long serialVersionUID = -6560707611899028292L;

  Long tenantId;
  
  TenantEntry tenant;
  
  Long categoryId;
  
  CategoryEntry category;
  
  Long subCategoryId;
  
  SubCategoryEntry subCategory;
  
  String fdCFType;
  
  String fdLevel1;
  
  String fdLevel2;
  
  String fdLevel3;

}
