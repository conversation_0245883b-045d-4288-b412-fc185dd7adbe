package com.curefit.odin.user.pojo;

import com.curefit.odin.enums.EscalationLevel;
import com.curefit.odin.enums.Priority;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 */

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class TicketEscalationPayload implements Serializable {
  EscalationLevel escalationLevel;

  Long ticketId;

  Priority priority;
}
