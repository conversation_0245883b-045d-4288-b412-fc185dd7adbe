package com.curefit.odin.user.pojo;

import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class FieldValueEntry implements Serializable {

  /**
   * 
   */
  private static final long serialVersionUID = -3254780771810498029L;

  Long id;

  String value;
  
  /**
   * 
   */
  public FieldValueEntry() {
    // TODO Auto-generated constructor stub
  }
  

  public FieldValueEntry(Long id, String value) {
    this.id = id;
    this.value = value;
  }
}
