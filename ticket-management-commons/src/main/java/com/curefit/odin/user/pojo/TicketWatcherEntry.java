package com.curefit.odin.user.pojo;

import com.curefit.odin.admin.pojo.UserEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
public class TicketWatcherEntry extends BaseOdinEntry {

    /**
     *
     */
    private static final long serialVersionUID = -7447977462453016764L;

    @NotEmpty
    String userId;

    UserEntry user;

    @NotNull
    Long ticketId;

    public TicketWatcherEntry(Long ticketId, String userId) {
        this.userId = userId;
        this.ticketId = ticketId;
    }
}
