package com.curefit.odin.user.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;
import org.springframework.util.StringUtils;

import javax.validation.constraints.NotEmpty;

@Getter
@Setter
@ToString
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AttachmentEntry extends BaseOdinEntry {

    private static final long serialVersionUID = -6447147101033355947L;
    @NotEmpty
    String url;
    String signedUrl;
    String name;
    String description;
    Long ticketId;
    Long commentId;

    @JsonIgnore
    public boolean isURLAbsolute() {
        return !StringUtils.isEmpty(this.url) && this.url.startsWith("http");
    }
}
