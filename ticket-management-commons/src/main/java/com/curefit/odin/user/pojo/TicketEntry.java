package com.curefit.odin.user.pojo;


import com.curefit.odin.admin.pojo.UserEntry;
import com.curefit.odin.enums.*;
import com.curefit.odin.utils.pojo.LocationEntry;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class TicketEntry extends BaseOdinEntry {
    private static final long serialVersionUID = 8288117001835299930L;

    static final SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm");

    Status status;

    String description;

    Date dueDate;

    String title;

    Long parentTicketId;

    Long tenantId;

    String tenantName;

    Long categoryId;

    String categoryName;

    boolean isConfidential;

    boolean isHidden = false;

    boolean isUserAuthorized = true;

    Long subCategoryId;

    Long issueTemplateId;

    String subCategoryName;

    List<AttachmentEntry> attachments;

    Priority priority;

    Long assignedQueueId;

    String assignedQueueName;

    UserEntry assignedUser;

    List<UserEntry> assigneeQueueUsers;

    List<FieldDataEntry> fields;

    List<UserEntry> watchers;

    Set<String> labels;

    Date closedAt;

    UserEntry reporter;

    LocationEntry locationEntry;

    TicketSource source;

    String sourceRefId;

    TicketDest dest;

    String destRefId;

    List<String> defaultFields;

    Boolean isAssignedToMe;

    Date reOpenedAt;

    Set<String> approvers;

    Date slaReminderSentAt;

    String userId;

    Integer resolutionRating;

    String assetReferenceId;

    String centerServiceRefId;

    @JsonIgnore
    public Map<String, Object> getCSVContent() {
        formatter.setTimeZone(TimeZone.getTimeZone("IST"));
        Map<String, Object> map = new HashMap<>();
        map.put("ID", getId());
        map.put("Tenant", getTenantName());
        map.put("Category", getCategoryName());
        map.put("Sub Category", getSubCategoryName());
        map.put("Title", getTitle());
        map.put("Description", getDescription());
        map.put("Due On", getDueDate() != null ? formatter.format(getDueDate()) : "");
        map.put("Created On", getCreatedOn() != null ? formatter.format(getCreatedOn()) : "");
        map.put("Reported By", getCreatedBy());
        map.put("Assignee", (getAssigneeQueueUsers() != null && !getAssigneeQueueUsers().isEmpty()) ? getAssigneeQueueUsers().stream().map(UserEntry::getEmailId).collect(Collectors.joining(", ")) : "");
        map.put("Status", getStatus());
        map.put("Resolved At", getClosedAt() != null ? formatter.format(getClosedAt()) : "");
        map.put("Priority", getPriority());
        map.put("Labels", getLabels());
        map.put("Location", getLocationEntry() != null ? getLocationEntry().getCenterName() : "");
        map.put("City", getLocationEntry() != null ? getLocationEntry().getCityName() : "");
        map.put("Source", source);
        map.put("Destination", dest);
        map.put("Resolution Rating", getResolutionRating());
        map.put("SLA", getSLAStatus());
        map.put("Center Service Id", getLocationEntry() != null ? getLocationEntry().getCenterServiceRefId() : "");
        if (!CollectionUtils.isEmpty(fields)) {
            for (FieldDataEntry field : fields) {
                if (field.getKey().getDataType().name().equals(DataType.LIST.name())) {
                    map.put(field.getKey().getValue(), ((ArrayList<FieldValueEntry>) field.getValue())
                            .stream().map(fv -> fv.getValue()).collect(Collectors.toList()));
                } else {
                    map.put(field.getKey().getValue(), field.getValue());
                }
            }
        }
        return map;
    }

    @JsonIgnore
    private String getSLAStatus() {
        if (getDueDate() != null) {
            if (Status.closedTicketStatus().contains(getStatus())) {
                if (getClosedAt() != null) {
                    if (!getClosedAt().after(getDueDate())) return "Closed within SLA";
                    else return "SLA breached";
                }
            } else {
                Date today = new Date();
                long diffInMillis = DateUtils.truncate(getDueDate(), Calendar.DATE).getTime() - DateUtils.truncate(today, Calendar.DATE).getTime();
                long diffInDays = TimeUnit.DAYS.convert(diffInMillis, TimeUnit.MILLISECONDS);
                if (diffInDays == 0) return "Due Today";
                if (diffInDays == 1) return "Due Tomorrow";
                if (diffInDays > 0) return String.format("Due in %d days", diffInDays);
                return String.format("Overdue %d days", Math.abs(diffInDays));
            }
        }
        return null;
    }

    List<UserEntry> userMentions;

}
