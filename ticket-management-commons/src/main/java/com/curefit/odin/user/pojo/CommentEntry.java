package com.curefit.odin.user.pojo;

import com.curefit.odin.admin.pojo.UserEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CommentEntry extends BaseOdinEntry {
    /**
     *
     */
    private static final long serialVersionUID = 8381905788359901717L;

    UserEntry user;

    Long ticketId;

    String comment;

    Long parentCommentId;

    List<AttachmentEntry> attachments;

    List<CommentEntry> relatedComments;

    List<UserEntry> userMentions;

    Boolean isInternal;
}
