package com.curefit.odin.user.pojo;

import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class FieldDataEntry extends BaseOdinEntry{

  /**
   * 
   */
  private static final long serialVersionUID = -3995427787906731848L;

  Long ticketId;

  Long categoryId;

  FieldEntry key;

  Object value;

  public FieldDataEntry() {

    if (value instanceof List) {
      value = new ArrayList<FieldValueEntry>();
    }

  }
}
