package com.curefit.odin.user.pojo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;
import com.curefit.odin.enums.Priority;
import com.curefit.odin.enums.Status;
import com.curefit.odin.enums.TicketSource;
import com.curefit.odin.utils.pojo.LocationHierarchyNode;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.AccessLevel;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TicketFilterRequestEntry implements Serializable {

  /**
   *
   */
  private static final long serialVersionUID = 925445802766656650L;

  int offset;

  int limit = 100;

  String sortBy;

  String sortOrder;

  List<Long> tenantIds;

  List<Long> categoryIds;

  List<Long> subCategoryIds;

  List<Status> status;

  @JsonFormat(pattern = "yyyy-MM-dd")
  Date dueDateFrom;

  @JsonFormat(pattern = "yyyy-MM-dd")
  Date dueDateTo;

  List<String> cities;

  List<String> locations;

  List<LocationHierarchyNode> locationNodes;

  List<Long> parentTicketIds;

  List<Priority> priorities;

  List<Long> assignedQueueIds;

  List<String> assignedUserIds;

  List<String> watcherUserIds;

  List<String> createdBy;

  List<String> centerServiceRefIds;

  @JsonFormat(pattern = "yyyy-MM-dd")
  Date createdOnFrom;

  @JsonFormat(pattern = "yyyy-MM-dd")
  Date createdOnTo;

  Map<String, List<String>> fields;

  Boolean isAssignedToMe;

  Boolean isCreatedByMe;

  Boolean isWatchedByMe;

  TicketSource source;

  List<TicketSource> sources;

  List<String> sourceRefIds;

  List<String> userIds;

  Boolean requireDetails;

  Boolean requireSignedUrl;

  Long beforeTime;

}
