package com.curefit.odin.user.pojo;

import java.io.Serializable;
import com.curefit.odin.enums.DataType;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class FieldEntry implements Serializable {

	/**
	* 
	*/
	private static final long serialVersionUID = 7935681195721135572L;

	Long id;
	
	DataType dataType;

	String value;
}
