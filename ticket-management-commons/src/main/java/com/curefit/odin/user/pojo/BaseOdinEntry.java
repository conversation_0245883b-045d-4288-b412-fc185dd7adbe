package com.curefit.odin.user.pojo;

import com.curefit.cf.commons.pojo.BaseEntry;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

import java.util.Date;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BaseOdinEntry extends BaseEntry {

    private static final long serialVersionUID = 3512528716092205442L;

    Boolean active;

    String lastModifiedBy;

    Date deletedOn;

    public BaseOdinEntry() {
        active = true;
    }
}
