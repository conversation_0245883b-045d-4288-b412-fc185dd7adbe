package com.curefit.odin.user.pojo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class BulkUpdateTicketAssigneeRequest implements Serializable {
  TicketFilterRequestEntry filter;
  List<String> userIds;
}
