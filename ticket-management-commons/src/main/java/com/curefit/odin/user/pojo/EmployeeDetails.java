package com.curefit.odin.user.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonIgnoreProperties(ignoreUnknown = true)
public class EmployeeDetails {
    Long id;
    String externalId;
    String firstName;
    String middleName;
    String lastName;
    String mobileNumber;
    String email;
    String gender;
    String employeeUIN;
    Boolean active;
}
