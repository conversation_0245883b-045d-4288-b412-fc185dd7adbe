package com.curefit.odin.utils.pojo;

import com.curefit.odin.user.pojo.BaseOdinEntry;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */


@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
@AllArgsConstructor
public class DomainEntry extends BaseOdinEntry {

  String name;

  String url;

  List<TenantEntry> whitelistedTenantEntries;
}
