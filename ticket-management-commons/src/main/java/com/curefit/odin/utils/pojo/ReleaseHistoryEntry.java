package com.curefit.odin.utils.pojo;

import com.curefit.odin.user.pojo.BaseOdinEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ReleaseHistoryEntry extends BaseOdinEntry {

  Date releaseDate;

  Boolean isUpComing;

  Float releaseVersion;

  List<ReleaseChange> releaseChanges;

}
