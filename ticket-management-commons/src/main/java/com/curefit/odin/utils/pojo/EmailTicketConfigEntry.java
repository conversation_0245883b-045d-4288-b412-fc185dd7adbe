package com.curefit.odin.utils.pojo;

import com.curefit.odin.enums.Priority;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;


/**
 * <AUTHOR>
 */

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class EmailTicketConfigEntry extends BaseOdinEntry {

  String email;

  // TODO: need to check alternative option
  Long tenantId;

  Long categoryId;

  Long subCategoryId;

  Long locationId;

  Priority priority;

}
