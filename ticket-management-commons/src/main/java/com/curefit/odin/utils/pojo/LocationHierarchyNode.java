package com.curefit.odin.utils.pojo;

import com.curefit.odin.enums.LocationType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LocationHierarchyNode<T> implements Serializable {

  String code;

  @JsonProperty("title")
  String name;

  @JsonProperty("value")
  public String getValue(){
    return type + "_" + code;
  }

  public void setValue(String value) {
    this.code = value.split("_")[1];
  }

  LocationType type;

  T data;

  List<LocationHierarchyNode> children;

  LocationEntry locationEntry;

  public LocationHierarchyNode(String code, String name, LocationType type, List<LocationHierarchyNode> children) {
    this.code = code;
    this.name = name;
    this.type = type;
    this.children = children;
  }

  public LocationHierarchyNode(String code, String name, LocationType type) {
    this.code = code;
    this.name = name;
    this.type = type;
  }

  public LocationHierarchyNode(String code, String name, LocationType type, LocationEntry locationEntry) {
    this.code = code;
    this.name = name;
    this.type = type;
    this.locationEntry = locationEntry;
  }

  @Override
  public boolean equals(Object obj) {
    if (obj instanceof LocationHierarchyNode) {
      LocationHierarchyNode locationHierarchyNode = (LocationHierarchyNode) obj;
      if (this.getCode() == locationHierarchyNode.getCode() && this.getType() == locationHierarchyNode.getType() && this.getName() == locationHierarchyNode.getName()) {
        return true;
      }
      return this.getCode().equals(locationHierarchyNode.getCode()) && this.getName().equals(locationHierarchyNode.getName()) && this.getType().equals(locationHierarchyNode.getType());
    }
    return false;
  }

  @Override
  public int hashCode() {
    return Objects.hash(this.getCode(), this.getName(), this.getType());
  }

  @JsonIgnore
  public String getUniqueCode() {
    return this.getCode() + "__" + this.getType();
  }
}
