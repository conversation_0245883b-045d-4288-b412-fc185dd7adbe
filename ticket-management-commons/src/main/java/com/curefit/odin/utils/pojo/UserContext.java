package com.curefit.odin.utils.pojo;

import com.curefit.odin.enums.UserRole;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */

@Slf4j
@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UserContext {

  @JsonProperty("roleName")
  UserRole role;

  String context;
}
