package com.curefit.odin.utils.pojo;

import com.curefit.odin.admin.pojo.DataSourceValueEntry;
import com.curefit.odin.enums.LocationType;
import lombok.*;
import lombok.experimental.FieldDefaults;

@Builder
@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
@AllArgsConstructor
public class LocationEntry extends DataSourceValueEntry {

  /**
   * 
   */
  private static final long serialVersionUID = 5715013708890943463L;

  Long tenantId;
  String centerId;
  String centerName;
  String clusterId;
  String clusterName;
  String cityId;
  String cityName;
  String countryId;
  String countryName;
  String neoCode;
  Double latitude;
  Double longitude;
  String address;
  LocationType type;
  String centerServiceRefId;

  public LocationEntry(String value) {
    this.value = value;
  }

  public LocationEntry(String value, String referenceId) {
    this.value = value;
    this.referenceId = referenceId;
  }

}
