package com.curefit.odin.utils.pojo;

import java.util.Map;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class LocationSourceEntry extends BaseOdinEntry {
  
  /**
   * 
   */
  private static final long serialVersionUID = -4485174800969922574L;
  
  Long tenantId;
  
  String type;
  
  String name;
  
  String url;
  
  String rootPath;
  
  Map<String, String> headersObject;
  
  Map<String, String> fieldMapping;
  
  int syncDurationInHours;
  
}
