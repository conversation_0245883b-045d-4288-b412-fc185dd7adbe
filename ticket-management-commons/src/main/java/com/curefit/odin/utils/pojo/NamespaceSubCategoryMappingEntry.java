package com.curefit.odin.utils.pojo;

import com.curefit.odin.admin.pojo.SubCategoryEntry;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class NamespaceSubCategoryMappingEntry extends BaseOdinEntry {

    /**
     *
     */
    private static final long serialVersionUID = 2124345903738048516L;

    String name;

    Long subCategoryId;
}
