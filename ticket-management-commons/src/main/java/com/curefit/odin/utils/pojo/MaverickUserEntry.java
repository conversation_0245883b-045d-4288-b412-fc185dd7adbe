package com.curefit.odin.utils.pojo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MaverickUserEntry implements Serializable  {
  
  /**
   * 
   */
  private static final long serialVersionUID = 6749992014929350544L;
  
  String esId;
  Double score;
  String fullName;
  String designation;
  String mobileNumber;
  String email;
  String tenant;
  Boolean isDl;
  Integer memberCount;


}
