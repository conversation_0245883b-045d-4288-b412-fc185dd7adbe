package com.curefit.odin.utils.pojo;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class EmployeeDetails {

  private static final String BLANK = " ";

  Employee employee;

  String reportingManager;

  JobDetails jobDetails;

  HierarchyDetails hierarchyDetails;

  @ToString
  @Getter
  public static class Employee {
    boolean active;
    String firstName;
    String middleName;
    String lastName;
    String mobileNumber;
    String employeeUIN;
    String externalId;
    String email;
    String gender;
    String photoUrl;

    public String getName() {
      lastName = StringUtils.isBlank(lastName) ? "" : lastName;
      return firstName  + (!StringUtils.isBlank(middleName) ? BLANK + middleName + BLANK : BLANK) + lastName;
    }
  }

  @Getter
  public static class JobDetails {
    Employee employee;
    Double designationId;
    String workLocation;
    String reportingManagerId;
    String reportingManagerUin;
    String unitHead;
    String category;
  }

  @Getter
  public static class HierarchyDetails {
    String entity;
    String jobFamily;
    String division;
    String subDivision;
    String designationName;
    boolean published;
  }
}
