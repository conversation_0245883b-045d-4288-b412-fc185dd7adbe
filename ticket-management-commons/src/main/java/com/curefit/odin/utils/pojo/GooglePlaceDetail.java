package com.curefit.odin.utils.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 */

@ToString
@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class GooglePlaceDetail {

  String status;

  List<Result> results;

  @Getter
  @Setter
  @FieldDefaults(level = AccessLevel.PRIVATE)
  public static class Result {

    @JsonProperty("formatted_address")
    String formattedAddress;

    @JsonProperty("place_id")
    String placeId;

    @JsonProperty("address_components")
    List<AddressComponent> addressComponents;
  }

  @Getter
  @Setter
  @FieldDefaults(level = AccessLevel.PRIVATE)
  public static class AddressComponent {

    @JsonProperty("short_name")
    String shortName;

    @JsonProperty("long_namne")
    String longName;

    String[] types;
  }
}