package com.curefit.odin.utils.pojo;

import java.io.Serializable;
import java.util.Date;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 */

@Setter
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UserInteractionEvent implements Serializable {

  String userId;

  String phone;

  String status;

  String notificationId;

  Date lastInteractionTs;
}

/*
      {
      "userId": "+918079045008",
      "phone": "+918079045008",
      "notificationId": "738199c3-cb8b-4e4c-aede-f3dc3a9d79ef",
      "lastInteractionTs": "2020-01-03T05:50:16.000Z"
    } */
