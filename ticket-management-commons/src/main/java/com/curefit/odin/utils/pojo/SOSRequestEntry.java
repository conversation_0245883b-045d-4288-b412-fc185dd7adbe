package com.curefit.odin.utils.pojo;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SOSRequestEntry implements Serializable {

  String employeeId;

  EmployeeDetails employeeDetails = new EmployeeDetails();

  Double latitude;

  Double longitude;

  Date lastLocationFetchTime;

  String tenantCode;

  @Getter
  @Setter
  public static class EmployeeDetails implements Serializable {
    String name;

    String email;

    String phone;

    String countryCode;

    String reportingManager;

    String reportingManagerPhone;

    String workLocation;

    String designationName;

    public String getCountryCode() {
      if (countryCode == null || countryCode.equals("+91") || countryCode.equals("91")) {
        return "";
      }
      return countryCode;
    }
  }
}
