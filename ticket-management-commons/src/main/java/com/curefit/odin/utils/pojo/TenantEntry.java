package com.curefit.odin.utils.pojo;

import javax.validation.constraints.NotNull;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(Include.NON_NULL)
public class TenantEntry extends BaseOdinEntry {

  /**
  * 
  */
  private static final long serialVersionUID = 726225501054898723L;

  @NotNull
  String name;

  String neoCode;

  @Override
  public boolean equals(Object obj) {
    if (!(obj instanceof TenantEntry) || this.getId() == null) {
      return false;
    }
    return this.getId().equals(((TenantEntry)obj).getId());
  }

  @Override
  public int hashCode() {
    return this.getId().hashCode();
  }
}
