service:
  expose:
  - 13000
deployment:
  tolerations:
  - key: graviton
    operator: Equal
    value: 'true'
    effect: NoSchedule
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - key: kubernetes.io/arch
          operator: In
          values:
          - arm64
  labels:
    billing: platforms
    sub-billing: ticketing-system
  probePort: 13000
  probePath: /actuator/health
  podAnnotations:
    iam.amazonaws.com/role: arn:aws:iam::035243212545:role/k8s-ticketing-system-prod
  resources:
    limits:
      cpu: 2
      memory: 4Gi
    requests:
      cpu: 2
      memory: 2Gi
  env:
  - name: LOG_LEVEL
    value: DEBUG
  - name: WORKER_THREADS
    value: 80
  - name: ENVIRONMENT
    value: prod
  - name: DB_URL
    value: *****************************************************************************************************
datadog:
  enabled: true
  custom_metrics: true
istio:
  internal:
    hosts:
    - ticketing-system.production.cure.fit.internal
  external:
    hosts:
    - ticketing-system.curefit.co
    match:
    - prefix: /ticket/external
    - prefix: /comment/external
    - exact: /google/oauth/callback
  vpn:
    hosts:
    - ticketing-system-vpn.curefit.co
    match:
    - prefix: /ticket/filter
    - prefix: /comment
    - prefix: /ticket
    - prefix: /attachment
externalSecrets:
  enabled: 'true'
scaling:
  scaleUpAtCPU: 1.5
  targetCPUUtilPercentage: 200
  minReplicas: 2
  maxReplicas: 5
pager:
  provider: opsgenie
  service-name: ticketing-system
bugtracker:
  provider: rollbar
  service-name: ticketing-system
support:
  slack-channel: supply-on-call
  mailing-list: <EMAIL>
apm:
  provider: datadog
  service-name: ticketing-system
pod-id: supply
tags:
  billing: supply
