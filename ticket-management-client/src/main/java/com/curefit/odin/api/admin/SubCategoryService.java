package com.curefit.odin.api.admin;

import com.curefit.common.rest.client.CommonHttpHelper;
import com.curefit.odin.admin.pojo.SubCategoryEntry;
import com.curefit.odin.commons.CommonUtils;
import com.curefit.odin.commons.OdinConfig;
import com.curefit.odin.commons.Constants;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.curefit.odin.commons.Constants.REQUIRED_PARAMETER_EXCEPTION;

/**
 * <AUTHOR>
 */

@Component("subCategoryServiceClient")
@Service
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SubCategoryService {

  @Autowired
  CommonHttpHelper commonHttpHelper;

  @Autowired
  OdinConfig odinConfig;

  public SubCategoryEntry getSubCategoryById(String userId, String namespace, Long subCategoryId) {
    final Map<String, Object> uriVariables = new HashMap<String, Object>() {{
      put("id", subCategoryId);
    }};
    String url = odinConfig.getBaseUri()
            .path(Constants.SUB_CATEGORY_BY_ID_PATH)
            .buildAndExpand(uriVariables)
            .toUriString();
    return commonHttpHelper.request(url, HttpMethod.GET, null, CommonUtils.getHeaders(userId, namespace), SubCategoryEntry.class).getBody();
  }

  public SubCategoryEntry addSubCategory(SubCategoryEntry subCategoryEntry, String modifiedBy) {
    if (subCategoryEntry == null) {
      throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, REQUIRED_PARAMETER_EXCEPTION);
    }
    String url = odinConfig.getBaseUri().path(Constants.SUB_CATEGORY_PATH)
            .build().toUriString();

    return commonHttpHelper.request(url, HttpMethod.POST, subCategoryEntry, CommonUtils.getHeaders(modifiedBy), SubCategoryEntry.class).getBody();
  }

  public List<SubCategoryEntry> fetchAllByCategoryId(Long categoryId, String user) {
    if (categoryId == null) {
      throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, REQUIRED_PARAMETER_EXCEPTION);
    }
    String url = odinConfig.getBaseUri().path(Constants.SUB_CATEGORY_BY_TENANT_ID_PATH)
            .queryParam("categoryId", categoryId)
            .build()
            .toUriString();
    return commonHttpHelper.request(url, HttpMethod.GET, null, CommonUtils.getHeaders(user), new TypeReference<List<SubCategoryEntry>>() {
    }).getBody();
  }
}
