package com.curefit.odin.api.admin;

import com.curefit.common.rest.client.CommonHttpHelper;
import com.curefit.odin.admin.pojo.CategoryEntry;
import com.curefit.odin.commons.CommonUtils;
import com.curefit.odin.commons.OdinConfig;
import com.curefit.odin.commons.Constants;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.curefit.odin.commons.Constants.REQUIRED_PARAMETER_EXCEPTION;

/**
 * <AUTHOR>
 */

@Component("categoryServiceClient")
@Service
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CategoryService {

    @Autowired
    CommonHttpHelper commonHttpHelper;

    @Autowired
    OdinConfig odinConfig;

    public CategoryEntry getCategoryById(String userId, String namespace, Long categoryId) {
        final Map<String, Object> uriVariables = new HashMap<String, Object>() {{
            put("id", categoryId);
        }};
        String url = odinConfig.getBaseUri()
                .path(Constants.CATEGORY_BY_ID_PATH)
                .buildAndExpand(uriVariables)
                .toUriString();
        return commonHttpHelper.request(url, HttpMethod.GET, null, CommonUtils.getHeaders(userId, namespace), CategoryEntry.class).getBody();
    }

    public CategoryEntry addCategory(CategoryEntry categoryEntry, String modifiedBy) {
        if (categoryEntry == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, REQUIRED_PARAMETER_EXCEPTION);
        }
        String url = odinConfig.getBaseUri().path(Constants.CATEGORY_PATH)
                .build().toUriString();

        return commonHttpHelper.request(url, HttpMethod.POST, categoryEntry, CommonUtils.getHeaders(modifiedBy), CategoryEntry.class).getBody();
    }

    public List<CategoryEntry> fetchAllByTenantId(Long tenantId, String user) {
        if (tenantId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, REQUIRED_PARAMETER_EXCEPTION);
        }
        String url = odinConfig.getBaseUri().path(Constants.CATEGORY_BY_TENANT_ID_PATH)
                .queryParam("tenantId", tenantId)
                .build()
                .toUriString();
        return commonHttpHelper.request(url, HttpMethod.GET, null, CommonUtils.getHeaders(user), new TypeReference<List<CategoryEntry>>() {
        }).getBody();
    }
}
