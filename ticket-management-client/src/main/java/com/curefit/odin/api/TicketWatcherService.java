package com.curefit.odin.api;

import com.curefit.common.rest.client.CommonHttpHelper;
import com.curefit.odin.commons.CommonUtils;
import com.curefit.odin.commons.OdinConfig;
import com.curefit.odin.commons.Constants;
import com.curefit.odin.user.pojo.TicketWatcherEntry;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.curefit.odin.commons.Constants.REQUIRED_PARAMETER_EXCEPTION;

/**
 * <AUTHOR>
 */

@Component("ticketWatcherServiceClient")
@Service
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TicketWatcherService {

  @Autowired
  CommonHttpHelper commonHttpHelper;

  @Autowired
  OdinConfig odinConfig;

  public TicketWatcherEntry addWatcher(TicketWatcherEntry ticketWatcherEntry, String modifiedBy) {
    if (ticketWatcherEntry == null) {
      throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, REQUIRED_PARAMETER_EXCEPTION);
    }
    String url = odinConfig.getBaseUri().path(Constants.TICKET_WATCHER_PATH)
            .build().toUriString();

    return commonHttpHelper.request(url, HttpMethod.POST, ticketWatcherEntry, CommonUtils.getHeaders(modifiedBy), TicketWatcherEntry.class).getBody();
  }

  public TicketWatcherEntry deleteWatcher(Long ticketWatcherId, String modifiedBy) {
    if (ticketWatcherId == null) {
      throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, REQUIRED_PARAMETER_EXCEPTION);
    }

    final Map<String, Object> uriVariables = new HashMap<String, Object>() {{
      put("id", ticketWatcherId);
    }};
    String url = odinConfig.getBaseUri().path(Constants.TICKET_WATCHER_DELETE_PATH)
            .buildAndExpand(uriVariables).toUriString();

    return commonHttpHelper.request(url, HttpMethod.DELETE, null, CommonUtils.getHeaders(modifiedBy), TicketWatcherEntry.class).getBody();
  }

  public List<TicketWatcherEntry> fetchAllWatchersByTicketId(Long ticketId, String user) {
    if (ticketId == null) {
      throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, REQUIRED_PARAMETER_EXCEPTION);
    }
    String url = odinConfig.getBaseUri().path(Constants.TICKET_WATCHERS_BY_TICKET_ID_PATH)
            .queryParam("ticketId", ticketId)
            .build()
            .toUriString();
    return commonHttpHelper.request(url, HttpMethod.GET, null, CommonUtils.getHeaders(user), new TypeReference<List<TicketWatcherEntry>>() {
    }).getBody();
  }
}
