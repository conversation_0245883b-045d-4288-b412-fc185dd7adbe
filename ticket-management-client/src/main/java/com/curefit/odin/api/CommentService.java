package com.curefit.odin.api;

import com.curefit.common.rest.client.CommonHttpHelper;
import com.curefit.odin.commons.CommonUtils;
import com.curefit.odin.commons.OdinConfig;
import com.curefit.odin.commons.Constants;
import com.curefit.odin.user.pojo.CommentEntry;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.curefit.odin.commons.Constants.REQUIRED_PARAMETER_EXCEPTION;

/**
 * <AUTHOR>
 */

@Component("commentServiceClient")
@Service
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CommentService {

  @Autowired
  CommonHttpHelper commonHttpHelper;

  @Autowired
  OdinConfig odinConfig;

  public CommentEntry addComment(CommentEntry commentEntry) {
    if (commentEntry == null || commentEntry.getUser() == null || commentEntry.getUser().getEmailId() == null) {
      throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, REQUIRED_PARAMETER_EXCEPTION);
    }
    String url = odinConfig.getBaseUri().path(Constants.COMMENT_PATH)
            .build().toUriString();

    return commonHttpHelper.request(url, HttpMethod.POST, commentEntry, CommonUtils.getHeaders(commentEntry.getUser().getEmailId()), CommentEntry.class).getBody();
  }

  public CommentEntry updateComment(Long commentId, CommentEntry commentEntry, String modifiedBy) {
    if (commentId == null || commentEntry == null) {
      throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, REQUIRED_PARAMETER_EXCEPTION);
    }

    final Map<String, Object> uriVariables = new HashMap<String, Object>() {{
      put("id", commentId);
    }};

    String url = odinConfig.getBaseUri().path(Constants.COMMENT_UPDATE_PATH)
            .buildAndExpand(uriVariables).toUriString();

    return commonHttpHelper.request(url, HttpMethod.PATCH, commentEntry, CommonUtils.getHeaders(modifiedBy), CommentEntry.class).getBody();
  }

  public CommentEntry deleteComment(Long commentId, String modifiedBy) {
    if (commentId == null) {
      throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, REQUIRED_PARAMETER_EXCEPTION);
    }

    final Map<String, Object> uriVariables = new HashMap<String, Object>() {{
      put("id", commentId);
    }};
    String url = odinConfig.getBaseUri().path(Constants.COMMENT_DELETE_PATH)
            .buildAndExpand(uriVariables).toUriString();

    return commonHttpHelper.request(url, HttpMethod.DELETE, null, CommonUtils.getHeaders(modifiedBy), CommentEntry.class).getBody();
  }

  public CommentEntry fetchComment(Long commentId, String user) {
    if (commentId == null) {
      throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, REQUIRED_PARAMETER_EXCEPTION);
    }
    final Map<String, Object> uriVariables = new HashMap<String, Object>() {{
      put("id", commentId);
    }};
    String url = odinConfig.getBaseUri().path(Constants.COMMENT_FETCH_PATH)
            .buildAndExpand(uriVariables)
            .toUriString();
    return commonHttpHelper.request(url, HttpMethod.GET, null, CommonUtils.getHeaders(user), CommentEntry.class).getBody();
  }

  public List<CommentEntry> fetchAllCommentsByTicketId(Long ticketId, String user) {
    return fetchAllCommentsByTicketId(user, null, ticketId, true, true);
  }

  public List<CommentEntry> fetchAllCommentsByTicketId(String user, String namespace, Long ticketId, Boolean includeInternal, Boolean includeExternal) {
    if (ticketId == null) {
      throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, REQUIRED_PARAMETER_EXCEPTION);
    }
    String url = odinConfig.getBaseUri()
            .path(Constants.COMMENTS_BY_TICKET_ID_PATH)
            .queryParam("ticketId", ticketId)
            .queryParam("includeInternal", includeInternal)
            .queryParam("includeExternal", includeExternal)
            .build()
            .toUriString();
    return commonHttpHelper.request(url, HttpMethod.GET, null, CommonUtils.getHeaders(user, namespace), new TypeReference<List<CommentEntry>>() {
    }).getBody();
  }
}
