package com.curefit.odin.api;

import com.curefit.common.rest.client.CommonHttpHelper;
import com.curefit.odin.commons.CommonUtils;
import com.curefit.odin.commons.OdinConfig;
import com.curefit.odin.commons.Constants;
import com.curefit.odin.utils.pojo.LocationEntry;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import static com.curefit.odin.commons.Constants.REQUIRED_PARAMETER_EXCEPTION;

/**
 * <AUTHOR>
 */

@Component("locationServiceClient")
@Service
@FieldDefaults(level = AccessLevel.PRIVATE)
public class LocationService {

  @Autowired
  CommonHttpHelper commonHttpHelper;

  @Autowired
  OdinConfig odinConfig;

  public LocationEntry fetchLocation(String centerId, Long tenantId, String user) {
    if (centerId == null || tenantId == null) {
      throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, REQUIRED_PARAMETER_EXCEPTION);
    }

    String url = odinConfig.getBaseUri().path(Constants.LOCATION_BY_REFERENCE_ID_PATH)
            .queryParam("tenantId", tenantId)
            .queryParam("referenceId", centerId)
            .build()
            .toUriString();

    return commonHttpHelper.request(url, HttpMethod.GET, null, CommonUtils.getHeaders(user), LocationEntry.class).getBody();
  }
}
