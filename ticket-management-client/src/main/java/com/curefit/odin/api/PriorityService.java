package com.curefit.odin.api;

import com.curefit.common.rest.client.CommonHttpHelper;
import com.curefit.odin.admin.pojo.PriorityFilterRequestEntry;
import com.curefit.odin.admin.pojo.PriorityMappingEntry;
import com.curefit.odin.commons.CommonUtils;
import com.curefit.odin.commons.OdinConfig;
import com.curefit.odin.commons.Constants;
import com.curefit.odin.enums.Priority;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */

@Component("priorityServiceClient")
@Service
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PriorityService {

    @Autowired
    CommonHttpHelper commonHttpHelper;

    @Autowired
    OdinConfig odinConfig;

    public List<Priority> fetchPriorities(String user) {
        String url = odinConfig.getBaseUri().path(Constants.PRIORITIES_PATH).toUriString();
        return commonHttpHelper.request(url, HttpMethod.GET, null, CommonUtils.getHeaders(user), new TypeReference<List<Priority>>() {
        }).getBody();
    }

    public PriorityMappingEntry fetchPriorityMappingById(Long id, String user) {
        String url = odinConfig.getBaseUri().path("/priority/" + id.toString()).toUriString();
        return commonHttpHelper.request(url, HttpMethod.GET, null, CommonUtils.getHeaders(user), new TypeReference<PriorityMappingEntry>() {
        }).getBody();
    }

    public List<PriorityMappingEntry> filter(PriorityFilterRequestEntry filterRequest, String user) {
        String url = odinConfig.getBaseUri().path("/priority/filter").toUriString();
        return commonHttpHelper.request(url, HttpMethod.GET, filterRequest, CommonUtils.getHeaders(user), new TypeReference<List<PriorityMappingEntry>>() {
        }).getBody();
    }

    public List<PriorityMappingEntry> filterActive(PriorityFilterRequestEntry filterRequest, String user) {
        String url = odinConfig.getBaseUri().path("/priority/filter/active").toUriString();
        return commonHttpHelper.request(url, HttpMethod.GET, filterRequest, CommonUtils.getHeaders(user), new TypeReference<List<PriorityMappingEntry>>() {
        }).getBody();
    }

}
