package com.curefit.odin.api;

import com.curefit.cf.commons.pojo.PagedResultEntry;
import com.curefit.common.rest.client.CommonHttpHelper;
import com.curefit.odin.commons.CommonUtils;
import com.curefit.odin.commons.Constants;
import com.curefit.odin.commons.OdinConfig;
import com.curefit.odin.user.pojo.TicketEntry;
import com.curefit.odin.user.pojo.TicketFilterRequestEntry;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

import static com.curefit.odin.commons.Constants.REQUIRED_PARAMETER_EXCEPTION;

/**
 * <AUTHOR>
 */

@Component("ticketServiceClient")
@Service
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TicketService {

    @Autowired
    CommonHttpHelper commonHttpHelper;

    @Autowired
    OdinConfig odinConfig;

    public TicketEntry createTicket(TicketEntry ticketEntry) {
        if (ticketEntry == null || ticketEntry.getReporter() == null || ticketEntry.getReporter().getEmailId() == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, REQUIRED_PARAMETER_EXCEPTION);
        }
        String url = odinConfig.getBaseUri().path(Constants.TICKET_PATH)
                .build().toUriString();

        return commonHttpHelper.request(url, HttpMethod.POST, ticketEntry, CommonUtils.getHeaders(ticketEntry.getReporter().getEmailId()), TicketEntry.class).getBody();
    }

    public TicketEntry createTicketV2(TicketEntry ticketEntry) {
        if (ticketEntry == null ) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, REQUIRED_PARAMETER_EXCEPTION);
        }
        String url = odinConfig.getBaseUri().path(Constants.TICKET_CREATE_V2_PATH)
                .build().toUriString();

        return commonHttpHelper.request(url, HttpMethod.POST, ticketEntry, CommonUtils.getHeaders(ticketEntry.getReporter().getEmailId()), TicketEntry.class).getBody();
    }

    public TicketEntry updateTicket(Long ticketId, TicketEntry ticketEntry, String modifiedBy) {
        if (ticketId == null || ticketEntry == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, REQUIRED_PARAMETER_EXCEPTION);
        }
        final Map<String, Object> uriVariables = new HashMap<String, Object>() {{
            put("id", ticketId);
        }};

        String url = odinConfig.getBaseUri().path(Constants.TICKET_UPDATE_PATH)
                .buildAndExpand(uriVariables).toUriString();

        ResponseEntity<TicketEntry> responseEntity = commonHttpHelper.request(url, HttpMethod.PATCH, ticketEntry, CommonUtils.getHeaders(modifiedBy), TicketEntry.class);
        return responseEntity.getBody();
    }

    public TicketEntry updateTicketCategory(TicketEntry ticketEntry, String modifiedBy) {
        if (ticketEntry == null || ticketEntry.getId() == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, REQUIRED_PARAMETER_EXCEPTION);
        }

        String url = odinConfig.getBaseUri().path(Constants.TICKET_CATEGORY_UPDATE_PATH).toUriString();

        ResponseEntity<TicketEntry> responseEntity = commonHttpHelper.request(url, HttpMethod.PATCH, ticketEntry, CommonUtils.getHeaders(modifiedBy), TicketEntry.class);
        return responseEntity.getBody();
    }

    public TicketEntry updateAssignee(Long ticketId, String userId, String modifiedBy) {
        if (ticketId == null || StringUtils.isBlank(userId)) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, REQUIRED_PARAMETER_EXCEPTION);
        }

        final Map<String, Object> uriVariables = new HashMap<String, Object>() {{
            put("id", ticketId);
        }};

        String url = odinConfig.getBaseUri().path(Constants.TICKET_ASSIGNEE_PATH)
                .queryParam("userId", userId)
                .buildAndExpand(uriVariables).toUriString();

        return commonHttpHelper.request(url, HttpMethod.PATCH, null, CommonUtils.getHeaders(modifiedBy), TicketEntry.class).getBody();
    }

    public TicketEntry updateStatus(Long ticketId, String status, String modifiedBy) throws UnsupportedEncodingException {
        if (ticketId == null || StringUtils.isBlank(status)) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, REQUIRED_PARAMETER_EXCEPTION);
        }

        final Map<String, Object> uriVariables = new HashMap<String, Object>() {{
            put("id", ticketId);
        }};

        status = URLEncoder.encode(status, "UTF-8");
        String url = odinConfig.getBaseUri().path(Constants.TICKET_STATUS_PATH)
                .queryParam("newStatus", status)
                .buildAndExpand(uriVariables).toUriString();
        ResponseEntity<TicketEntry> responseEntity = commonHttpHelper.request(url, HttpMethod.PATCH, null, CommonUtils.getHeaders(modifiedBy), TicketEntry.class);
        return responseEntity.getBody();
    }

    public TicketEntry addLabel(Long ticketId, String label, String modifiedBy) {
        if (ticketId == null || StringUtils.isBlank(label)) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, REQUIRED_PARAMETER_EXCEPTION);
        }

        final Map<String, Object> uriVariables = new HashMap<String, Object>() {{
            put("id", ticketId);
        }};

        String url = odinConfig.getBaseUri().path(Constants.TICKET_LABEL_PATH)
                .queryParam("label", label)
                .buildAndExpand(uriVariables).toUriString();

        ResponseEntity<TicketEntry> responseEntity = commonHttpHelper.request(url, HttpMethod.PATCH, null, CommonUtils.getHeaders(modifiedBy), TicketEntry.class);
        return responseEntity.getBody();
    }

    public TicketEntry deleteLabel(Long ticketId, String label, String modifiedBy) {
        if (ticketId == null || StringUtils.isBlank(label)) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, REQUIRED_PARAMETER_EXCEPTION);
        }

        final Map<String, Object> uriVariables = new HashMap<String, Object>() {{
            put("id", ticketId);
        }};

        String url = odinConfig.getBaseUri().path(Constants.TICKET_LABEL_PATH)
                .queryParam("label", label)
                .buildAndExpand(uriVariables).toUriString();

        ResponseEntity<TicketEntry> responseEntity = commonHttpHelper.request(url, HttpMethod.DELETE, null, CommonUtils.getHeaders(modifiedBy), TicketEntry.class);
        return responseEntity.getBody();
    }

    public TicketEntry fetchDetail(Long ticketId, String user) {
        if (ticketId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, REQUIRED_PARAMETER_EXCEPTION);
        }
        final Map<String, Object> uriVariables = new HashMap<String, Object>() {{
            put("id", ticketId);
        }};

        String url = odinConfig.getBaseUri().path(Constants.TICKET_DETAIL_PATH)
                .buildAndExpand(uriVariables).toUriString();
        return commonHttpHelper.request(url, HttpMethod.GET, null, CommonUtils.getHeaders(user), TicketEntry.class).getBody();
    }

    public PagedResultEntry<Long, TicketEntry> filterTickets(String user, String namespace, Long tenantId, TicketFilterRequestEntry filterRequest) {
        String url = odinConfig.getBaseUri()
                .path(Constants.TICKET_FILTER_PATH)
                .build()
                .toUriString();

        return commonHttpHelper.request(url, HttpMethod.POST, filterRequest, CommonUtils.getHeaders(user, namespace, tenantId), new TypeReference<PagedResultEntry<Long, TicketEntry>>() {
        }).getBody();
    }

    public PagedResultEntry<Long, TicketEntry> filterTicketsCreatedByUserOrderByLastModifiedOnDesc(String user, String namespace, Long tenantId, int offset, int limit) {
        TicketFilterRequestEntry filterRequest = new TicketFilterRequestEntry();
        filterRequest.setSortBy("lastModifiedOn");
        filterRequest.setSortOrder("DESC");
        filterRequest.setOffset(offset);
        filterRequest.setLimit(limit);
        filterRequest.setIsCreatedByMe(true);
        return filterTickets(user, namespace, tenantId, filterRequest);
    }
}
