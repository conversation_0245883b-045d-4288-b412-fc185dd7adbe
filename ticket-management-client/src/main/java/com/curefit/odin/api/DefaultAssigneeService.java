package com.curefit.odin.api;

import com.curefit.common.rest.client.CommonHttpHelper;
import com.curefit.odin.admin.pojo.DefaultAssigneeEntry;
import com.curefit.odin.commons.CommonUtils;
import com.curefit.odin.commons.OdinConfig;
import com.curefit.odin.commons.Constants;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import static com.curefit.odin.commons.Constants.REQUIRED_PARAMETER_EXCEPTION;

/**
 * <AUTHOR>
 */

@Component("defaultAssigneeServiceClient")
@Service
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DefaultAssigneeService {

  @Autowired
  CommonHttpHelper commonHttpHelper;

  @Autowired
  OdinConfig odinConfig;

  public DefaultAssigneeEntry fetchDefaultAssignee(Long locationId, Long categoryId, Long subCategoryId, String user) {
    if (categoryId == null) {
      throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, REQUIRED_PARAMETER_EXCEPTION);
    }

    String url = odinConfig.getBaseUri().path(Constants.DEFAULT_ASSIGNEE_PATH)
            .queryParam("locationId", locationId)
            .queryParam("categoryId", categoryId)
            .queryParam("subCategoryId", subCategoryId)
            .toUriString();
    return commonHttpHelper.request(url, HttpMethod.GET, null, CommonUtils.getHeaders(user), DefaultAssigneeEntry.class).getBody();
  }
}
