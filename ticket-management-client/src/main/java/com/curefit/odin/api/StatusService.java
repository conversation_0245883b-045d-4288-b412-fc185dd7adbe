package com.curefit.odin.api;

import com.curefit.common.rest.client.CommonHttpHelper;
import com.curefit.odin.commons.CommonUtils;
import com.curefit.odin.commons.OdinConfig;
import com.curefit.odin.commons.Constants;
import com.curefit.odin.enums.Status;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */

@Component("statusServiceClient")
@Service
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StatusService {

  @Autowired
  CommonHttpHelper commonHttpHelper;

  @Autowired
  OdinConfig odinConfig;

  public List<Status> fetchAllStatus(String user) {
    String url = odinConfig.getBaseUri().path(Constants.STATUSES_PATH).toUriString();
    return commonHttpHelper.request(url, HttpMethod.GET, null, CommonUtils.getHeaders(user), new TypeReference<List<Status>>() {
    }).getBody();
  }


}
