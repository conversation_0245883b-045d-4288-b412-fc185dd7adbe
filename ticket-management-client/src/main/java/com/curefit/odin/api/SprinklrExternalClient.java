package com.curefit.odin.api;


import com.curefit.common.rest.client.CommonHttpHelper;
import com.curefit.odin.sprinklr.pojo.SprinklrCaseCreatePayload;
import com.curefit.odin.sprinklr.pojo.SprinklrCaseCreateResponse;
import com.curefit.odin.sprinklr.pojo.SprinklrTicketUpdateRequest;
import com.curefit.odin.sprinklr.pojo.message.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component("sprinklrExternalClient")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SprinklrExternalClient {

    private static final String SPRINKLR_BASE_URL = "https://api3.sprinklr.com/prod4";
    private static final String SPRINKLR_AUTH_TOKEN = "jgtYidnBzBLQ4JZNPKcgSN45gpbk0DK/hnUcIaIilk00MmQ2NzY0Yy1hMjQ4LTM1NjMtODFkOC00OTdmNGQ0YzJiY2I=";

    final CommonHttpHelper commonHttpHelper;

    private UriComponentsBuilder getUri() {
        return UriComponentsBuilder.fromHttpUrl(SPRINKLR_BASE_URL);
    }

    private Map<String, String> getHeadersMap() {
        return new HashMap<>() {{
            put("Content-Type", "application/json");
            put("Accept", "application/json");
            put("Authorization", "Bearer " + SPRINKLR_AUTH_TOKEN);
        }};
    }

    public Object updateSprinklrCase(SprinklrTicketUpdateRequest sprinklrTicketUpdateRequest) {
        String url = getUri().path("/api/v2/case")
                .build().toUriString();
        ResponseEntity<Object> httpResponse = commonHttpHelper.request(url, HttpMethod.PUT, sprinklrTicketUpdateRequest, getHeadersMap(), new TypeReference<>() {
        });
        log.info("updateSprinklrCase::client response: {}", httpResponse);
        log.info("updateSprinklrCase::client status code: {}", httpResponse.getStatusCode());
        return httpResponse.getBody();
    }

    public Object getSprinklrCase(String sprinklrCaseId) {
        String url = getUri().path("/api/v2/case/" + sprinklrCaseId)
                .build().toUriString();
        ResponseEntity<Object> httpResponse = commonHttpHelper.request(url, HttpMethod.GET, null, getHeadersMap(), new TypeReference<>() {
        });
        return httpResponse.getBody();
    }

    public SprinklrCaseCreateResponse createSprinklrCase(SprinklrCaseCreatePayload sprinklrCreateCasePayload) {
        String url = getUri().path("/api/v2/case/profile")
                .build().toUriString();
        return internalRequestWithRetry(url, HttpMethod.POST, sprinklrCreateCasePayload, getHeadersMap(), new TypeReference<>() {
        });
    }

    public void addSprinklrComment(String sprinklrCaseNumber, String message) {
        String url = getUri().path("/api/v2/comment/CASE/" + sprinklrCaseNumber)
                .build().toUriString();
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("text", message);
        this.internalRequestWithRetry(url, HttpMethod.POST, requestBody, getHeadersMap(), new TypeReference<JsonNode>() {
        });
    }

    public SprinklrMessageResponse getMessageConversations(SprinklrMessageRequest sprinklrMessageRequest) {
        String url = getUri().path("/api/v2/message/conversations")
                .build().toUriString();
        return internalRequestWithRetry(url, HttpMethod.POST, sprinklrMessageRequest, getHeadersMap(), new TypeReference<>() {
        });
    }

    public void createMessage(SprinklrMessageCreateRequest sprinklrMessageCreateRequest, String aIdEmail, String originalBody) {
        String url = getUri().path("/api/v2/email/create")
                .queryParam("aId", aIdEmail)
                .build().toUriString();
        this.internalRequestWithRetry(url, HttpMethod.POST, sprinklrMessageCreateRequest, getHeadersMap(), new TypeReference<JsonNode>() {
        });
    }

    public void processEngineAPI(String email, String originalBody) {
        SprinklrMessageProcessEngineRequest processEngineRequest = SprinklrMessageProcessEngineRequest.builder()
                .command("START")
                .processDefinitionId("56845")
                .processVariables(ProcessVariables.builder().to_email_sent_from_api(email).mail_body_sent_from_api(originalBody).build())
                .build();
        String url = getUri().path("/api/v1/process-engine/queryProcess")
                .build().toUriString();
        ResponseEntity<Object> httpResponse = commonHttpHelper.request(url, HttpMethod.PUT, processEngineRequest, getHeadersMap(), new TypeReference<>() {
        });
        httpResponse.getBody();
    }

    public SprinklrMediaResolveResponse resolveMedia(List<String> sprinklrMediaUrls) {
        String url = getUri().path("/api/v2/secure-assets/bulk/fetch")
                .build().toUriString();
        return internalRequestWithRetry(url, HttpMethod.POST, sprinklrMediaUrls, getHeadersMap(), new TypeReference<>() {
        });
    }

    public <Request, Response> Response internalRequestWithRetry(String url, HttpMethod method, Request request, Map<String, String> authHeaders, TypeReference<Response> typeReference) {
        int retryCount = 0;
        while (retryCount < 5) {
            try {
                log.info("internalRequestWithRetry for url: {}, request: {}, retryCount: {}", url, request, retryCount + 1);
                ResponseEntity<Response> response = commonHttpHelper.request(url, method, request, authHeaders, typeReference);
                log.info("internalRequestWithRetry for url: {}, API response: {}, retryCount: {}", url, response.getBody(), retryCount + 1);
                return response.getBody();
            } catch (Exception e) {
                String errMsg = String.format("internalRequestWithRetry failure for url: %s, request: %s on attempt %d - Error: %s", url, request, retryCount + 1, e.getMessage());
                log.error(errMsg, e);
                retryCount++;
                try {
                    Thread.sleep(300);
                } catch (InterruptedException ex) {
                    log.error("internalRequestWithRetry::Retry sleep interrupted. Aborting retries. Retry count: {}", retryCount + 1);
                    break;
                }
            }
        }
        return null;
    }
}
