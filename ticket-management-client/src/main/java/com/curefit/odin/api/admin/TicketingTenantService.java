package com.curefit.odin.api.admin;

import com.curefit.common.rest.client.CommonHttpHelper;
import com.curefit.odin.commons.CommonUtils;
import com.curefit.odin.commons.OdinConfig;
import com.curefit.odin.commons.Constants;
import com.curefit.odin.utils.pojo.TenantEntry;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import java.util.List;

import static com.curefit.odin.commons.Constants.REQUIRED_PARAMETER_EXCEPTION;

/**
 * <AUTHOR>
 */

@Component("ticketingTenantServiceClient")
@Service
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TicketingTenantService {

  @Autowired
  CommonHttpHelper commonHttpHelper;

  @Autowired
  OdinConfig odinConfig;

  public List<TenantEntry> fetchAllTenants(String user, String namespace) {
    if (namespace == null) {
      throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, REQUIRED_PARAMETER_EXCEPTION);
    }
    String url = odinConfig.getBaseUri().path(Constants.TENANT_PATH).toUriString();
    return commonHttpHelper.request(url, HttpMethod.GET, null, CommonUtils.getHeaders(user, namespace), new TypeReference<List<TenantEntry>>() {
    }).getBody();
  }
}
