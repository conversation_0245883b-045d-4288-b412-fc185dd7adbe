package com.curefit.odin.api;

import com.curefit.common.rest.client.CommonHttpHelper;
import com.curefit.odin.commons.CommonUtils;
import com.curefit.odin.commons.OdinConfig;
import com.curefit.odin.commons.Constants;
import com.curefit.odin.commons.OdinS3Service;
import com.curefit.odin.user.pojo.AttachmentEntry;
import com.curefit.odin.user.pojo.S3Bucket;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static com.curefit.odin.commons.Constants.REQUIRED_PARAMETER_EXCEPTION;

/**
 * <AUTHOR>
 */

@Component("attachmentServiceClient")
@Service
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AttachmentService {

  @Autowired
  CommonHttpHelper commonHttpHelper;

  @Autowired
  OdinConfig odinConfig;

  @Autowired
  OdinS3Service odinS3Service;

  public AttachmentEntry addAttachment(AttachmentEntry attachmentEntry, File file, String user) {
    if (attachmentEntry == null) {
      throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, REQUIRED_PARAMETER_EXCEPTION);
    }

    String s3FileUrl = UUID.randomUUID().toString() + "." + getFileExtension(file);
    odinS3Service.upload(fetchS3Bucket(user), s3FileUrl, file);

    attachmentEntry.setUrl(s3FileUrl);
    String url = odinConfig.getBaseUri().path(Constants.ATTACHMENT_PATH)
            .build().toUriString();

    return commonHttpHelper.request(url, HttpMethod.POST, attachmentEntry, CommonUtils.getHeaders(user), AttachmentEntry.class).getBody();
  }

  private static String getFileExtension(File file) {
    String fileName = file.getName();
    if(fileName.lastIndexOf(".") != -1 && fileName.lastIndexOf(".") != 0) {
      return fileName.substring(fileName.lastIndexOf(".") + 1);
    }
    else {
      return "";
    }
  }

  public AttachmentEntry deleteAttachment(Long attachmentId, String modifiedBy) {
    if (attachmentId == null) {
      throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, REQUIRED_PARAMETER_EXCEPTION);
    }

    final Map<String, Object> uriVariables = new HashMap<String, Object>() {{
      put("id", attachmentId);
    }};
    String url = odinConfig.getBaseUri().path(Constants.ATTACHMENT_DELETE_PATH)
            .buildAndExpand(uriVariables).toUriString();

    return commonHttpHelper.request(url, HttpMethod.DELETE, null, CommonUtils.getHeaders(modifiedBy), AttachmentEntry.class).getBody();
  }

  private String fetchS3Bucket(String user) {
    String url = odinConfig.getBaseUri().path(Constants.S3_BUCKET_PATH).build()
            .toUriString();

    S3Bucket s3Bucket = commonHttpHelper.request(url, HttpMethod.GET, null, CommonUtils.getHeaders(user), S3Bucket.class).getBody();
    return s3Bucket.getName();
  }

  public String getPreSignedUrl(String user, String namespace, String fileKey) {
    String url = odinConfig.getBaseUri()
            .path(Constants.ATTACHMENT_GET_PRE_SIGNED_URL_PATH)
            .queryParam("fileKey", fileKey)
            .build()
            .toUriString();

    return commonHttpHelper.request(url, HttpMethod.GET, null, CommonUtils.getHeaders(user, namespace)).getBody();
  }
}
