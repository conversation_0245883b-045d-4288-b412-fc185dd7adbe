package com.curefit.odin.api;

import com.curefit.common.rest.client.CommonHttpHelper;
import com.curefit.odin.commons.OdinConfig;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Component("namespaceSubCategoryMappingServiceClient")
@Service
public class NamespaceSubCategoryMappingService {
    private final CommonHttpHelper commonHttpHelper;
    private final OdinConfig odinConfig;

    public NamespaceSubCategoryMappingService(CommonHttpHelper commonHttpHelper, OdinConfig odinConfig) {
        this.commonHttpHelper = commonHttpHelper;
        this.odinConfig = odinConfig;
    }
}
