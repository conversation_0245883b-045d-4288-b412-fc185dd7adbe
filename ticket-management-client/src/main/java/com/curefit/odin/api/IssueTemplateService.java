package com.curefit.odin.api;

import com.curefit.common.rest.client.CommonHttpHelper;
import com.curefit.odin.admin.pojo.CategoryEntry;
import com.curefit.odin.admin.pojo.IssueTemplateEntry;
import com.curefit.odin.admin.pojo.SubCategoryEntry;
import com.curefit.odin.commons.CommonUtils;
import com.curefit.odin.commons.OdinConfig;
import com.curefit.odin.commons.Constants;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Component("issueTemplateServiceClient")
@Service
public class IssueTemplateService {
    private final CommonHttpHelper commonHttpHelper;
    private final OdinConfig odinConfig;

    public IssueTemplateService(CommonHttpHelper commonHttpHelper, OdinConfig odinConfig) {
        this.commonHttpHelper = commonHttpHelper;
        this.odinConfig = odinConfig;
    }

    public IssueTemplateEntry getIssueTemplateById(String userId, String namespace, Long issueTemplateId) {
        final Map<String, Object> uriVariables = new HashMap<String, Object>() {{
            put("id", issueTemplateId);
        }};
        String url = odinConfig.getBaseUri()
                .path(Constants.ISSUE_TEMPLATE_BY_ID_PATH)
                .buildAndExpand(uriVariables)
                .toUriString();
        return commonHttpHelper.request(url, HttpMethod.GET, null, CommonUtils.getHeaders(userId, namespace), IssueTemplateEntry.class).getBody();
    }

    public List<IssueTemplateEntry> getIssueTemplates(String namespace, String user, Long subCategoryId) {
        String url = odinConfig.getBaseUri()
                .path(Constants.ISSUE_TEMPLATE_GET_BY_SUBCATEGORY_ID)
                .queryParam("subCategoryId", subCategoryId)
                .toUriString();
        return commonHttpHelper.request(url, HttpMethod.GET, null, CommonUtils.getHeaders(user, namespace), new TypeReference<List<IssueTemplateEntry>>() {
        }).getBody();
    }

    public List<CategoryEntry> getCategories(String namespace, Long tenantId, String user) {
        String url = odinConfig.getBaseUri()
                .path(Constants.ISSUE_TEMPLATE_GET_CATEGORIES_PATH)
                .queryParam("tenantId", tenantId)
                .toUriString();
        return commonHttpHelper.request(url, HttpMethod.GET, null, CommonUtils.getHeaders(user, namespace, tenantId), new TypeReference<List<CategoryEntry>>() {
        }).getBody();
    }

    public List<SubCategoryEntry> getSubCategories(String namespace, Long categoryId, String user) {
        String url = odinConfig.getBaseUri()
                .path(Constants.ISSUE_TEMPLATE_GET_SUB_CATEGORIES_PATH)
                .queryParam("categoryId", categoryId)
                .toUriString();
        return commonHttpHelper.request(url, HttpMethod.GET, null, CommonUtils.getHeaders(user, namespace), new TypeReference<List<SubCategoryEntry>>() {
        }).getBody();
    }
}
