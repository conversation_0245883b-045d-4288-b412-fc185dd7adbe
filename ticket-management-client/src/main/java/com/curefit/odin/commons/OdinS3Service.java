package com.curefit.odin.commons;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.PutObjectRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;

/**
 * <AUTHOR>
 */

@Component
public class OdinS3Service {

  @Autowired
  private AmazonS3 amazonS3;

  public void upload(String bucket, String s3FileKey, File file) {
    PutObjectRequest putObjectRequest = new PutObjectRequest(bucket, s3FileKey, file);
    amazonS3.putObject(putObjectRequest);
  }
}
