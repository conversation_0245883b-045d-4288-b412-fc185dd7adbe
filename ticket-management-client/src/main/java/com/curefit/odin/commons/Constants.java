package com.curefit.odin.commons;

/**
 * <AUTHOR>
 */

public class Constants {

    public static final String TICKET_PATH = "/ticket/";
    public static final String TICKET_CREATE_V2_PATH = "/ticket/create/v2";

    public static final String TICKET_UPDATE_PATH = "/ticket/{id}";

    public static final String TICKET_CATEGORY_UPDATE_PATH = "/ticket/category";
    public static final String TICKET_ASSIGNEE_PATH = "/ticket/{id}/assignee/";
    public static final String TICKET_STATUS_PATH = "/ticket/{id}/status/";
    public static final String TICKET_LABEL_PATH = "/ticket/{id}/label/";
    public static final String TICKET_DETAIL_PATH = "/ticket/{id}/detail/";
    public static final String TICKET_NEXT_STATUS_PATH = "/ticket/{id}/nextStatus/";
    public static final String TICKET_PRIORITIES_PATH = "/ticket/priorities/";
    public static final String TICKET_FILTER_PATH = "/ticket/filter";

    public static final String COMMENT_PATH = "/comment/";
    public static final String COMMENT_DELETE_PATH = "/comment/{id}";
    public static final String COMMENT_UPDATE_PATH = "/comment/{id}";
    public static final String COMMENT_FETCH_PATH = "/comment/{id}";
    public static final String COMMENTS_BY_TICKET_ID_PATH = "/comment/active";

    public static final String TICKET_WATCHER_PATH = "/ticket_watcher/";
    public static final String TICKET_WATCHER_DELETE_PATH = "/ticket_watcher/{id}";
    public static final String TICKET_WATCHERS_BY_TICKET_ID_PATH = "/ticket_watcher";

    public static final String ATTACHMENT_PATH = "/attachment/";
    public static final String ATTACHMENT_DELETE_PATH = "/attachment";

    public static final String PRIORITIES_PATH = "/priority/all";

    public static final String STATUSES_PATH = "/status/all";

    public static final String TENANT_PATH = "/tenant/filter";

    public static final String CATEGORY_PATH = "/category/";
    public static final String CATEGORY_BY_ID_PATH = "/category/{id}";
    public static final String CATEGORY_BY_TENANT_ID_PATH = "/category/active";

    public static final String SUB_CATEGORY_PATH = "/sub_category/";
    public static final String SUB_CATEGORY_BY_ID_PATH = "/sub_category/{id}";
    public static final String SUB_CATEGORY_BY_TENANT_ID_PATH = "/sub_category/active";

    public static final String LOCATION_BY_REFERENCE_ID_PATH = "/location/fetch";

    public static final String DEFAULT_ASSIGNEE_PATH = "/default_assignee";

    public static final String ISSUE_TEMPLATE_BY_ID_PATH = "/issue_template/{id}";
    public static final String ISSUE_TEMPLATE_GET_BY_SUBCATEGORY_ID = "/issue_template/active";
    public static final String ISSUE_TEMPLATE_GET_CATEGORIES_PATH = "/issue_template/categories";
    public static final String ISSUE_TEMPLATE_GET_SUB_CATEGORIES_PATH = "/issue_template/subCategories";

    public static final String S3_BUCKET_PATH = "/attachment/s3Bucket";
    public static final String ATTACHMENT_GET_PRE_SIGNED_URL_PATH = "/attachment/pre_signed_url";

    public static final String REQUIRED_PARAMETER_EXCEPTION = "Missing the required parameters";

    public static final String USER_ID_HEADER = "X_USER_ID";
    public static final String NAMESPACE_HEADER = "X_NAMESPACE";
    public static final String TENANT_ID_HEADER = "X_TENANT_ID";
    public static final String SPRINKLR_CUSTOMER_SUPPORT_USER = "<EMAIL>";
    public static final String CUSTOMER_SUPPORT_NAMESPACE = "CUSTOMER_SUPPORT";
}
