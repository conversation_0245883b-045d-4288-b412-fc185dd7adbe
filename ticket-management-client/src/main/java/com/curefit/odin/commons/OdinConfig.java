package com.curefit.odin.commons;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * <AUTHOR>
 */


@Component("odinClientConfig")
@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OdinConfig {

    @Value("${ticketingSystem.baseUrl}")
    private String baseUrl;

    @Value("${ticketingSystem.user:system}")
    private String user;

    public UriComponentsBuilder getBaseUri() {
        return UriComponentsBuilder.fromHttpUrl(getBaseUrl());
    }
}
