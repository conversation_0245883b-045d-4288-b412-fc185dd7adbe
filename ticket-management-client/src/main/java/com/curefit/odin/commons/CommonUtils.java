package com.curefit.odin.commons;

import java.util.HashMap;
import java.util.Map;

import static com.curefit.odin.commons.Constants.*;

/**
 * <AUTHOR>
 */

public class CommonUtils {

    private static final String DEFAULT_NAMESPACE = "odin";

    public static Map<String, String> getHeaders(String user, String namespace) {
        return new HashMap<>() {{
            put(USER_ID_HEADER, user);
            put(NAMESPACE_HEADER, namespace == null ? DEFAULT_NAMESPACE : namespace);
        }};
    }

    public static Map<String, String> getHeaders(String user) {
        return new HashMap<>() {{
            put(USER_ID_HEADER, user);
            put(NAMESPACE_HEADER, DEFAULT_NAMESPACE);
        }};
    }

    public static Map<String, String> getHeaders(String user, String namespace, Long tenantId) {
        return new HashMap<>() {{
            put(USER_ID_HEADER, user);
            put(NAMESPACE_HEADER, namespace == null ? DEFAULT_NAMESPACE : namespace);
            if (tenantId != null) {
                put(TENANT_ID_HEADER, tenantId.toString());
            }
        }};
    }
}
