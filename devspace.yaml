version: v1beta10

images:
  app:
    image: ${DOCKER_REGISTRY}/${ORG}/${ENVIRONMENT}/${APP_NAME}
    tags:
      - ${TAG}
    entrypoint:
      - "/bin/bash"
    cmd:
      - "-c"
      - "java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=localhost:9229 -javaagent:dd-java-agent.jar -Ddd.trace.global.tags=env:$ENVIRONMENT -Ddd.service=ticketing-system -Ddd.service.mapping=sns:ticketing-system-sns,sqs:ticketing-system-sqs,mongo:ticketing-system-mongo,mysql:ticketing-system-mysql,redis:ticketing-system-redis -Dserver.port=13000 -jar ticket-management*.jar"
    injectRestartHelper: true
    dockerfile: ./Dockerfile-main
    build:
      kaniko:
        cache: false
        initImage: public.ecr.aws/docker/library/alpine:latest
        options:
          buildArgs:
            FROM: ${FROM}
            APP_NAME: ${APP_NAME}
            APP_ENV: ${ENVIRONMENT}
            APP_VERSION: ${APP_VERSION}
            ENVIRONMENT: ${ENVIRONMENT}

dev:
  ports:
    - labelSelector:
        app: ${APP_NAME}
        version: ${VIRTUAL_CLUSTER}
      forward:
        - port: 13000
          remotePort: 13000
        - port: 9229
          remotePort: 9229
  sync:
    - labelSelector:
        app: ${APP_NAME}
        version: ${VIRTUAL_CLUSTER}
      containerName: ${APP_NAME}
      localSubPath: ./ticket-management-core/target
      containerPath: /app
      disableDownload: true
      initialSync: preferLocal
      onUpload:
        restartContainer: true
  logs:
    disabled: true
