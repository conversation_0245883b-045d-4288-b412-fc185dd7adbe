package com.curefit.odin.integration.freshdesk.models;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import com.curefit.commons.sf.model.BaseMySQLEntity;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 *
 */
@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "odin_type_fd_type_map", uniqueConstraints = { @UniqueConstraint(columnNames = { "tenant_id" ,"category_id" ,"sub_category_id"  }) })
public class OdinTypeFDTypeMap extends BaseMySQLEntity {

  /**
   * 
   */
  private static final long serialVersionUID = 8519516535124191867L;
  
  @Column(name = "tenant_id")
  Long tenantId;
  
  @Column(name = "category_id")
  Long categoryId;
  
  @Column(name = "sub_category_id")
  Long subCategoryId;

  @Column(name = "fd_cf_type")
  String fdCFType;
  
  @Column(name = "fd_level1")
  String fdLevel1;
  
  @Column(name = "fd_level2")
  String fdLevel2;
  
  @Column(name = "fd_level3")
  String fdLevel3;
}
