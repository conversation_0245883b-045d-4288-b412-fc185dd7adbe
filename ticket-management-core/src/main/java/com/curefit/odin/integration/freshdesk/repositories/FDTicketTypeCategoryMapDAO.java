package com.curefit.odin.integration.freshdesk.repositories;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import com.curefit.commons.sf.repository.BaseMySQLRepository;
import com.curefit.odin.integration.freshdesk.models.FDTypeOdinTypeMap;

import java.util.List;


@Repository
public interface FDTicketTypeCategoryMapDAO extends BaseMySQLRepository<FDTypeOdinTypeMap> {

    @Query("SELECT DISTINCT fdCFType FROM FDTypeOdinTypeMap")
    List<String> findDistinctCfType();

    @Query("SELECT DISTINCT fdLevel1 FROM FDTypeOdinTypeMap where fdCFType = :fdCFType")
    List<String> findDistinctFdLevel1(@Param("fdCFType") String fdCFType);

    @Query("SELECT DISTINCT fdLevel2 FROM FDTypeOdinTypeMap where fdCFType = :fdCFType and fdLevel1 = :fdLevel1")
    List<String> findDistinctFdLevel2(@Param("fdCFType") String fdCFType, @Param("fdLevel1") String fdLevel1);

    @Query("SELECT DISTINCT fdLevel3 FROM FDTypeOdinTypeMap where fdCFType = :fdCFType and fdLevel1 = :fdLevel1 and fdLevel2 = :fdLevel2")
    List<String> findDistinctFdLevel3(@Param("fdCFType") String fdCFType, @Param("fdLevel1") String fdLevel1, @Param("fdLevel2") String fdLevel2);
}
