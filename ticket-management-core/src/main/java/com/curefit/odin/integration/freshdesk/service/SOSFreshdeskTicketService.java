package com.curefit.odin.integration.freshdesk.service;

import com.curefit.freshdesk.pojo.CustomFields;
import com.curefit.freshdesk.pojo.FreshdeskTicket;
import com.curefit.freshdesk.pojo.Note;
import com.curefit.freshdesk.service.FreshdeskService;
import com.curefit.odin.config.OdinConfigurations;
import com.curefit.odin.enums.TicketDest;
import com.curefit.odin.integration.freshdesk.CSFreshdeskHelper;
import com.curefit.odin.integration.freshdesk.FreshdeskHelper;
import com.curefit.odin.user.pojo.TicketEntry;
import com.curefit.odin.utils.pojo.ExternalTicketResponse;
import com.curefit.odin.utils.pojo.SOSRequestEntry;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

import static com.curefit.odin.commons.Constants.SOURCE;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SOSFreshdeskTicketService {

  @Autowired
  CSFreshdeskHelper freshdeskHelper;

  @Qualifier(value = "sosFreshdeskService")
  @Autowired
  FreshdeskService freshdeskService;

  @Autowired
  OdinConfigurations odinConfigurations;

  public ExternalTicketResponse createTicket(TicketEntry ticketEntry, SOSRequestEntry.EmployeeDetails employeeDetails) {
    FreshdeskTicket freshdeskTicket = new FreshdeskTicket();
    freshdeskTicket.setSubject(ticketEntry.getTitle());
    freshdeskTicket.setDescription(buildDescription(ticketEntry));
    freshdeskTicket.setTicketPriority(freshdeskHelper.getPriorityMap().get(ticketEntry.getPriority()));
    freshdeskTicket.setStatus(freshdeskHelper.getStatusValue(freshdeskHelper.getStatusMap().get(ticketEntry.getStatus())));
    freshdeskTicket.setTags(new ArrayList<String>() {{
      add(SOURCE);
    }});
    String email = employeeDetails.getEmail();
    if (StringUtils.isEmpty(email)) {
      email = odinConfigurations.getFreshdeskSourceEmail();
    }
    freshdeskTicket.setEmail(email);

    CustomFields customFields = new CustomFields();
    customFields.setEmployeePhone(employeeDetails.getPhone());
    customFields.setEmployeeManagerPhone(employeeDetails.getReportingManagerPhone());
    customFields.setSourceTicketId(String.valueOf(ticketEntry.getId()));
    freshdeskTicket.setCustomFields(customFields);

    freshdeskTicket = freshdeskService.createTicket(freshdeskTicket);
    log.info("freshdesk ticket with id {} created for sos, odin ticket id {} ", freshdeskTicket.getId(), ticketEntry.getId());
    return ExternalTicketResponse.builder().id(String.valueOf(freshdeskTicket.getId()))
            .dest(TicketDest.SOS).build();
  }

  public void addPrivateNote(String id, String message) {
    Note note = new Note();
    note.setBody(message);
    freshdeskService.addPrivateNote(Long.valueOf(id), note);
  }

  private String buildDescription(TicketEntry ticketEntry) {
    return "<b>Description : </b>" + ticketEntry.getDescription() + "<br />"
        + "<b>Due Date : </b>" + (ticketEntry.getDueDate() != null ? ticketEntry.getDueDate() : "") + "<br />"
        + "<b>Priority : </b>" + (ticketEntry.getPriority() != null ? ticketEntry.getPriority().getName() : "") + "<br />";
  }
}
