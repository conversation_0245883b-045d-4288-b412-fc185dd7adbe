package com.curefit.odin.integration.freshdesk;
import com.curefit.freshdesk.pojo.FreshdeskTicketStatusMapper;
import org.springframework.stereotype.Component;

import static com.curefit.freshdesk.enums.TicketStatus.*;

@Component
public class SugarfitFreshdeskStatusMapper extends FreshdeskTicketStatusMapper {

  public SugarfitFreshdeskStatusMapper() {
    addStatusMapping(Open, 2);
    addStatusMapping(Pending, 3);
    addStatusMapping(Resolved, 4);
    addStatusMapping(Closed, 5);
    addStatusMapping(WaitingForOps, 6);
    addStatusMapping(OpsResponded, 11);
    addStatusMapping(OpsResolved, 10);
    addStatusMapping(Reopened, 14);
  }
}
