package com.curefit.odin.integration.freshdesk.controller;

import com.curefit.cf.commons.pojo.PagedResultEntry;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.controller.BaseController;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.service.CategoryService;
import com.curefit.odin.admin.service.SubCategoryService;
import com.curefit.odin.admin.service.TenantService;
import com.curefit.odin.integration.freshdesk.models.OdinTypeFDTypeMap;
import com.curefit.odin.integration.freshdesk.service.OdinTypeFDTypeMappingService;
import com.curefit.odin.integrations.pojo.OdinTypeFDTypeMapEntry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */

@RestController
@Slf4j
@RequestMapping("/odin_fd_map")
public class OdinTypeFDTypeMapController extends BaseController<OdinTypeFDTypeMap, OdinTypeFDTypeMapEntry> {

  @Autowired
  TenantService tenantService;

  @Autowired
  CategoryService categoryService;

  @Autowired
  SubCategoryService subCategoryService;

  @Autowired
  RollbarService rollbarService;

  public OdinTypeFDTypeMapController(OdinTypeFDTypeMappingService odinTypeFDTypeMappingService) {
    super(odinTypeFDTypeMappingService);
  }

  //TODO: have to move this code to service (cyclic dependency issue is coming)
  @Override
  public ResponseEntity<PagedResultEntry<Long, OdinTypeFDTypeMapEntry>> search(int offset, int limit, String sortBy, String sortOrder, String query, @RequestParam(value = "keyValueSeparator",required = false) String keyValueSeparator) throws InvalidSeachQueryException {
    PagedResultEntry<Long, OdinTypeFDTypeMapEntry> response = baseMySQLService.search(offset, limit, sortBy, sortOrder, query);
    response.getElements().forEach(entry -> {
      try {
        if (entry.getTenantId() != null) {
          entry.setTenant(tenantService.findOneById(entry.getTenantId()));
        }
        if (entry.getCategoryId() != null) {
          entry.setCategory(categoryService.findOneById(entry.getCategoryId()));
        }
        if (entry.getSubCategoryId() != null) {
          entry.setSubCategory(subCategoryService.findOneById(entry.getSubCategoryId()));
        }
      } catch (BaseException e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
      }
    });
    return new ResponseEntity(response, HttpStatus.OK);
  }
}
