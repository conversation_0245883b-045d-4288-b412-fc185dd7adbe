package com.curefit.odin.integration.freshdesk.service;


import com.curefit.odin.admin.pojo.UserEntry;
import com.curefit.odin.api.SprinklrExternalClient;
import com.curefit.odin.enums.Status;
import com.curefit.odin.enums.TicketDest;
import com.curefit.odin.enums.TicketSource;
import com.curefit.odin.integrations.pojo.OdinTypeFDTypeMapEntry;
import com.curefit.odin.sprinklr.pojo.SprinklrCaseCreatePayload;
import com.curefit.odin.sprinklr.pojo.SprinklrCaseCreateResponse;
import com.curefit.odin.sprinklr.pojo.SprinklrContactInfo;
import com.curefit.odin.sprinklr.pojo.webhook.Workflow;
import com.curefit.odin.sprinklr.service.SprinklrCultCustomFieldMappingService;
import com.curefit.odin.sprinklr.service.SprinklrTicketService;
import com.curefit.odin.user.pojo.FieldDataEntry;
import com.curefit.odin.user.pojo.FieldValueEntry;
import com.curefit.odin.user.pojo.TicketEntry;
import com.curefit.odin.user.service.ExternalTicketService;
import com.curefit.odin.utils.pojo.ExternalTicketResponse;
import com.curefit.userservice.client.UserServiceClient;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.curefit.odin.commons.Constants.*;
import static com.curefit.odin.commons.SprinklrConstants.*;

@Service(value = "SprinklrService")
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SprinklrService implements ExternalTicketService {

    @Autowired
    SprinklrTicketService sprinklrTicketService;

    @Autowired
    SprinklrExternalClient sprinklrExternalClient;

    @Autowired
    OdinTypeFDTypeMappingService odinTypeFDTypeMappingService;

    @Autowired
    SprinklrCultCustomFieldMappingService sprinklrCultCustomFieldMappingService;

    @Autowired
    UserServiceClient userServiceClient;

    @Override
    public ExternalTicketResponse createTicket(String namespace, TicketEntry ticketEntry) {
        if (!StringUtils.hasLength(ticketEntry.getUserId())) {
            log.error("sprinklr::createTicket: userId is null for ticketId {}", ticketEntry.getId());
            return null;
        }
        com.curefit.userservice.pojo.entry.UserEntry userEntry;
        try {
            userEntry = userServiceClient.getUser(ticketEntry.getUserId()).get();
        } catch (Exception e) {
            log.error("sprinklr::createTicket: failed to fetch user entry for userId {}", ticketEntry.getUserId(), e);
            return null;
        }
        if (userEntry == null || userEntry.getEmail() == null) {
            log.error("sprinklr::createTicket: userEntry is null or email is null for userId {}", ticketEntry.getUserId());
            return null;
        }
        SprinklrCaseCreatePayload sprinklrCaseCreatePayload = SprinklrCaseCreatePayload.builder()
                .subject(ticketEntry.getTitle() + " " + "Injury")
                .description(ticketEntry.getDescription())
                .workflow(Workflow.builder()
                        .customProperties(getCustomProperties(ticketEntry, userEntry))
                        .build())
                .channelType("EMAIL")
                .contactInfo(SprinklrContactInfo.builder()
                        .email(userEntry.getEmail())
                        .firstName(userEntry.getFirstName())
                        .lastName(userEntry.getLastName())
                        .fullName(userEntry.getFirstName() + " " + userEntry.getLastName())
                        .phoneNo(userEntry.getPhone())
                        .build()
                )
                .build();

        SprinklrCaseCreateResponse sprinklrCaseCreateResponse = sprinklrExternalClient.createSprinklrCase(sprinklrCaseCreatePayload);
        log.info("sprinklr::createTicket: {} for ticketId {}", sprinklrCaseCreateResponse, ticketEntry.getId());
        return ExternalTicketResponse.builder().id(String.valueOf(sprinklrCaseCreateResponse.getData().getCaseNumber()))
                .dest(TicketDest.SPRINKLR).build();
    }

    public Object getFieldValueFromTicket(TicketEntry ticketEntry, Long fieldId) {
        return Objects.requireNonNull(
                ticketEntry.getFields().stream()
                        .filter(field -> fieldId.equals(field.getKey().getId()))
                        .map(FieldDataEntry::getValue)
                        .findFirst()
                        .orElse(null)
        );
    }

    private Map<String, List<Object>> getCustomProperties(TicketEntry ticketEntry, com.curefit.userservice.pojo.entry.UserEntry userEntry) {
        Map<String, List<Object>> customProperties = new HashMap<>();
        Map<String, String> cultVsSprinklrMapping = sprinklrCultCustomFieldMappingService.cultVsSprinklrMapping();
        customProperties.put(cultVsSprinklrMapping.get(EXTERNAL_SYSTEM_STATUS), List.of("Pending on Ops"));
        customProperties.put(cultVsSprinklrMapping.get(ODIN_TICKET_LOCATION_NAME), List.of(ticketEntry.getLocationEntry().getCenterName()));
        customProperties.put(cultVsSprinklrMapping.get(ODIN_TICKET_LOCATION_ID), List.of(ticketEntry.getLocationEntry().getId().toString()));
        customProperties.put(cultVsSprinklrMapping.get(ODIN_TICKET_DUE_DATE), List.of(new SimpleDateFormat("dd MM yyyy").format(ticketEntry.getDueDate())));
        customProperties.put(cultVsSprinklrMapping.get(ODIN_ASSIGNED_TO), List.of(ticketEntry.getAssigneeQueueUsers().stream().map(UserEntry::getEmailId).collect(Collectors.joining(", "))));
        customProperties.put(cultVsSprinklrMapping.get(ODIN_STATUS), List.of(ticketEntry.getStatus().getName()));
        customProperties.put(cultVsSprinklrMapping.get(ODIN_TICKET_ID), List.of(ticketEntry.getId().toString()));
        customProperties.put(cultVsSprinklrMapping.get(CUREFIT_USER_ID), List.of(ticketEntry.getUserId()));
        customProperties.put(cultVsSprinklrMapping.get(TEAM_TO_ASSIGN_ODIN_CASE_FIELD), List.of("Escalation Team"));
        customProperties.put(cultVsSprinklrMapping.get(AGENT_SKILL_SET_FIELD), List.of("Escalated"));
        customProperties.put(cultVsSprinklrMapping.get(CASE_CREATED_VIA_API_FLAG), List.of("Y"));
        customProperties.put(cultVsSprinklrMapping.get(CULT_USERNAME_FIELD), List.of(userEntry.getFirstName() + " " + userEntry.getLastName()));
        customProperties.put(cultVsSprinklrMapping.get(CULT_USER_PHONE_NUMBER_FIELD), List.of(userEntry.getPhone()));
        customProperties.put(cultVsSprinklrMapping.get(CULT_KNOWN_CUSTOMER_FLAG), List.of("Known Customer"));
        customProperties.put(cultVsSprinklrMapping.get(CULT_SUBJECT_FIELD), List.of(ticketEntry.getTitle() + " " + "Injury"));
        customProperties.put(cultVsSprinklrMapping.get(WIDGET_VISIBILITY_ON_SPRINKLR_FLAG), List.of("Y"));
        customProperties.put(cultVsSprinklrMapping.get(CULT_LIVE_CHAT_L1_FIELD), List.of("Session"));

        List<FieldValueEntry> valueEntries = (List<FieldValueEntry>) getFieldValueFromTicket(ticketEntry, 788L);
        String value = valueEntries.getFirst().getValue();
        customProperties.put(cultVsSprinklrMapping.get(CULT_LIVE_CHAT_SUB_L1_FIELD), List.of(getSessionType(value)));
        customProperties.put(cultVsSprinklrMapping.get(ODIN_L2_VALUE), List.of("Injury"));
        customProperties.put(cultVsSprinklrMapping.get(CUREFIT_ODIN_API_CASE_PICKLIST), List.of("Yes"));
        return customProperties;
    }

    public String getSessionType(String workout) {
        return switch (workout) {
            case "Gym" -> "Gym";
            case "GX" -> "Group Classes";
            case "Play" -> "Play";
            default -> "Gym"; // Default value if no match is found
        };
    }

    private void updateSprinklrTicket(String caseNumber, TicketEntry ticketEntry) {
        Map<String, List<Object>> customPropertiesToBeUpdated = new HashMap<>();
        customPropertiesToBeUpdated.put(ODIN_TICKET_ID, List.of(ticketEntry.getId().toString()));
        log.info("updateSprinklrTicket: id {}", ticketEntry.getId());

        if (ticketEntry.getStatus() != null) {
            customPropertiesToBeUpdated.put(ODIN_STATUS, List.of(ticketEntry.getStatus().toString()));
        }
        log.info("updateSprinklrTicket: status {} {}", customPropertiesToBeUpdated, ticketEntry.getId());

        if (!CollectionUtils.isEmpty(ticketEntry.getAssigneeQueueUsers())) {
            String assignedUsers = ticketEntry.getAssigneeQueueUsers().stream()
                    .map(UserEntry::getEmailId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(", "));
            customPropertiesToBeUpdated.put(ODIN_ASSIGNED_TO, List.of(assignedUsers));
        }
        log.info("updateSprinklrTicket: assign value {} {} {}", customPropertiesToBeUpdated, ticketEntry.getAssigneeQueueUsers(), ticketEntry.getId());

        if (ticketEntry.getDueDate() != null) {
            String formattedDate = new SimpleDateFormat("dd MM yyyy").format(ticketEntry.getDueDate());
            customPropertiesToBeUpdated.put(ODIN_SLA, List.of(formattedDate));
            customPropertiesToBeUpdated.put(ODIN_TICKET_DUE_DATE, List.of(formattedDate));
        }

        if (ticketEntry.getClosedAt() != null) {
            String resolveDate = new SimpleDateFormat("dd MM yyyy").format(ticketEntry.getClosedAt());
            customPropertiesToBeUpdated.put(ODIN_TICKET_RESOLUTION_DATE, List.of(resolveDate));
        }

        if (ticketEntry.getLocationEntry() != null && ticketEntry.getLocationEntry().getCenterServiceRefId() != null) {
            customPropertiesToBeUpdated.put(ODIN_TICKET_LOCATION_ID, List.of(ticketEntry.getLocationEntry().getCenterServiceRefId()));
            customPropertiesToBeUpdated.put(ODIN_TICKET_LOCATION_NAME, List.of(ticketEntry.getLocationEntry().getCenterName()));
        }
        log.info("updateSprinklrTicket: {}", customPropertiesToBeUpdated);

        sprinklrTicketService.updateExternalSprinklrCase(caseNumber, customPropertiesToBeUpdated);
    }

    private String getSprinklrExternalSystemStatusFromOdinStatus(TicketEntry ticketEntry) {
        switch (ticketEntry.getStatus()) {
            case OPEN, ON_HOLD, IN_PROGRESS -> {
                return PENDING_ON_OPS;
            }
            case RESOLVED, REJECTED, DONE -> {
                return OPS_RESOLVED;
            }
            case null, default -> {
                return null;
            }
        }
    }

    @Override
    public void updateTicket(String id, TicketEntry ticketEntry) {
        log.info("sprinklr::updateTicket: {} {}", ticketEntry, id);
        updateSprinklrTicket(id, ticketEntry);
        this.updateStatus(id, ticketEntry);
    }

    @Override
    public void updateAssignees(String id, TicketEntry ticketEntry) throws Exception {
        log.info("sprinklr::updateAssignees: {} {}", ticketEntry, id);
        updateSprinklrTicket(id, ticketEntry);
    }

    @Override
    public void updateStatus(String id, TicketEntry ticketEntry) {
        log.info("sprinklr::updateStatus caseNumber: {}, odin status: {}", id, ticketEntry.getStatus().name());
        String sprinklrExternalSystemStatus = getSprinklrExternalSystemStatusFromOdinStatus(ticketEntry);
        if (sprinklrExternalSystemStatus != null) {
            updateSprinklrTicketExternalStatusV2(id, sprinklrExternalSystemStatus, ticketEntry.getStatus());
        }
    }

    @Override
    public void updateStatusForDest(String id, TicketEntry ticketEntry) {
        log.info("sprinklr::updateStatusForDest caseNumber: {}, odin status: {}", id, ticketEntry.getStatus().name());
        this.updateStatus(id, ticketEntry);
    }

    @Override
    public Long addComment(String namespace, String id, String message) {
        if (namespace != null && !namespace.equals(CUSTOMER_SUPPORT_NAMESPACE)) {
            log.info("sprinklr::addComment caseNumber: {}, namespace: {}, message: {}", id, namespace, message);
            sprinklrExternalClient.addSprinklrComment(id, message);
        }
        return null;
    }

    @Override
    public Long addPrivateNote(String namespace, String id, String message) {
        if (namespace != null && !namespace.equals(CUSTOMER_SUPPORT_NAMESPACE)) {
            log.info("sprinklr::addPrivateNote caseNumber: {}, namespace: {}, message: {}", id, namespace, message);
            sprinklrExternalClient.addSprinklrComment(id, message);
        }
        return null;
    }

    @Override
    public Long addPrivateNoteAndUpdateStatus(String namespace, String id, TicketEntry ticketEntry, String message) {
        log.info("sprinklr::addPrivateNoteAndUpdateStatus caseNumber: {} message: {}", id, message);
        if (CUSTOMER_SUPPORT_NAMESPACE.equals(namespace) && !TICKET_COMPLETE_STATUSES.contains(ticketEntry.getStatus())) {
            updateSprinklrTicketExternalStatus(id, PENDING_ON_OPS);
            return null;
        }
        if (!TICKET_COMPLETE_STATUSES.contains(ticketEntry.getStatus())) {
            updateSprinklrTicketExternalStatus(id, OPS_RESPONDED);
        }
        sprinklrExternalClient.addSprinklrComment(ticketEntry.getSourceRefId(), message);
        return null;
    }

    private void updateSprinklrTicketExternalStatus(String caseNumber, String status) {
        Map<String, List<Object>> cultProperties = new HashMap<>();
        cultProperties.put(EXTERNAL_SYSTEM_STATUS, List.of(status));
        cultProperties.put(EXTERNAL_SYSTEM_STATUS_PICK_LIST, List.of(status));
        sprinklrTicketService.updateExternalSprinklrCase(caseNumber, cultProperties);
    }

    private void updateSprinklrTicketExternalStatusV2(String caseNumber, String status, Status odinStatus) {
        Map<String, List<Object>> cultProperties = new HashMap<>();
        cultProperties.put(EXTERNAL_SYSTEM_STATUS, List.of(status));
        cultProperties.put(EXTERNAL_SYSTEM_STATUS_PICK_LIST, List.of(status));
        cultProperties.put(ODIN_STATUS, List.of(odinStatus.toString()));
        sprinklrTicketService.updateExternalSprinklrCase(caseNumber, cultProperties);
    }

    @Override
    public boolean isTicketNeedsToCreate(String namespace, TicketEntry ticketEntry) {
        return ticketEntry != null && INJURY_CATEGORY_ID.equals(ticketEntry.getCategoryId());   // to create injury tickets on Sprinklr
    }

    private boolean isTicketNeedsToCreate(TicketEntry ticketEntry) {
        if (ticketEntry.getSource() != TicketSource.SPRINKLR) {
            OdinTypeFDTypeMapEntry odinTypeFDTypeMapEntry = odinTypeFDTypeMappingService.fetchFDTicketType(ticketEntry.getTenantId(), ticketEntry.getCategoryId(), ticketEntry.getSubCategoryId());
            return odinTypeFDTypeMapEntry != null;
        }
        return false;
    }
}
