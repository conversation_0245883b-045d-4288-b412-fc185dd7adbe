package com.curefit.odin.integration.freshdesk.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.integration.freshdesk.models.OdinTypeFDTypeMap;
import com.curefit.odin.integration.freshdesk.repositories.OdinTypeFDTypeMapDAO;
import com.curefit.odin.integrations.pojo.OdinTypeFDTypeMapEntry;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;



@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OdinTypeFDTypeMappingService
    extends BaseMySQLService<OdinTypeFDTypeMap, OdinTypeFDTypeMapEntry> {



  @Autowired
  RollbarService rollbarService;

  public OdinTypeFDTypeMappingService(OdinTypeFDTypeMapDAO fdTicketDAO) {
    super(fdTicketDAO);
  }


  /**
   * @param tenantId
   * @param catId
   * @param subCatId
   * @return
   * @throws BaseException 
   */
  @Cacheable(value = "fetchFDTicketType", unless = "#result == null")
  public OdinTypeFDTypeMapEntry fetchFDTicketType(Long tenantId, Long catId, Long subCatId ) {
    String query = "tenantId.eq:" + tenantId;
    if (catId != null) {
      query = query + ";categoryId.eq:" + catId;
      if (subCatId != null) {
        query = query + ";subCategoryId.eq:" + subCatId;
      }
    }
    
    try {
      List<OdinTypeFDTypeMapEntry> result = search(0, 1, null, null, query).getElements();
      if(!CollectionUtils.isEmpty(result)) {
        return result.get(0);
      }
    } catch (InvalidSeachQueryException e) {
      log.error(e.getMessage(),e);
      rollbarService.error(e);
    }
    return null;
  }

}
