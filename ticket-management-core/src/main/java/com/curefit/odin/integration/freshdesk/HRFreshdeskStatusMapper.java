package com.curefit.odin.integration.freshdesk;

import com.curefit.freshdesk.pojo.FreshdeskTicketStatusMapper;
import org.springframework.stereotype.Component;

import static com.curefit.freshdesk.enums.TicketStatus.*;

/**
 * <AUTHOR>
 */

@Component
public class HRFreshdeskStatusMapper extends FreshdeskTicketStatusMapper {

    public HRFreshdeskStatusMapper() {
        addStatusMapping(Open, 2);
        addStatusMapping(Pending, 3);
        addStatusMapping(Resolved, 4);
        addStatusMapping(Closed, 5);
        addStatusMapping(WaitingForOps, 10);
        addStatusMapping(OpsResponded, 11);
        addStatusMapping(OpsResolved, 12);
        addStatusMapping(Reopened, 16);
    }
}

