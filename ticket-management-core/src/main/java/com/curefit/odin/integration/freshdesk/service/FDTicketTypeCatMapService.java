package com.curefit.odin.integration.freshdesk.service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.service.CategoryService;
import com.curefit.odin.admin.service.SubCategoryService;
import com.curefit.odin.admin.service.TenantService;
import com.curefit.odin.integration.freshdesk.models.FDTypeOdinTypeMap;
import com.curefit.odin.integration.freshdesk.repositories.FDTicketTypeCategoryMapDAO;
import com.curefit.odin.integrations.pojo.FDTicketTypeCategoryMapEntry;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;



@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class FDTicketTypeCatMapService
    extends BaseMySQLService<FDTypeOdinTypeMap, FDTicketTypeCategoryMapEntry> {


  @Autowired
  TenantService tenantService;

  @Autowired
  CategoryService categoryService;

  @Autowired
  SubCategoryService subCategoryService;

  @Autowired
  RollbarService rollbarService;

  public FDTicketTypeCatMapService(FDTicketTypeCategoryMapDAO fdTicketDAO) {
    super(fdTicketDAO);
  }


  /*
   * (non-Javadoc)
   * 
   * @see
   * com.curefit.commons.sf.service.BaseMySQLService#convertToEntity(com.curefit.cf.commons.pojo.
   * BaseEntry)
   */
  @Override
  public FDTypeOdinTypeMap convertToEntity(FDTicketTypeCategoryMapEntry entry) {
    return super.convertToEntity(entry);
  }

  /*
   * (non-Javadoc)
   * 
   * @see
   * com.curefit.commons.sf.service.BaseMySQLService#convertToEntry(com.curefit.commons.sf.model.
   * BaseMySQLEntity)
   */
  @Override
  public FDTicketTypeCategoryMapEntry convertToEntry(FDTypeOdinTypeMap entity) {
    FDTicketTypeCategoryMapEntry convertedEntry = super.convertToEntry(entity);
    try {
      if (entity.getTenantId() != null) {
        convertedEntry.setTenant(tenantService.findOneById(entity.getTenantId()));
      }
      if (entity.getCategoryId() != null) {
        convertedEntry.setCategory(categoryService.findOneById(entity.getCategoryId()));
      }
      if (entity.getSubCategoryId() != null) {
        convertedEntry.setSubCategory(subCategoryService.findOneById(entity.getSubCategoryId()));
      }
    } catch (BaseException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    return convertedEntry;

  }


  /**
   * @param cfType
   * @param level1
   * @param level2
   * @param level3
   * @return
   * @throws BaseException 
   */
  public FDTicketTypeCategoryMapEntry fetchOdinType(String cfType, String level1, String level2,
      String level3, String mappingType) throws BaseException {
    StringBuilder query = new StringBuilder("fdCFType.eq:").append(cfType);

    if (level1 != null) {
      query.append(";fdLevel1.eq:").append(level1);
      if (level2 != null) {
        query.append(";fdLevel2.eq:").append(level2);
        if (level3 != null) {
          query.append(";fdLevel3.eq:").append(level3);
        }
      }
    }
    if (mappingType != null) {
      query.append(";mappingType.eq:").append(mappingType);
    }

    try {
      List<FDTicketTypeCategoryMapEntry> result = search(0, 1, null, null, query.toString()).getElements();
      if(!CollectionUtils.isEmpty(result)) {
        return result.get(0);
      }else {
        return null;
      }
    } catch (InvalidSeachQueryException e) {
      log.error(e.getMessage(),e);
      rollbarService.error(e);
      throw new BaseException();
    }
  }

  private List<FDTicketTypeCategoryMapEntry> findAll(String query) {
    try {
      return search(0, -1, null, null, query).getElements();
    } catch (InvalidSeachQueryException e) {
      log.error(e.getMessage(),e);
      rollbarService.error(e);
    }
    return Collections.emptyList();
  }

  public List<String> fetchAllCfTypes() {
    return ((FDTicketTypeCategoryMapDAO)baseMySQLRepository).findDistinctCfType();
  }

  public List<String> fetchAllLevel1(String cfType) {
    return ((FDTicketTypeCategoryMapDAO)baseMySQLRepository).findDistinctFdLevel1(cfType);
  }

  public List<String> fetchAllLevel2(String cfType, String level1) {
    return ((FDTicketTypeCategoryMapDAO)baseMySQLRepository).findDistinctFdLevel2(cfType, level1);
  }

  public List<String> fetchAllLevel3(String cfType, String level1, String level2) {
    return ((FDTicketTypeCategoryMapDAO)baseMySQLRepository).findDistinctFdLevel3(cfType, level1, level2) ;
  }
}
