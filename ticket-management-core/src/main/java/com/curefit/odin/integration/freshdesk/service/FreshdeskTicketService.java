package com.curefit.odin.integration.freshdesk.service;


import com.curefit.freshdesk.enums.TicketStatus;
import com.curefit.freshdesk.pojo.Comment;
import com.curefit.freshdesk.pojo.FreshdeskTicket;
import com.curefit.freshdesk.pojo.Note;
import com.curefit.freshdesk.service.FreshdeskService;
import com.curefit.odin.enums.TicketDest;
import com.curefit.odin.integration.freshdesk.CSFreshdeskHelper;
import com.curefit.odin.user.pojo.TicketEntry;
import com.curefit.odin.user.service.ExternalTicketService;
import com.curefit.odin.utils.pojo.ExternalTicketResponse;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;


import static com.curefit.odin.commons.Constants.CUSTOMER_SUPPORT_NAMESPACE;
import static com.curefit.odin.commons.Constants.TICKET_COMPLETE_STATUSES;

/**
 * <AUTHOR>
 */

@Service(value = "FreshdeskTicketService")
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class FreshdeskTicketService implements ExternalTicketService {

  @Autowired
  CSFreshdeskHelper freshdeskHelper;

  @Qualifier(value = "freshdeskService")
  @Autowired
  FreshdeskService freshdeskService;

  @Override
  public ExternalTicketResponse createTicket(String namespace, TicketEntry ticketEntry) {
    if (namespace != null && !namespace.equals(CUSTOMER_SUPPORT_NAMESPACE)) {
      FreshdeskTicket freshdeskTicket = freshdeskHelper.convertToFreshdeskTicket(ticketEntry);
      freshdeskTicket = freshdeskService.createTicket(freshdeskTicket);
      return ExternalTicketResponse.builder().id(String.valueOf(freshdeskTicket.getId()))
              .dest(TicketDest.CUSTOMER_SUPPORT).build();
    }
    return null;
  }

  @Override
  public void updateTicket(String id, TicketEntry ticketEntry) {
    FreshdeskTicket freshdeskTicket = freshdeskHelper.convertToFreshdeskTicketForUpdate(ticketEntry);
    freshdeskService.updateTicket(Long.valueOf(id), freshdeskTicket);
    updateTicketStatus(Long.valueOf(id), freshdeskHelper.getFreshdeskTicketStatusMap().get(ticketEntry.getStatus()));
  }

  @Override
  public void updateAssignees(String id, TicketEntry ticketEntry) throws Exception {
    FreshdeskTicket freshdeskTicket = freshdeskHelper.convertToFreshdeskTicketForAssigneeUpdate(ticketEntry);
    freshdeskService.updateTicket(Long.valueOf(id), freshdeskTicket);
  }

  @Override
  public void updateStatus(String id, TicketEntry ticketEntry) {
    updateTicketStatus(Long.valueOf(id), freshdeskHelper.getFreshdeskTicketStatusMap().get(ticketEntry.getStatus()));
  }

  @Override
  public void updateStatusForDest(String id, TicketEntry ticketEntry) {
    Long fdTicketId = Long.valueOf(id);
    FreshdeskTicket freshdeskTicket = freshdeskService.fetchTicket(fdTicketId);
    TicketStatus fdTicketStatus = freshdeskHelper.getStatus(freshdeskTicket.getStatus());
    if (TicketStatus.Closed.equals(fdTicketStatus) || TicketStatus.Resolved.equals(fdTicketStatus)) {

      FreshdeskTicket ticket = new FreshdeskTicket();
      TicketStatus ticketStatus = freshdeskHelper.getOdinFreshdeskTicketStatusMap().get(ticketEntry.getStatus());
      ticket.setStatus(freshdeskHelper.getStatusValue(ticketStatus));

      log.info("updating status of ticket to {} in freshdesk, as freshdesk ticket {} is in closed/resolved status", ticketStatus, id);
      freshdeskService.updateTicket(fdTicketId, ticket);
      log.info("ticket status updated to {} successfully in freshdesk for ticket id {} ", ticketStatus, id);
    }
  }

  @Override
  public Long addComment(String namespace, String id, String message) {
    if (namespace != null && !namespace.equals(CUSTOMER_SUPPORT_NAMESPACE)) {
      Comment comment = freshdeskHelper.convertToFreshdeskComment(message);
      return freshdeskService.addComment(Long.valueOf(id), comment);
    }
    return null;
  }

  @Override
  public Long addPrivateNote(String namespace, String id, String message) {
    if (namespace != null && !namespace.equals(CUSTOMER_SUPPORT_NAMESPACE)) {
      Note note = freshdeskHelper.convertToFreshdeskNote(message);
      return freshdeskService.addPrivateNote(Long.valueOf(id), note);
    }
    return null;
  }

  @Override
  public Long addPrivateNoteAndUpdateStatus(String namespace, String id, TicketEntry ticketEntry, String message) {
    if (namespace != null && !namespace.equals(CUSTOMER_SUPPORT_NAMESPACE)) {
      if (!TICKET_COMPLETE_STATUSES.contains(ticketEntry.getStatus())) {
        updateTicketStatus(Long.valueOf(id), TicketStatus.OpsResponded);
      }
      Note note = freshdeskHelper.convertToFreshdeskNote(message);
      return freshdeskService.addPrivateNote(Long.valueOf(id), note);
    }
    return null;
  }

  @Override
  public boolean isTicketNeedsToCreate(String namespace, TicketEntry ticketEntry) {
    return freshdeskHelper.isTicketNeedsToCreate(ticketEntry);
  }

  private void updateTicketStatus(Long id, TicketStatus ticketStatus) {
    FreshdeskTicket freshdeskTicket = freshdeskService.fetchTicket(id);
    if (TicketStatus.Closed.equals(freshdeskHelper.getStatus(freshdeskTicket.getStatus())) || TicketStatus.Resolved.equals(freshdeskHelper.getStatus(freshdeskTicket.getStatus()))) {
      log.info("Not updating status of ticket to {} in freshdesk, as freshdesk ticket {} is already in closed/resolved status", ticketStatus, id);
      return;
    }
    FreshdeskTicket ticket = new FreshdeskTicket();
    ticket.setStatus(freshdeskHelper.getStatusValue(ticketStatus));
    freshdeskService.updateTicket(id, ticket);
    log.info("ticket status updated to {} successfully in freshdesk for ticket id {} ", ticketStatus, id);
  }

}
