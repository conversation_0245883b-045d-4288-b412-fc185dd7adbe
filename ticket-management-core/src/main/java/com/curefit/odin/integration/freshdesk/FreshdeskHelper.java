package com.curefit.odin.integration.freshdesk;

import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.freshdesk.enums.TicketPriority;
import com.curefit.freshdesk.enums.TicketStatus;
import com.curefit.freshdesk.pojo.*;
import com.curefit.odin.config.OdinConfigurations;
import com.curefit.odin.enums.*;
import com.curefit.odin.integration.freshdesk.service.OdinTypeFDTypeMappingService;
import com.curefit.odin.integrations.pojo.OdinTypeFDTypeMapEntry;
import com.curefit.odin.user.pojo.FieldDataEntry;
import com.curefit.odin.user.pojo.FieldValueEntry;
import com.curefit.odin.user.pojo.TicketEntry;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.curefit.odin.commons.Constants.ODIN_TAG;
import static com.curefit.odin.commons.Constants.SOURCE;

/**
 * <AUTHOR>
 */


@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public abstract class FreshdeskHelper {

  private static final Map<Priority, TicketPriority> PRIORITY_MAP = new HashMap<>();
  private static final Map<Status, TicketStatus> STATUS_MAP = new HashMap<>();
  private static final Map<Status, TicketStatus> FRESHDESK_TICKET_STATUS_MAP = new HashMap<>();
  private static final Map<Status, TicketStatus> ODIN_FRESHDESK_TICKET_STATUS_MAP = new HashMap<>();

  @Autowired
  OdinTypeFDTypeMappingService odinTypeFDTypeMappingService;

  @Autowired
  OdinConfigurations odinConfigurations;

  @Autowired
  ObjectMapper objectMapper;

  @Autowired
  RollbarService rollbarService;

  public FreshdeskHelper() {
    populatePriorities();
    populateStatuses();
  }

  private void populatePriorities() {
    PRIORITY_MAP.put(Priority.NA, TicketPriority.Low);
    PRIORITY_MAP.put(Priority.P10, TicketPriority.Low);
    PRIORITY_MAP.put(Priority.P5, TicketPriority.Medium);
    PRIORITY_MAP.put(Priority.P2, TicketPriority.High);
    PRIORITY_MAP.put(Priority.P0, TicketPriority.Urgent);
  }

  private void populateStatuses() {
    STATUS_MAP.put(Status.OPEN, TicketStatus.Open);
    STATUS_MAP.put(Status.IN_PROGRESS, TicketStatus.Pending);
    STATUS_MAP.put(Status.ON_HOLD, TicketStatus.Pending);
    STATUS_MAP.put(Status.RESOLVED, TicketStatus.Closed);
    STATUS_MAP.put(Status.REJECTED, TicketStatus.Closed);

    FRESHDESK_TICKET_STATUS_MAP.put(Status.OPEN, TicketStatus.WaitingForOps);
    FRESHDESK_TICKET_STATUS_MAP.put(Status.IN_PROGRESS, TicketStatus.WaitingForOps);
    FRESHDESK_TICKET_STATUS_MAP.put(Status.ON_HOLD, TicketStatus.WaitingForOps);
    FRESHDESK_TICKET_STATUS_MAP.put(Status.REJECTED, TicketStatus.OpsResolved);
    FRESHDESK_TICKET_STATUS_MAP.put(Status.RESOLVED, TicketStatus.OpsResolved);

    ODIN_FRESHDESK_TICKET_STATUS_MAP.put(Status.OPEN, TicketStatus.Reopened);
  }

  public FreshdeskTicket convertToFreshdeskTicket(TicketEntry ticketEntry) {
    FreshdeskTicket freshdeskTicket = new FreshdeskTicket();
    String title = ticketEntry.getTitle();
    if (StringUtils.isBlank(title)) {
      if (ticketEntry.getCategoryId() != null) {
        title = ticketEntry.getCategoryName();
        if (ticketEntry.getSubCategoryId() != null) {
          title = title + "-" + ticketEntry.getSubCategoryName();
        }
      }
    }
    freshdeskTicket.setSubject(title);
    freshdeskTicket.setDescription(buildDescription(ticketEntry));
    freshdeskTicket.setTicketPriority(PRIORITY_MAP.get(ticketEntry.getPriority()));
    freshdeskTicket.setEmail(odinConfigurations.getFreshdeskSourceEmail());
    freshdeskTicket.setTags(new ArrayList<String>() {{
      add(SOURCE);
    }});
    freshdeskTicket.setCustomFields(getCustomFields(ticketEntry));
    updateStatus(ticketEntry, freshdeskTicket);
    return freshdeskTicket;
  }

  public FreshdeskTicket convertToFreshdeskTicketForUpdate(TicketEntry ticketEntry) {
    FreshdeskTicket freshdeskTicket = new FreshdeskTicket();
    CustomFields customFields = new CustomFields();
    customFields.setSourceTicketId(String.valueOf(ticketEntry.getId()));

    if (ticketEntry.getAssigneeQueueUsers() != null) {
      List<OdinAssignee> odinAssignees = ticketEntry.getAssigneeQueueUsers().stream()
              .map(user -> new OdinAssignee(user.getName(), user.getEmailId(), user.getMobileNumber()))
              .collect(Collectors.toList());

      try {
        customFields.setOdinAssignees(objectMapper.writeValueAsString(odinAssignees));
      } catch (Exception e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
      }
    }
    customFields.setIsOdinTicket(true);
    freshdeskTicket.setCustomFields(customFields);
    return freshdeskTicket;
  }

  public FreshdeskTicket convertToFreshdeskTicketForAssigneeUpdate(TicketEntry ticketEntry) throws Exception {
    FreshdeskTicket freshdeskTicket = new FreshdeskTicket();
    CustomFields customFields = new CustomFields();
    List<OdinAssignee> odinAssignees = ticketEntry.getAssigneeQueueUsers().stream()
            .map(user -> new OdinAssignee(user.getName(), user.getEmailId(), user.getMobileNumber()))
            .collect(Collectors.toList());
    customFields.setOdinAssignees(objectMapper.writeValueAsString(odinAssignees));
    freshdeskTicket.setCustomFields(customFields);

    return freshdeskTicket;
  }

  public boolean isTicketNeedsToCreate(TicketEntry ticketEntry) {
    if (ticketEntry.getSource() != TicketSource.CUSTOMER_SUPPORT) {
      OdinTypeFDTypeMapEntry odinTypeFDTypeMapEntry = odinTypeFDTypeMappingService.fetchFDTicketType(ticketEntry.getTenantId(), ticketEntry.getCategoryId(), ticketEntry.getSubCategoryId());
      return odinTypeFDTypeMapEntry != null;
    }
    return false;
  }

  public Comment convertToFreshdeskComment(String message) {
    Comment comment = new Comment();
    comment.setBody(appendOdinTag(message));
    return comment;
  }

  public Note convertToFreshdeskNote(String message) {
    Note note = new Note();
    note.setBody(appendOdinTag(message));
    return note;
  }

  private CustomFields getCustomFields(TicketEntry ticketEntry) {
    OdinTypeFDTypeMapEntry odinTypeFDTypeMapEntry = odinTypeFDTypeMappingService.fetchFDTicketType(ticketEntry.getTenantId(), ticketEntry.getCategoryId(), ticketEntry.getSubCategoryId());
    CustomFields customFields = new CustomFields();
    customFields.setSourceTicketId(String.valueOf(ticketEntry.getId()));
    customFields.setIsOdinTicket(true);
    if (odinTypeFDTypeMapEntry != null) {
      customFields.setCfType(odinTypeFDTypeMapEntry.getFdCFType());
      customFields.setLevel1(odinTypeFDTypeMapEntry.getFdLevel1());
      customFields.setLevel2(odinTypeFDTypeMapEntry.getFdLevel2());
      customFields.setLevel3(odinTypeFDTypeMapEntry.getFdLevel3());
    }

    return customFields;
  }

  private String buildDescription(TicketEntry ticketEntry) {
    String location = ticketEntry.getLocationEntry() != null ? ticketEntry.getLocationEntry().getCenterName() : "";

    StringBuilder description = new StringBuilder("<b>Description : </b>" + ticketEntry.getDescription() + "<br />"
            + "<b>Location : </b>" + location + "<br />"
            + "<b>Due Date : </b>" + (ticketEntry.getDueDate() != null ? ticketEntry.getDueDate() : "") + "<br />"
            + "<b>Priority : </b>" + (ticketEntry.getPriority() != null ? ticketEntry.getPriority().getName() : "") + "<br />");

    try {
      List<FieldDataEntry> fieldData = ticketEntry.getFields();

      if (fieldData != null) {

        fieldData.forEach(field -> {
          description.append("<b>").append(field.getKey().getValue()).append(": </b>");

          if (field.getKey().getDataType() == DataType.LIST) {
            List<FieldValueEntry> fieldValues = ((List<FieldValueEntry>)field.getValue());
            if (CollectionUtils.isNotEmpty(fieldValues)) {
              description.append(fieldValues.get(0).getValue());
            }
          } else {
            description.append(field.getValue());
          }
          description.append("<br />");
        });
      }
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }

    return description.toString();
  }

  public Map<Priority, TicketPriority> getPriorityMap() {
    return PRIORITY_MAP;
  }

  public Integer getStatusValue(TicketStatus ticketStatus) {
    return getFreshdeskTicketStatusMapper().getValue(ticketStatus);
  }

  public TicketStatus getStatus(Integer value) {
    return getFreshdeskTicketStatusMapper().getStatus(value);
  }

  public Map<Status, TicketStatus> getStatusMap() {
    return STATUS_MAP;
  }

  public Map<Status, TicketStatus> getFreshdeskTicketStatusMap() {
    return FRESHDESK_TICKET_STATUS_MAP;
  }

  public Map<Status, TicketStatus> getOdinFreshdeskTicketStatusMap() {
    return ODIN_FRESHDESK_TICKET_STATUS_MAP;
  }

  public abstract FreshdeskTicketStatusMapper getFreshdeskTicketStatusMapper();

  private void updateStatus(TicketEntry ticketEntry, FreshdeskTicket freshdeskTicket) {
    if (StringUtils.isBlank(ticketEntry.getSourceRefId())) {
      freshdeskTicket.setStatus(getStatusValue(STATUS_MAP.get(ticketEntry.getStatus())));
    } else {
      freshdeskTicket.setStatus(getStatusValue(FRESHDESK_TICKET_STATUS_MAP.get(ticketEntry.getStatus())));
    }
  }

  private String appendOdinTag(String message) {
    return ODIN_TAG + message;
  }
}