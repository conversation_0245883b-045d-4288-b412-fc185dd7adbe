package com.curefit.odin.integration.freshdesk.models;

import com.curefit.odin.admin.models.BaseMySQLModel;
import com.curefit.odin.enums.Priority;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.hibernate.annotations.Where;

import javax.persistence.*;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Where(clause = "active = 1")
@Table(name = "fd_type_odin_type_map", uniqueConstraints = {@UniqueConstraint(columnNames = {"fd_cf_type", "fd_level1", "fd_level2", "fd_level3"})})
public class FDTypeOdinTypeMap extends BaseMySQLModel {

    /**
     *
     */
    private static final long serialVersionUID = 8885620686216455912L;

    @Column(name = "fd_cf_type")
    String fdCFType;

    @Column(name = "fd_level1")
    String fdLevel1;

    @Column(name = "fd_level2")
    String fdLevel2;

    @Column(name = "fd_level3")
    String fdLevel3;

    @Column(name = "tenant_id")
    Long tenantId;

    @Column(name = "category_id")
    Long categoryId;

    @Column(name = "sub_category_id")
    Long subCategoryId;

    @Enumerated(EnumType.STRING)
    Priority priority;

    @Column(name = "mapping_type")
    String mappingType;
}
