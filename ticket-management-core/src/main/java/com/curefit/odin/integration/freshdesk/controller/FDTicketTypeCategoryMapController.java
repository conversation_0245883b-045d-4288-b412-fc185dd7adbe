package com.curefit.odin.integration.freshdesk.controller;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.curefit.common.data.enums.AppStatus;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.sf.controller.BaseController;
import com.curefit.odin.integration.freshdesk.models.FDTypeOdinTypeMap;
import com.curefit.odin.integration.freshdesk.service.FDTicketTypeCatMapService;
import com.curefit.odin.integrations.pojo.FDTicketTypeCategoryMapEntry;

import java.util.List;


@RestController
@RequestMapping("/fd_ticket_map")
public class FDTicketTypeCategoryMapController
    extends BaseController<FDTypeOdinTypeMap, FDTicketTypeCategoryMapEntry> {

  public FDTicketTypeCategoryMapController(FDTicketTypeCatMapService fdTicketMapService) {
    super(fdTicketMapService);
  }

  @RequestMapping(method = RequestMethod.GET)
  public ResponseEntity<FDTicketTypeCategoryMapEntry> fetchOdinType(@RequestParam String cfType,
      @RequestParam(required=false) String level1, @RequestParam(required=false) String level2, @RequestParam(required=false) String level3, @RequestParam(defaultValue = "FRESHDESK") String mappingType)
      throws BaseException {
    if (cfType == null) {
      throw new BaseException("cfType must not be null", AppStatus.BAD_REQUEST);
    }
    return new ResponseEntity<>(((FDTicketTypeCatMapService) baseMySQLService).fetchOdinType(cfType,
        level1, level2, level3, mappingType), HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET, value = "/cf_type")
  public ResponseEntity<List<String>> fetchAllCfTypes() {
    return new ResponseEntity<>(((FDTicketTypeCatMapService) baseMySQLService).fetchAllCfTypes(), HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET, value = "/level1")
  public ResponseEntity<List<String>> fetchAllLevel1(@RequestParam String cfType) {
    return new ResponseEntity<>(((FDTicketTypeCatMapService) baseMySQLService).fetchAllLevel1(cfType), HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET, value = "/level2")
  public ResponseEntity<List<String>> fetchAllLevel2(@RequestParam String cfType, @RequestParam String level1) {
    return new ResponseEntity<>(((FDTicketTypeCatMapService) baseMySQLService).fetchAllLevel2(cfType, level1), HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET, value = "/level3")
  public ResponseEntity<List<String>> fetchAllLevel3(@RequestParam String cfType, @RequestParam String level1, @RequestParam String level2) {
    return new ResponseEntity<>(((FDTicketTypeCatMapService) baseMySQLService).fetchAllLevel3(cfType, level1, level2), HttpStatus.OK);
  }
}
