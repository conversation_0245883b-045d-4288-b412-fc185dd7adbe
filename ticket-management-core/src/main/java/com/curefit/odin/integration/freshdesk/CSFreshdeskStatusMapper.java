package com.curefit.odin.integration.freshdesk;


import com.curefit.freshdesk.pojo.FreshdeskTicketStatusMapper;
import org.springframework.stereotype.Component;

import static com.curefit.freshdesk.enums.TicketStatus.*;

/**
 * <AUTHOR>
 */

@Component
public class CSFreshdeskStatusMapper extends FreshdeskTicketStatusMapper {

    public CSFreshdeskStatusMapper() {
        addStatusMapping(Open, 2);
        addStatusMapping(Pending, 3);
        addStatusMapping(Resolved, 4);
        addStatusMapping(Closed, 5);
        addStatusMapping(WaitingForOps, 8);
        addStatusMapping(OpsResponded, 14);
        addStatusMapping(OpsResolved, 15);
        addStatusMapping(Reopened, 16);
    }
}
