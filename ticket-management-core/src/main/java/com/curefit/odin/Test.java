/**
 * 
 */
package com.curefit.odin;

import com.curefit.odin.utils.pojo.LocationEntry;
import com.curefit.odin.utils.pojo.LocationHierarchyNode;
import com.curefit.odin.utils.service.LocationHierarchyService;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.fasterxml.jackson.dataformat.csv.CsvSchema;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.*;

import static com.curefit.odin.enums.LocationType.CITY;
import static com.curefit.odin.enums.LocationType.CLUSTER;

/**
 * <AUTHOR>
 *
 */
public class Test {

  public static void main(String[] args) {
    Test test = new Test();
    try {
      test.test();
    } catch (IOException e) {
      // TODO Auto-generated catch block
      e.printStackTrace();
    }
    System.out.println("Done");

    LocationEntry locationEntry = new LocationEntry();
    locationEntry.setId(12L);
    locationEntry.setCenterId("123");
    locationEntry.setClusterId("12121");
    locationEntry.setCityId("2121");
    locationEntry.setCountryId("1");

    LocationHierarchyService locationHierarchyService = new LocationHierarchyService();
    List<LocationHierarchyNode> nodes = locationHierarchyService.getAllLocationNodes(locationEntry);
    System.out.println(nodes);
  }

  private void test() throws IOException {
    File tempFile = File.createTempFile(UUID.randomUUID().toString(), ".csv");



    // Writer writer = new FileWriter(tempFile, true);
    List<Map<String, Object>> csvData = new ArrayList<Map<String, Object>>();
    Map<String, Object> obj1 = new HashMap<String, Object>();
    obj1.put("col1", "value1");
    obj1.put("col2", "value12");
    csvData.add(obj1);
    CsvMapper mapper = new CsvMapper();
    // CsvSchema schema = mapper.schemaFor(csvData.get(0).keySet());
    // schema = schema.withColumnSeparator('\t').withUseHeader(true);


    BufferedWriter successWriter = new BufferedWriter(new FileWriter(tempFile));
    csvWriter(mapper, csvData, successWriter, true);
    // writer.close();
    System.out.println("File written: " + tempFile.getAbsolutePath());
  }

  private void csvWriter(CsvMapper mapper, List<Map<String, Object>> listOfMap,
      BufferedWriter fileWriter, boolean isFirst) throws IOException {
    CsvSchema schema = null;
    CsvSchema.Builder schemaBuilder = CsvSchema.builder();
    if (isFirst) {
      if (listOfMap != null && !listOfMap.isEmpty()) {
        for (String col : listOfMap.get(0).keySet()) {
          schemaBuilder.addColumn(col);
        }
        schema = schemaBuilder.build().withLineSeparator("\n").withHeader();
      }
    }
    ObjectWriter writer = mapper.writer(schema);
    writer.writeValues(fileWriter).writeAll(listOfMap);
  }


}
