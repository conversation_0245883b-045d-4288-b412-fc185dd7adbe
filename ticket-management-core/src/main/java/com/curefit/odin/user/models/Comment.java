package com.curefit.odin.user.models;

import java.util.List;
import javax.persistence.*;

import com.curefit.cf.commons.pojo.crypto.EncryptionMode;
import com.curefit.commons.sf.audit.annotation.*;
import com.curefit.odin.admin.models.BaseMySQLModel;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.hibernate.annotations.Type;

@Getter
@Setter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Audit
@Table(name = "comment")
@EnableEncryption(mode = EncryptionMode.CONFIDENTIAL)
public class Comment extends BaseMySQLModel {

  /**
  * 
  */
  private static final long serialVersionUID = -7473421179593823506L;

  @Encrypt
  String comment;

  @IgnoreAudit
  @ManyToOne
  @JoinColumn(name = "parent_comment_id", referencedColumnName = "id")
  Comment parentComment;

  @AuditParent(parentId = "id")
  @ManyToOne
  @JoinColumn(name = "ticket_id", referencedColumnName = "id")
  Ticket ticket;

  @IgnoreAudit
  @OneToMany(cascade = CascadeType.ALL)
  @JoinColumn(name = "comment_id", referencedColumnName = "id")
  List<Attachment> attachments;

  @IgnoreAudit
  @Type(type = "json")
  @Column(columnDefinition = "json")
  List<String> mentions;

  @Column(name = "is_internal")
  Boolean isInternal;
}
