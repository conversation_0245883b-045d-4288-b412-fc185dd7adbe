package com.curefit.odin.user.service;

import com.curefit.cf.commons.pojo.PagedResultEntry;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.mozart.MozartClient;
import com.curefit.mozart.enums.JobStatus;
import com.curefit.mozart.pojo.JobEntry;
import com.curefit.odin.enums.AuthType;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import com.curefit.odin.user.pojo.FieldDataEntry;
import com.curefit.odin.user.pojo.TicketEntry;
import com.curefit.odin.user.pojo.TicketFilterRequestEntry;
import com.curefit.odin.utils.AsyncService;
import com.curefit.odin.utils.AuthService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.fasterxml.jackson.dataformat.csv.CsvSchema;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;
import java.util.*;

/**
 * <AUTHOR>
 */

@Slf4j
@Component
public class TicketReportService {

  @Autowired
  TicketFilterService ticketFilterService;

  @Autowired
  FieldDataService fieldDataService;

  @Autowired
  AuthService authService;

  @Autowired
  MozartClient mozartClient;

  @Autowired
  RollbarService rollbarService;

  @Autowired
  ObjectMapper objectMapper;

  public JobEntry download(String userId, String tenantId, TicketFilterRequestEntry request)
          throws BaseException {
    request.setOffset(0);
    request.setLimit(1);
    List<BaseOdinEntry> authContexts = authService.getContexts(AuthType.LOCATION);
    PagedResultEntry<Long, TicketEntry> filteredData = ticketFilterService.filter(userId, tenantId, request, authContexts);
    if (filteredData.getTotalElements() == 0) {
      log.info("No result found for given filters");
      throw new BaseException("No content");
    } else {
      int stepCount = (int) ((filteredData.getTotalElements() / 100) + 4);
      JobEntry jobToCreate = new JobEntry();
      jobToCreate.setCreatedBy(userId);
      jobToCreate.setSource("odin");
      jobToCreate.setStatus(JobStatus.CREATED);
      jobToCreate.setJobConfigId("odin_download");
       jobToCreate.setMetaData(objectMapper.convertValue(request, JsonNode.class));
      jobToCreate.setTotalSteps(stepCount);
      jobToCreate.setCompletedSteps(0);
      JobEntry createdJob = mozartClient.createJob(jobToCreate);
      log.info("Submitting job");
      AsyncService.submit(() -> aggregateTickets(createdJob, request, userId, tenantId, authContexts));
      log.info("returning job");
      return createdJob;
    }
  }

  /**
   * @param request
   * @param createdJob
   * @param tenantId
   * @param userId
   */
  private void aggregateTickets(JobEntry createdJob,
                                TicketFilterRequestEntry request, String userId, String tenantId, List<BaseOdinEntry> authContexts) {
    try {
      request.setOffset(0);
      request.setLimit(100);
      PagedResultEntry<Long, TicketEntry> filteredData = ticketFilterService.filter(userId, tenantId, request, authContexts);
      File tempFile = File.createTempFile(UUID.randomUUID().toString(), ".tmp");
      File dumpFile = File.createTempFile("odin_tickets_" + UUID.randomUUID().toString(), ".csv");
      BufferedWriter writer = new BufferedWriter(new FileWriter(dumpFile));
      long count = filteredData.getTotalElements();
      log.info("total rows = " + count);
      Set<String> headers = new LinkedHashSet<>();
      BufferedWriter tempFileWriter = new BufferedWriter(new FileWriter(tempFile));

      do {
        List<TicketEntry> data = filteredData.getElements();
        List<Map<String, Object>> csvData = new ArrayList<>();
        data.forEach(d -> updateData(headers, csvData, d));
        for (Map<String, Object> csvDatum : csvData) {
          tempFileWriter.write(objectMapper.writeValueAsString(csvDatum));
          tempFileWriter.newLine();
        }
        request.setOffset(request.getOffset() + request.getLimit());
        filteredData = ticketFilterService.filter(userId, tenantId, request, authContexts);
      } while (filteredData.getElements().size() > 0);
      tempFileWriter.close();
      log.info("Dumped file to temp File:" + tempFile.getAbsolutePath());

      // Write to CSV File
      CsvMapper mapper = new CsvMapper();
      log.info("Headers=" + headers);
      // Write Header
      createCSVHeader(mapper, headers);

      // Dump TempFile date to CSV
      BufferedReader tempFileReader = new BufferedReader(new FileReader(tempFile));
      String line = tempFileReader.readLine();
      List<Map<String, Object>> dataToDump = new ArrayList<>();
      boolean isFirst = true;
      while (line != null) {
        Map<String, Object> csvData =
                objectMapper.readValue(line, new TypeReference<Map<String, Object>>() {
                });

        Map<String, Object> modifiedCSVData = new HashMap<>();
        for (String key : headers) {
          modifiedCSVData.put(key, csvData.get(key));
        }
        dataToDump.add(modifiedCSVData);
        if (dataToDump.size() >= 100) {
          csvWriter(mapper, dataToDump, writer, isFirst);
          isFirst = false;
          dataToDump.clear();
        }
        line = tempFileReader.readLine();
      }
      csvWriter(mapper, dataToDump, writer, isFirst);
      dataToDump.clear();
      try {
        mozartClient.incrementStep(createdJob.getId());
      } catch (BaseException e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
      }
      tempFileReader.close();
      writer.close();
      try {
        mozartClient.uploadSuccessFile(createdJob.getId(), dumpFile);
        mozartClient.markJobAsSuccess(createdJob.getId());
      } catch (BaseException e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
      }
    } catch (Throwable e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
      try {
        mozartClient.markJobAsFailure(createdJob.getId());
      } catch (BaseException e1) {
        log.error(e1.getMessage(), e1);
        rollbarService.error(e1);
      }
    }


  }

  /**
   * @param headers
   * @param csvData
   * @param d
   * @return
   */
  private void updateData(Set<String> headers, List<Map<String, Object>> csvData, TicketEntry d) {

    List<FieldDataEntry> customFieldDataEntryList =
            fieldDataService.findFieldDataByTicketId(d.getId());
    d.setFields(customFieldDataEntryList);
    headers.addAll(d.getCSVContent().keySet());
    csvData.add(d.getCSVContent());
  }

  private void createCSVHeader(CsvMapper mapper, Set<String> headers) {
    CsvSchema schema;
    CsvSchema.Builder schemaBuilder = CsvSchema.builder();

    for (String col : headers) {
      schemaBuilder.addColumn(col);
    }

    schema = schemaBuilder.build().withLineSeparator("\n").withHeader();
    mapper.writer(schema);
    log.info("Written headers:" + headers);
  }

  private void csvWriter(CsvMapper mapper, List<Map<String, Object>> listOfMap,
                         BufferedWriter fileWriter, boolean isFirst) throws IOException {
    CsvSchema schema = null;
    log.info("Writing " + listOfMap);
    CsvSchema.Builder schemaBuilder = CsvSchema.builder();

    if (!listOfMap.isEmpty()) {
      for (String col : listOfMap.get(0).keySet()) {
        schemaBuilder.addColumn(col);
      }
      if (isFirst) {
        schema = schemaBuilder.build().withLineSeparator("\n").withHeader();
      } else {
        schema = schemaBuilder.build().withLineSeparator("\n").withoutHeader();
      }
    }
    ObjectWriter writer = mapper.writer(schema);
    writer.writeValues(fileWriter).writeAll(listOfMap);
    log.info("Written list");
  }
}
