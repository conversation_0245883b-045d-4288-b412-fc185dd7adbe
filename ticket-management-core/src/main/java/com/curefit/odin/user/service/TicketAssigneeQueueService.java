package com.curefit.odin.user.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.user.models.TicketAssigneeQueueMap;
import com.curefit.odin.user.pojo.TicketAssigneeQueueEntry;
import com.curefit.odin.user.pojo.TicketWatcherEntry;
import com.curefit.odin.user.repositories.TicketAssigneeQueueDAO;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TicketAssigneeQueueService extends BaseMySQLService<TicketAssigneeQueueMap, TicketAssigneeQueueEntry> {

    @Autowired
    TicketWatcherService ticketWatcherService;

    @Autowired
    RollbarService rollbarService;

    public TicketAssigneeQueueService(TicketAssigneeQueueDAO ticketAssigneeQueueDAO) {
        super(ticketAssigneeQueueDAO);
    }

    public void addAssigneesToTicket(Long ticketId, Set<String> assigneeIds) throws BaseException {
        bulkCreate(assigneeIds.stream()
                .map(assigneeId -> new TicketAssigneeQueueEntry(ticketId, assigneeId))
                .collect(Collectors.toList()));
    }

    public void updateAssigneesToTicket(Long ticketId, Set<String> assigneeIds) throws BaseException {
        List<TicketAssigneeQueueMap> ticketAssigneeQueueMaps = ((TicketAssigneeQueueDAO) baseMySQLRepository).findAllByTicketId(ticketId);

        baseMySQLRepository.deleteAll(ticketAssigneeQueueMaps.stream()
                .filter(ticketAssigneeQueueMap -> !assigneeIds.contains(ticketAssigneeQueueMap.getAssigneeId()))
                .collect(Collectors.toList()));

        Set<String> assigneesAlreadyPresent = ticketAssigneeQueueMaps.stream()
                .map(TicketAssigneeQueueMap::getAssigneeId)
                .collect(Collectors.toSet());

        List<String> filteredAssignees = assigneeIds.stream()
                .filter(assigneeId -> !assigneesAlreadyPresent.contains(assigneeId))
                .collect(Collectors.toList());

        bulkCreate(filteredAssignees.stream().map(assigneeId -> new TicketAssigneeQueueEntry(ticketId, assigneeId))
                .collect(Collectors.toList()));

        filteredAssignees.stream()
                .filter(assigneeId -> !StringUtils.isBlank(assigneeId))
                .map(assigneeId -> new TicketWatcherEntry(ticketId, assigneeId))
                .forEach(ticketWatcherEntry -> {
                    try {
                        ticketWatcherService.createOrUpdate(ticketWatcherEntry);
                    } catch (BaseException e) {
                        //logging not required, happening in calling method
                    }
                });
    }

    private List<TicketAssigneeQueueEntry> fetchAllByAssigneeIds(Set<String> assigneeIds) {
        try {
            log.debug("assignee ids {}", assigneeIds);
            return search(0, -1, null, null, "assigneeId.in:" + StringUtils.join(assigneeIds, ",")).getElements();
        } catch (InvalidSeachQueryException e) {
            log.error(e.getMessage(), e);
            rollbarService.error(e);
        }
        return new ArrayList<>();
    }

    public List<Long> fetchAllTicketsByAssigneeIds(Set<String> assigneeIds) {
        return fetchAllByAssigneeIds(assigneeIds).stream()
                .map(TicketAssigneeQueueEntry::getTicketId)
                .collect(Collectors.toList());
    }
}
