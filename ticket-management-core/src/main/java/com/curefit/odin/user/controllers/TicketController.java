package com.curefit.odin.user.controllers;

import com.curefit.cf.commons.pojo.PagedResultEntry;
import com.curefit.common.data.exception.BaseException;
import com.curefit.freshdesk.enums.TicketStatus;
import com.curefit.mozart.pojo.JobEntry;
import com.curefit.odin.admin.controllers.BaseOdinController;
import com.curefit.odin.audit.pojo.TicketRevision;
import com.curefit.odin.enums.Priority;
import com.curefit.odin.enums.Status;
import com.curefit.odin.enums.TicketSource;
import com.curefit.odin.user.models.Ticket;
import com.curefit.odin.user.pojo.*;
import com.curefit.odin.user.service.*;
import com.curefit.odin.utils.ApiKeyValidator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/ticket")
@Slf4j
public class TicketController extends BaseOdinController<Ticket, TicketEntry> {

    @Autowired
    TicketHistoryService ticketHistoryService;

    @Autowired
    TicketReportService ticketReportService;

    @Autowired
    TicketFilterService ticketFilterService;

    @Autowired
    FreshdeskUpdatesHandler freshdeskUpdatesHandler;

    @Autowired
    ApiKeyValidator apiKeyValidator;

    @Autowired
    SLAReminderService slaReminderService;

    public TicketController(TicketService ticketService) {
        super(ticketService);
    }

    @RequestMapping(method = RequestMethod.GET, value = "/sla-reminder/callback")
    public ResponseEntity<Void> slaReminderCallback() throws BaseException {
        this.slaReminderService.handleSLAReminderCallback();
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.PATCH, value = "/category")
    public ResponseEntity<TicketEntry> updateTicketCategory(@RequestBody TicketEntry ticketEntry) throws BaseException {
        if (ticketEntry.getId() == null) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<>(((TicketService) baseMySQLService).updateTicketCategory(ticketEntry), HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.POST, value = "/refresh")
    public ResponseEntity<List<TicketEntry>> refreshTickets(@RequestParam(value = "ticketIds") List<Long> ticketIds,
                                                            @RequestParam(value = "updateAssignee", defaultValue = "true") Boolean updateAssignee,
                                                            @RequestParam(value = "updateDueDate", defaultValue = "false") Boolean updateDueDate) throws BaseException {
        return new ResponseEntity<>(((TicketService) baseMySQLService).refreshTickets(ticketIds, updateAssignee, updateDueDate), HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.GET, value = "/{id}/nextStatus")
    public ResponseEntity<List<Status>> getNextPossibleTransitions(@PathVariable Long id) throws BaseException, IOException {
        return new ResponseEntity<>(((TicketService) baseMySQLService).findNextStatus(id), HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.PATCH, value = "/{id}/status/")
    public ResponseEntity<TicketEntry> updateStatus(@PathVariable Long id, @RequestParam String newStatus) throws BaseException {
        return new ResponseEntity<>(((TicketService) baseMySQLService).updateStatus(id, Status.get(newStatus)), HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.POST, value = "/bulk/status/update")
    public ResponseEntity<?> updateBulkTicketStatus(@RequestBody BulkUpdateTicketStatusRequest request) throws BaseException {
        log.info("Received bulk update request: {}", request.toString());
        ((TicketService) baseMySQLService).bulkUpdateStatus(request);
        log.info("Status update successful");
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.PATCH, value = "/external/{id}/status/")
    public ResponseEntity<TicketEntry> updateStatusFromExternalSource(@RequestHeader("apiKey") String apiKey, @PathVariable Long id,
                                                                      @RequestParam String newStatus) throws BaseException {
        apiKeyValidator.validateApiKey(apiKey);
        return new ResponseEntity<>(
                ((TicketService) baseMySQLService).updateStatus(id, Status.get(newStatus)), HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.PATCH, value = "/bulk/assignees")
    public ResponseEntity<Integer> bulkUpdateAssignees(@RequestHeader("X_USER_ID") String userId, @RequestHeader(value = "X_TENANT_ID", required = false) String tenantId,
                                                       @RequestBody BulkUpdateTicketAssigneeRequest request) throws BaseException {
        int updatedCount = ticketFilterService.bulkUpdateAssignees(userId, tenantId, request);
        return new ResponseEntity<>(updatedCount, HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.PATCH, value = "/{id}/assignees")
    public ResponseEntity<TicketEntry> updateAssignees(@PathVariable Long id, @RequestBody List<String> userIds) throws BaseException {
        return new ResponseEntity<>(((TicketService) baseMySQLService).updateAssignees(id, userIds), HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.GET, value = "/priorities")
    public ResponseEntity<List<Priority>> getPriorities() {
        List<Priority> priorities = new ArrayList<>(Arrays.asList(Priority.values()));
        priorities.remove(Priority.NA);
        return new ResponseEntity<>(priorities, HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.GET, value = "/sources")
    public ResponseEntity<List<TicketSource>> getTicketSources() {
        return new ResponseEntity<>(Arrays.asList(TicketSource.values()), HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.POST, value = "/create/v2")
    public ResponseEntity<TicketEntry> createV2(@RequestBody TicketEntry payload) throws BaseException {
        return new ResponseEntity<>(((TicketService) baseMySQLService).createV2(payload), HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.POST, value = "/filter")
    public ResponseEntity<PagedResultEntry<Long, TicketEntry>> filter(
            @RequestHeader("X_USER_ID") String userId, @RequestHeader(value = "X_TENANT_ID", required = false) String tenantId,
            @RequestBody TicketFilterRequestEntry request) throws BaseException {
        log.debug("userId: {}, tenantId: {}, request: {}", userId, tenantId, request);
        return new ResponseEntity<>((ticketFilterService.filter(userId, tenantId, request)), HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.POST, value = "/download")
    public ResponseEntity<JobEntry> download(@RequestHeader("X_USER_ID") String userId,
                                             @RequestHeader("X_TENANT_ID") String tenantId,
                                             @RequestBody TicketFilterRequestEntry request) throws BaseException {

        if (StringUtils.isBlank(tenantId)) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<>(ticketReportService.download(userId, tenantId, request), HttpStatus.OK);
    }


    @RequestMapping(method = RequestMethod.GET, value = "{id}/detail")
    public ResponseEntity<TicketEntry> fetchDetail(@PathVariable Long id) throws BaseException {
        return new ResponseEntity<>(((TicketService) baseMySQLService).fetchDetail(id), HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.GET, value = "{id}/history")
    public ResponseEntity<List<TicketRevision>> fetchHistory(@PathVariable Long id) throws BaseException {
        return new ResponseEntity<>(ticketHistoryService.fetchHistory(id), HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.DELETE, value = "{id}/label")
    public ResponseEntity<TicketEntry> deleteLabel(@PathVariable Long id, @RequestParam String label)
            throws BaseException {
        return new ResponseEntity<>(((TicketService) baseMySQLService).deleteLabelByTicketId(id, label),
                HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.PATCH, value = "{id}/label")
    public ResponseEntity<TicketEntry> addLabel(@PathVariable Long id, @RequestParam String label)
            throws BaseException {
        return new ResponseEntity<>(((TicketService) baseMySQLService).addLabelByTicketId(id, label),
                HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.PATCH, value = "/external/{fd_ticket_id}/fd_status/")
    public ResponseEntity<?> fdStatusUpdate(@RequestHeader("apiKey") String apiKey, @PathVariable String fd_ticket_id,
                                            @RequestParam TicketStatus status, @RequestParam(required = false) String freshdeskNamespace) throws BaseException {
        apiKeyValidator.validateApiKey(apiKey);
        freshdeskUpdatesHandler.handleFDTicketStatusUpdate(fd_ticket_id, status, freshdeskNamespace);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.PATCH, value = "/{id}/rating/")
    public ResponseEntity<TicketEntry> rateTicketResolution(@PathVariable Long id,
                                                            @RequestBody RateTicketResolutionRequest request) throws BaseException {
        return new ResponseEntity<>(
                ((TicketService) baseMySQLService).rateTicketResolution(id, request.getRating()), HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.POST, value = "/sla_reminder_for_next_day")
    public ResponseEntity<?> SLAAboutToBreachTomorrow() throws BaseException {
        ((TicketService) baseMySQLService).sendLSAAboutToBreachReminderForTicketsHavingDueDateAsTomorrow();
        return new ResponseEntity<>(HttpStatus.OK);

    }
}
