package com.curefit.odin.user.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.pojo.UserEntry;
import com.curefit.odin.admin.service.UserService;
import com.curefit.odin.user.models.TicketWatcher;
import com.curefit.odin.user.pojo.CommentEntry;
import com.curefit.odin.user.pojo.TicketEntry;
import com.curefit.odin.user.pojo.TicketWatcherEntry;
import com.curefit.odin.user.repositories.TicketWatcherDAO;
import com.curefit.odin.utils.AuthService;
import com.curefit.odin.utils.EmailValidator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TicketWatcherService extends BaseMySQLService<TicketWatcher, TicketWatcherEntry> implements TicketCacheService<TicketWatcherEntry> {

    @Autowired
    TicketService ticketService;

    @Autowired
    CommentService commentService;

    @Autowired
    UserService userService;

    @Autowired
    RollbarService rollbarService;

    @Autowired
    AuthService authService;

    @Autowired
    EmailValidator emailValidator;

    public TicketWatcherService(TicketWatcherDAO ticketWatcherDAO) {
        super(ticketWatcherDAO);
    }

    @Override
    public TicketWatcherEntry convertToEntry(TicketWatcher ticketWatcher) {
        TicketWatcherEntry ticketWatcherEntry = super.convertToEntry(ticketWatcher);
        if (ticketWatcher.getTicket() != null) {
            ticketWatcherEntry.setTicketId(ticketWatcher.getTicket().getId());
        }
        try {
            ticketWatcherEntry.setUser(userService.findUserByMailId(ticketWatcherEntry.getUserId()));
        } catch (BaseException e) {
            log.error(e.getMessage(), e);
            rollbarService.error(e);
        }
        return ticketWatcherEntry;
    }

    @Override
    public TicketWatcher convertToEntity(TicketWatcherEntry ticketWatcherEntry) {
        TicketWatcher convertedEntity = super.convertToEntity(ticketWatcherEntry);
        if (ticketWatcherEntry.getTicketId() != null) {
            try {
                convertedEntity.setTicket(ticketService.fetchEntityById(ticketWatcherEntry.getTicketId()));
            } catch (BaseException e) {
                log.error(e.getMessage(), e);
                rollbarService.error(e);
            }
        }
        return convertedEntity;
    }


    @Transactional(rollbackFor = Exception.class)
    public TicketWatcherEntry deleteById(Long id) throws BaseException {
        log.info("delete ticket watcher by id {}", id);
        authService.checkAuthorized(id, this);
        TicketWatcherEntry ticketWatcherEntry = new TicketWatcherEntry();
        ticketWatcherEntry.setActive(false);
        ticketWatcherEntry = super.patchUpdate(id, ticketWatcherEntry);
        updateLastModified(ticketWatcherEntry);
        return ticketWatcherEntry;
    }


    @Override
    public TicketWatcherEntry create(TicketWatcherEntry entry) throws BaseException {
        authService.checkAuthorized(entry, this);
        return createOrUpdate(entry);
    }

    public TicketWatcherEntry createOrUpdate(TicketWatcherEntry entry) throws BaseException {
        try {
            List<TicketWatcherEntry> existingWatcher = search(0, 1, null, null,
                    "userId.eq:" + entry.getUserId() + ";ticket.id.eq:" + entry.getTicketId()).getElements();
            if (existingWatcher.isEmpty()) {
                return super.create(entry);
            } else {
                TicketWatcherEntry watcher = existingWatcher.getFirst();
                if (watcher.getActive() != null && watcher.getActive()) {
                    return watcher;
                }
                watcher.setActive(true);
                return super.patchUpdate(watcher.getId(), watcher);
            }
        } catch (InvalidSeachQueryException e) {
            log.error(e.getMessage(), e);
            rollbarService.error(e);
            throw new BaseException(e);
        }
    }

    public List<TicketWatcherEntry> findByTicketId(Long id) {
        log.info("Fetching watchers by id {}", id);
        try {
            return search(0, -1, null, null, "ticket.id.eq:" + id + ";active.eq:true").getElements();
        } catch (InvalidSeachQueryException e) {
            log.error(e.getMessage(), e);
            rollbarService.error(e);
        }
        return new ArrayList<>();
    }

    public void createTicketWatchers(Long ticketId, Set<String> watchers) throws BaseException {
        Set<String> existingWatchers = findByTicketId(ticketId).stream().map(TicketWatcherEntry::getUserId).collect(Collectors.toSet());
        Set<String> watcherToBeCreated = watchers.stream()
                .filter(watcher -> !existingWatchers.contains(watcher))
                .collect(Collectors.toSet());
        bulkCreate(watcherToBeCreated.stream()
                .map(watcherId -> new TicketWatcherEntry(ticketId, watcherId))
                .collect(Collectors.toList()));
    }

    public List<Long> findTicketsByUserIds(Set<String> userIds) {
        try {
            List<TicketWatcherEntry> entries = search(0, -1, null, null, "userId.in:" + StringUtils.join(userIds, ",") + ";active.eq:true").getElements();
            return entries.stream().map(TicketWatcherEntry::getTicketId).collect(Collectors.toList());
        } catch (InvalidSeachQueryException e) {
            log.error(e.getMessage(), e);
            rollbarService.error(e);
        }
        return Collections.emptyList();
    }

    public List<TicketWatcherEntry> fetchByTicketId(Long id) throws BaseException {
        authService.checkAuthorized(id, ticketService);
        return findByTicketId(id);
    }

    @Override
    public TicketWatcherEntry patchUpdate(Long id, TicketWatcherEntry entry) throws BaseException {
        throw new BaseException("Not supported");
    }

    private void updateLastModified(TicketWatcherEntry ticketWatcherEntry) throws BaseException {
        TicketEntry ticketEntry = new TicketEntry();
        ticketEntry.setLastModifiedOn(ticketWatcherEntry.getLastModifiedOn());
        ticketService.patchUpdate(ticketWatcherEntry.getTicketId(), ticketEntry);
    }

    public Set<String> findAllUsersToUpdate(Long ticketId) {
        try {
            List<TicketWatcherEntry> watcherEntries = findByTicketId(ticketId);
            List<CommentEntry> commentEntries = commentService.findActiveByTicketId(ticketId, true, true);
            TicketEntry ticketEntry = ticketService.findOneById(ticketId);

            Set<String> watcherEmails = watcherEntries.stream()
                    .filter(ticketWatcherEntry -> ticketWatcherEntry.getUser() != null)
                    .map(ticketWatcherEntry -> ticketWatcherEntry.getUser().getEmailId().toLowerCase())
                    .collect(Collectors.toSet());

            if (ticketEntry.getAssignedUser() != null) {
                watcherEmails.add(ticketEntry.getAssignedUser().getEmailId().toLowerCase());
            }
            if (ticketEntry.getReporter() != null) {
                watcherEmails.add(ticketEntry.getReporter().getEmailId().toLowerCase());
            }
            if (ticketEntry.getLastModifiedBy() != null) {
                watcherEmails.add(ticketEntry.getLastModifiedBy().toLowerCase());
            }

            if (ticketEntry.getUserMentions() != null) {
                watcherEmails.addAll(ticketEntry.getUserMentions().stream()
                        .map(UserEntry::getEmailId)
                        .collect(Collectors.toList()));
            }

            commentEntries.forEach(commentEntry -> {
                if (!StringUtils.isEmpty(commentEntry.getLastModifiedBy())) {
                    watcherEmails.add(commentEntry.getLastModifiedBy().toLowerCase());
                }
                if (!StringUtils.isEmpty(commentEntry.getCreatedBy())) {
                    watcherEmails.add(commentEntry.getCreatedBy().toLowerCase());
                }
                if (commentEntry.getUserMentions() != null) {
                    watcherEmails.addAll(commentEntry.getUserMentions()
                            .stream()
                            .map(UserEntry::getEmailId)
                            .collect(Collectors.toList()));
                }
            });
            watcherEmails.removeIf(email -> !emailValidator.isValid(email));
            return watcherEmails;

        } catch (BaseException e) {
            log.error(e.getMessage(), e);
            rollbarService.error(e);
        }
        return new HashSet<>();
    }

    @Override
    @Cacheable(value = "findTicketIdById", unless = "#result == null")
    public Long findTicketIdById(Long id) throws BaseException {
        return super.findOneById(id).getTicketId();
    }

    @Override
    public Long findTicketId(TicketWatcherEntry entry) {
        return entry.getTicketId();
    }
}
