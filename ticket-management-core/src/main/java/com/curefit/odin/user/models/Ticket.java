package com.curefit.odin.user.models;

import java.util.Date;
import java.util.List;
import java.util.Set;
import javax.persistence.*;

import com.curefit.cf.commons.pojo.crypto.EncryptionMode;
import com.curefit.commons.sf.audit.annotation.Audit;
import com.curefit.commons.sf.audit.annotation.EnableEncryption;
import com.curefit.commons.sf.audit.annotation.Encrypt;
import com.curefit.commons.sf.audit.annotation.IgnoreAudit;
import com.curefit.odin.admin.models.*;
import com.curefit.odin.enums.TicketDest;
import com.curefit.odin.enums.TicketSource;
import com.curefit.odin.utils.models.Location;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;
import org.json.simple.JSONObject;
import com.curefit.odin.enums.Priority;
import com.curefit.odin.enums.Status;
import com.vladmihalcea.hibernate.type.json.JsonStringType;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;


@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "ticket")
@Audit
@TypeDefs({@TypeDef(name = "json", typeClass = JsonStringType.class),})
@EnableEncryption(mode = EncryptionMode.CONFIDENTIAL)
public class Ticket extends BaseMySQLModel {

  /**
   *
   */
  private static final long serialVersionUID = -1585963809087500836L;

  @Audit(name = "name")
  @Enumerated(EnumType.STRING)
  @Column(name = "status")
  Status status;

  @Encrypt
  String description;

  @Column(name = "due_date")
  Date dueDate;

  @Encrypt
  String title;

  @IgnoreAudit
  @ManyToOne
  @JoinColumn(name = "parent_ticket_id", referencedColumnName = "id")
  Ticket parentTicket;

  @Audit(name = "name")
  @ManyToOne
  @JoinColumn(name = "tenant_id", referencedColumnName = "id")
  Tenant tenant;

  @Audit(name = "name")
  @ManyToOne
  @JoinColumn(name = "category_id", referencedColumnName = "id")
  Category category;

  @Audit(name = "name")
  @ManyToOne
  @JoinColumn(name = "sub_category_id", referencedColumnName = "id")
  SubCategory subCategory;

  @Column(name = "issue_template_id")
  Long issueTemplateId;

  @Audit(name = "name")
  @Enumerated(EnumType.STRING)
  Priority priority;

  @IgnoreAudit
  @OneToMany()
  @JoinColumn(name = "ticket_id", referencedColumnName = "id")
  List<Attachment> attachments;

  @IgnoreAudit
  @ManyToOne
  @JoinColumn(name = "assignee_queue_id", referencedColumnName = "id")
  AssigneeQueue assigneeQueue;

  @IgnoreAudit
  String assignee;

  @Type(type = "json")
  @Column(columnDefinition = "json")
  Set<String> assignees;

  @Type(type = "json")
  @Column(columnDefinition = "json")
  JSONObject fieldJson;

  @Type(type = "json")
  @Column(columnDefinition = "json")
  Set<String> labels;

  @IgnoreAudit
  @OneToMany(cascade = CascadeType.ALL)
  @JoinColumn(name = "ticket_id", referencedColumnName = "id")
  List<FieldData> fields;

  @Column(name = "closed_at")
  Date closedAt;

  @IgnoreAudit
  @ManyToOne
  @JoinColumn(name = "location", referencedColumnName = "id")
  Location location;

  @Enumerated(EnumType.STRING)
  TicketSource source;
  
  @Column(name = "source_ref_id")
  String sourceRefId;

  @IgnoreAudit
  @Enumerated(EnumType.STRING)
  TicketDest dest;

  @IgnoreAudit
  @Column(name = "dest_ref_id")
  String destRefId;

  @IgnoreAudit
  @Type(type = "json")
  @Column(columnDefinition = "json")
  List<String> mentions;

  @Column(name = "re_opened_at")
  Date reOpenedAt;

  @Column(name = "sla_reminder_sent_at")
  Date slaReminderSentAt;

  @Column(name = "user_id")
  String userId;

  @Column(name = "resolution_rating")
  Integer resolutionRating;

  @Column(name = "asset_reference_id")
  String assetReferenceId;

}
