package com.curefit.odin.user.models;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.curefit.commons.sf.audit.annotation.Audit;
import com.curefit.commons.sf.audit.annotation.AuditParent;
import com.curefit.odin.admin.models.BaseMySQLModel;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Audit
@Table(name = "ticket_watcher",
    uniqueConstraints = {@UniqueConstraint(columnNames = {"ticket_id", "user_id"})})
public class TicketWatcher extends BaseMySQLModel {

  /**
  * 
  */
  private static final long serialVersionUID = -873868436764378631L;

  @AuditParent(parentId = "id")
  @ManyToOne
  @JoinColumn(name = "ticket_id", referencedColumnName = "id")
  Ticket ticket;

  @Column(name = "user_id")
  String userId;

}
