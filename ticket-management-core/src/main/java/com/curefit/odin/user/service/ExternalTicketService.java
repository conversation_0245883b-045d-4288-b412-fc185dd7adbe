package com.curefit.odin.user.service;

import com.curefit.odin.user.pojo.TicketEntry;
import com.curefit.odin.utils.pojo.ExternalTicketResponse;

/**
 * <AUTHOR>
 */

public interface ExternalTicketService {

  ExternalTicketResponse createTicket(String namespace, TicketEntry ticketEntry);

  void updateTicket(String id, TicketEntry ticketEntry);

  void updateAssignees(String id, TicketEntry ticketEntry) throws Exception;

  void updateStatus(String id, TicketEntry ticketEntry);

  void updateStatusForDest(String id, TicketEntry ticketEntry);

  Long addComment(String namespace, String id, String comment);

  Long addPrivateNote(String namespace, String id, String message);

  Long addPrivateNoteAndUpdateStatus(String namespace, String id, TicketEntry ticketEntry, String message);

  boolean isTicketNeedsToCreate(String namespace, TicketEntry ticketEntry);
}
