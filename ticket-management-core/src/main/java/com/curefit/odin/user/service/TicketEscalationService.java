package com.curefit.odin.user.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.mozart.MozartClient;
import com.curefit.mozart.pojo.JobEntry;
import com.curefit.odin.admin.pojo.BusinessHours;
import com.curefit.odin.admin.pojo.EscalationConfigEntry;
import com.curefit.odin.admin.pojo.EscalationRule;
import com.curefit.odin.admin.pojo.UserEntry;
import com.curefit.odin.admin.service.CategoryService;
import com.curefit.odin.admin.service.EscalationConfigService;
import com.curefit.odin.config.OdinConfigurations;
import com.curefit.odin.enums.Priority;
import com.curefit.odin.notification.NotificationHandler;
import com.curefit.odin.user.pojo.TicketEntry;
import com.curefit.odin.user.pojo.TicketEscalationPayload;
import com.curefit.odin.user.pojo.TicketWatcherEntry;
import com.curefit.odin.utils.AsyncService;
import com.curefit.odin.utils.BusinessHoursUtils;
import com.curefit.odin.utils.DateUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.curefit.odin.commons.Constants.*;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TicketEscalationService {

  @Autowired
  EscalationConfigService escalationConfigService;

  @Autowired
  TicketWatcherService ticketWatcherService;

  @Autowired
  RollbarService rollbarService;

  @Autowired
  MozartClient mozartClient;

  @Autowired
  ObjectMapper objectMapper;

  @Autowired
  OdinConfigurations odinConfigurations;

  @Autowired
  NotificationHandler notificationHandler;

  @Autowired
  TicketService ticketService;

  @Autowired
  CategoryService categoryService;

  public void scheduleEscalationRules(TicketEntry ticketEntry) {
    scheduleEscalationRules(ticketEntry, ticketEntry.getPriority(), ticketEntry.getDueDate());
  }

  public void scheduleEscalationRules(TicketEntry ticketEntry, Priority priority, Date dueDate) {
    if (dueDate == null) return; // No escalation if no due date is set.
    EscalationConfigEntry escalationConfigEntry = escalationConfigService.fetchEscalationConfig(ticketEntry.getCategoryId(), ticketEntry.getSubCategoryId());
    if (escalationConfigEntry == null) return;

    JobEntry jobRequest = new JobEntry();
    jobRequest.setSource(ODIN_NAMESPACE);
    jobRequest.setRefId(String.valueOf(ticketEntry.getId()));
    jobRequest.setJobConfigId(odinConfigurations.getMozartJobConfigId());

    AsyncService.submit(() -> escalationConfigEntry.getEscalationRules().forEach(escalationRule -> {
      try {
        jobRequest.setAtSchedule(new Date(fetchEscalationTime(dueDate, escalationRule, ticketEntry)));

        TicketEscalationPayload payload = new TicketEscalationPayload();
        payload.setPriority(priority);
        payload.setTicketId(ticketEntry.getId());
        payload.setEscalationLevel(escalationRule.getEscalationLevel());

        jobRequest.setPayload(objectMapper.convertValue(payload, new TypeReference<ObjectNode>() {
        }));
        mozartClient.submitJobAsync(jobRequest);
      } catch (Exception e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
      }
    }));
  }

  public Set<String> fetchEscalationUsers(TicketEntry ticketEntry) {
    try {
      Long locationId = ticketEntry.getLocationEntry() != null ? ticketEntry.getLocationEntry().getId() : null;
      EscalationConfigEntry escalationConfigEntry = escalationConfigService.fetchEscalationUsers(locationId, ticketEntry.getCategoryId(), ticketEntry.getSubCategoryId(), ticketEntry.getCreatedBy());
      if (escalationConfigEntry != null && escalationConfigEntry.getEscalationRules() != null) {
        return escalationConfigEntry.getEscalationRules().stream()
                .filter(escalationRule -> escalationRule.getEscalationUsers() != null)
                .map(EscalationRule::getEscalationUsers)
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .map(UserEntry::getEmailId)
                .collect(Collectors.toSet());
      }
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    return Collections.emptySet();
  }

  public void handleEscalationEvent(TicketEscalationPayload ticketEscalationPayload) throws BaseException {
    Long ticketId = ticketEscalationPayload.getTicketId();
    TicketEntry ticketEntry = ticketService.findOneById(ticketId);
    if (ticketEntry.getPriority() != ticketEscalationPayload.getPriority()) {
      log.info("ticket priority changed, ignoring the event {} ", ticketEscalationPayload);
      return;
    }
    if (ticketEntry.getDueDate() == null) {
      log.info("ticket due date is null, ignoring the event {} ", ticketEscalationPayload);
      return;
    }

    if (TICKET_DONE_STATUSES.contains(ticketEntry.getStatus())) {
      log.info("ticket status is closed, ignoring the event {}", ticketEscalationPayload);
      return;
    }
    Long locationId = ticketEntry.getLocationEntry() != null ? ticketEntry.getLocationEntry().getId() : null;
    EscalationConfigEntry escalationConfigEntry = escalationConfigService.fetchEscalationUsers(locationId, ticketEntry.getCategoryId(), ticketEntry.getSubCategoryId(), ticketEntry.getCreatedBy());
    if (escalationConfigEntry != null && escalationConfigEntry.getEscalationRules() != null) {
       Optional<EscalationRule> escalationRuleOpt = escalationConfigEntry.getEscalationRules().stream()
              .filter(escalationRule -> escalationRule.getEscalationLevel() == ticketEscalationPayload.getEscalationLevel()
                      && fetchEscalationTime(ticketEntry.getDueDate(), escalationRule, ticketEntry) <= System.currentTimeMillis())
              .findFirst();
       if (escalationRuleOpt.isPresent()) {
         Set<String> escalationUserIds =  escalationRuleOpt.filter(escalationRule -> escalationRule.getEscalationUsers() != null)
                 .map(escalationRule -> escalationRule.getEscalationUsers().stream().map(UserEntry::getEmailId).collect(Collectors.toSet()))
                 .orElse(new HashSet<>());

         log.info("escalating the ticket {} as it is not resolved within due date {}", ticketId, ticketEntry.getDueDate());
         notificationHandler.handleTicketEscalation(ticketEntry, escalationRuleOpt.get().getEscalationLevel(), escalationUserIds);
       } else {
         log.warn("escalation event timing/level is not matching, ignoring the event {}", ticketEscalationPayload);
       }
    }
  }

  private Long fetchEscalationTime(Date dueDate, EscalationRule escalationRule, TicketEntry ticketEntry) {
    return BusinessHoursUtils.calculateDueDateTime(dueDate.getTime(), (long) escalationRule.getEscalationTimeInHours() * MINUTES_IN_HOUR, fetchBusinessHours(ticketEntry), DateUtils.IST_ZONE);
  }

  private BusinessHours fetchBusinessHours(TicketEntry ticketEntry) {
    try {
      if (ticketEntry.getCategoryId() != null) {
        return categoryService.findOneById(ticketEntry.getCategoryId()).getBusinessHours();
      }
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    return null;
  }
}
