package com.curefit.odin.user.service;

import com.curefit.cf.commons.pojo.BaseEntry;
import com.curefit.cf.commons.pojo.PagedResultEntry;
import com.curefit.cf.commons.pojo.SearchOperator;
import com.curefit.common.data.exception.BaseException;
import com.curefit.odin.admin.service.UserService;
import com.curefit.odin.enums.AuthType;
import com.curefit.odin.enums.Priority;
import com.curefit.odin.enums.Status;
import com.curefit.odin.enums.UserRole;
import com.curefit.odin.exceptions.InvalidDataException;
import com.curefit.odin.user.pojo.*;
import com.curefit.odin.utils.AuthService;
import com.curefit.odin.utils.DateUtils;
import com.curefit.odin.utils.service.EmailDLService;
import com.curefit.odin.utils.service.LocationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.PredicateUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
public class TicketFilterService {

    @Autowired
    LocationService locationService;

    @Autowired
    TicketAssigneeQueueService ticketAssigneeQueueService;

    @Autowired
    TicketWatcherService ticketWatcherService;

    @Autowired
    TicketService ticketService;

    @Autowired
    EmailDLService emailDLService;

    @Autowired
    AuthService authService;
    @Autowired
    FieldDataService fieldDataService;
    @Autowired
    AttachmentService attachmentService;
    @Autowired
    private UserService userService;

    @Transactional(rollbackFor = Exception.class)
    public int bulkUpdateAssignees(String userId, String tenantId, BulkUpdateTicketAssigneeRequest request) throws BaseException {
        List<String> existingUserIds = userService.filterExistingUserMailIds(request.getUserIds());
        if (existingUserIds.isEmpty()) {
            log.error("Error while BulkUpdateAssignees: No valid userIds in request - {}", request.getUserIds().toString());
            throw new InvalidDataException("Error while BulkUpdateAssignees: No valid userIds in request");
        }
        List<Long> ticketIds = new ArrayList<>();
        PagedResultEntry<Long, TicketEntry> ticketPage;
        TicketFilterRequestEntry queryFilter = request.getFilter();
        queryFilter.setLimit(500);
        queryFilter.setOffset(0);
        do {
            ticketPage = this.filter(userId, tenantId, queryFilter);
            ticketIds.addAll(
                    ticketPage.getElements().stream()
                            .filter(ticketEntry -> !ticketEntry.isHidden())
                            .map(BaseEntry::getId)
                            .collect(Collectors.toList())
            );
            queryFilter.setOffset((ticketPage.getOffset() + ticketPage.getElements().size()));
        } while (ticketPage.hasMore());
        for (Long ticketId : ticketIds) {
            this.ticketService.updateAssignees(ticketId, existingUserIds);
        }
        return ticketIds.size();
    }

    public PagedResultEntry<Long, TicketEntry> filter(String userId, String tenantId, TicketFilterRequestEntry request) throws BaseException {
        List<BaseOdinEntry> contexts = authService.getContexts(AuthType.LOCATION);
        return filter(userId, tenantId, request, contexts);
    }

    public PagedResultEntry<Long, TicketEntry> filter(String userId, String tenantId, TicketFilterRequestEntry request, List<BaseOdinEntry> authContexts) throws BaseException {
        Map<SearchOperator, Map<String, Object>> query = new HashMap<>();
        updateRequest(request, userId);

        Map<String, Object> inQuery = new HashMap<>();
        if (StringUtils.isNotEmpty(tenantId)) {
            updateInQuery(inQuery, "tenant.id", Collections.singletonList(tenantId));
        }
        updateInQuery(inQuery, "category.id", request.getCategoryIds());
        updateInQuery(inQuery, "subCategory.id", request.getSubCategoryIds());
        updateInQuery(inQuery, "status", request.getStatus());
        updateInQuery(inQuery, "priority", request.getPriorities());
        updateInQuery(inQuery, "parentTicket.id", request.getParentTicketIds());
        updateInQuery(inQuery, "assigneeQueue.id", request.getAssignedQueueIds());
        updateInQuery(inQuery, "location.centerServiceRefId", request.getCenterServiceRefIds());

        boolean isRestricted = authService.getRoles().contains(UserRole.RESTRICTED_USER);
        List<Long> ticketIds = isRestricted ? fetchTicketIdsForRestrictedUser(request, userId) : fetchTicketIds(request);

        if (CollectionUtils.isNotEmpty(ticketIds)) {
            inQuery.put("id", StringUtils.join(ticketIds, ","));
        } else if (ticketIds != null && ticketIds.isEmpty()) {
            return new PagedResultEntry<>(Collections.emptyList(), 0, request.getOffset(), request.getLimit());
        }

        updateInQuery(inQuery, "location.id", request.getLocations());
        if (StringUtils.isNotEmpty(tenantId)) {
            Long tenant = Long.parseLong(tenantId);
            if (!CollectionUtils.isEmpty(request.getLocationNodes()) && !locationService.doesHierarchyContainAllLocations(request.getLocationNodes())) {
                List<String> locations = locationService.getCenterLocations(tenant, request.getLocationNodes());
                if (CollectionUtils.isEmpty(locations)) {
                    return new PagedResultEntry<>(Collections.emptyList(), 0, request.getOffset(),
                            request.getLimit());
                }
                updateInQuery(inQuery, "location.id", locations);
            }
            // Filter authorized locations
            String queryLocationsStr = (String) inQuery.get("location.id");
            List<String> queryLocationIds = queryLocationsStr != null ? Arrays.asList(queryLocationsStr.split(",")) : Collections.emptyList();
            List<String> authorizedLocationIds = getAuthorizedLocations(tenant, authContexts);
            if (!queryLocationIds.isEmpty() && !authorizedLocationIds.isEmpty()) {
                List<String> filteredLocations = (List<String>) CollectionUtils.intersection(queryLocationIds, authorizedLocationIds);
                if (!filteredLocations.isEmpty()) {
                    updateInQuery(inQuery, "location.id", filteredLocations);
                } else {
                    return new PagedResultEntry<>(Collections.emptyList(), 0, request.getOffset(), request.getLimit());
                }
            } else if (!authorizedLocationIds.isEmpty()) {
                updateInQuery(inQuery, "location.id", authorizedLocationIds);
            }
        }

        updateInQuery(inQuery, "createdBy", request.getCreatedBy());
        updateInQuery(inQuery, "userId", request.getUserIds());

        Map<String, Object> gteQuery = new HashMap<>();

        if (request.getCreatedOnFrom() != null) {
            updateQuery(gteQuery, "createdOn", DateUtils.format(request.getCreatedOnFrom()));
        }
        if (request.getDueDateFrom() != null) {
            updateQuery(gteQuery, "dueDate", DateUtils.format(request.getDueDateFrom()));
        }

        Map<String, Object> ltQuery = new HashMap<>();
        if (request.getCreatedOnTo() != null) {
            updateQuery(ltQuery, "createdOn", DateUtils.dateDiffParsed(request.getCreatedOnTo(), 1));
        }
        if (request.getDueDateTo() != null) {
            updateQuery(ltQuery, "dueDate", DateUtils.dateDiffParsed(request.getDueDateTo(), 1));
        }

        Map<String, Object> equalQuery = new HashMap<>();
        if (request.getSource() != null) {
            equalQuery.put("source", request.getSource());
            updateInQuery(inQuery, "sourceRefId", request.getSourceRefIds());
        } else if (CollectionUtils.isNotEmpty(request.getSources())) {
            updateInQuery(inQuery, "source", request.getSources());
            updateInQuery(inQuery, "sourceRefId", request.getSourceRefIds());
        }

        if (!ltQuery.isEmpty()) {
            query.put(SearchOperator.lt, ltQuery);
        }
        if (!gteQuery.isEmpty()) {
            query.put(SearchOperator.gte, gteQuery);
        }
        if (!equalQuery.isEmpty()) {
            query.put(SearchOperator.eq, equalQuery);
        }
        if (!inQuery.isEmpty()) {
            query.put(SearchOperator.in, inQuery);
        }
        log.info("Query {}", query);
        PagedResultEntry<Long, TicketEntry> result = ticketService.search(request.getOffset(), request.getLimit(), request.getSortBy(),
                request.getSortOrder(), query);

        result.getElements().forEach(ticketEntry -> {
            if (ticketEntry.isConfidential() && authService.notAuthorizedIfConfidential(ticketEntry)) {
                hideTicketData(ticketEntry);
            }
        });

        if (BooleanUtils.isTrue(request.getRequireDetails())) {
            result.getElements().forEach(ticketEntry -> {
                ticketService.populateDetails(ticketEntry);
            });
        }

        if (BooleanUtils.isTrue(request.getRequireSignedUrl())) {
            for (TicketEntry ticketEntry : result.getElements()) {
                for (AttachmentEntry attachmentEntry : ticketEntry.getAttachments()) {
                    attachmentEntry.setSignedUrl(attachmentService.getS3SignedUrl(attachmentEntry.getUrl()));
                }
            }
        }

        if (request.getBeforeTime() != null) {
            List<TicketEntry> ticketEntries = result.getElements().stream()
                    .filter(ticketEntry -> ticketEntry.getCreatedOn().getTime() < request.getBeforeTime())
                    .toList();
            result = new PagedResultEntry<>(ticketEntries, result.getTotalElements(), result.getOffset(), request.getLimit());
        }

        return result;
    }

    private void hideTicketData(TicketEntry entry) {
        entry.setId(0L);
        entry.setTitle("xxxx");
        entry.setDescription("xxxx");
        entry.setLocationEntry(null);
        entry.setClosedAt(null);
        entry.setAssigneeQueueUsers(null);
        entry.setDestRefId(null);
        entry.setDest(null);
        entry.setAssignedQueueId(null);
        entry.setDueDate(null);
        entry.setStatus(Status.OPEN);
        entry.setReporter(null);
        entry.setPriority(Priority.P10);
        entry.setSourceRefId(null);
        entry.setSource(null);
        entry.setIsAssignedToMe(false);
        entry.setAssignedQueueName(null);
        entry.setReOpenedAt(null);
        entry.setAssignedUser(null);
        entry.setParentTicketId(null);
        entry.setFields(Collections.emptyList());
        entry.setAttachments(Collections.emptyList());
        entry.setWatchers(Collections.emptyList());
        entry.setUserMentions(Collections.emptyList());
        entry.setUserAuthorized(false);
        entry.setHidden(true);
    }

    private List<Long> fetchTicketIds(TicketFilterRequestEntry request) throws InvalidDataException {
        List<Long> ticketIds = null;
        if (!CollectionUtils.isEmpty(request.getAssignedUserIds())) {
            ticketIds = ticketAssigneeQueueService.fetchAllTicketsByAssigneeIds(fetchAllUsersIncludedInDL(request.getAssignedUserIds()));
        }
        if (!CollectionUtils.isEmpty(request.getWatcherUserIds())) {
            List<Long> filteredTicketIds = ticketWatcherService.findTicketsByUserIds(fetchAllUsersIncludedInDL(request.getWatcherUserIds()));
            ticketIds = ticketIds == null ? filteredTicketIds : (List<Long>) CollectionUtils.intersection(ticketIds, filteredTicketIds);
        }
        if (request.getFields() != null) {
            List<Long> filteredTicketIds = fieldDataService.customFieldFilter(request.getFields());
            ticketIds = ticketIds == null ? filteredTicketIds : (List<Long>) CollectionUtils.intersection(ticketIds, filteredTicketIds);
        }
        if (CollectionUtils.isNotEmpty(ticketIds)) {
            CollectionUtils.filter(ticketIds, PredicateUtils.notNullPredicate());
        }
        return ticketIds;
    }

    private List<Long> fetchTicketIdsForRestrictedUser(TicketFilterRequestEntry request, String userId) throws InvalidDataException {
        if (BooleanUtils.isFalse(request.getIsAssignedToMe()) && BooleanUtils.isFalse(request.getIsWatchedByMe()) && BooleanUtils.isFalse(request.getIsCreatedByMe())) {
            List<Long> assignedTickets = ticketAssigneeQueueService.fetchAllTicketsByAssigneeIds(fetchAllUsersIncludedInDL(Collections.singletonList(userId)));
            List<Long> watchedTickets = ticketWatcherService.findTicketsByUserIds(fetchAllUsersIncludedInDL(Collections.singletonList(userId)));
            List<Long> createdTickets = ticketService.findTicketsByCreatedBy(userId).stream().map(BaseEntry::getId).toList();
            return Stream.of(assignedTickets, watchedTickets, createdTickets).flatMap(Collection::stream).distinct().toList();
        }
        return fetchTicketIds(request);
    }

    private void updateRequest(TicketFilterRequestEntry request, String userId) {
        if (request.getIsAssignedToMe() != null && request.getIsAssignedToMe()) {
            request.setAssignedUserIds(Collections.singletonList(userId));
        }
        if (request.getIsWatchedByMe() != null && request.getIsWatchedByMe()) {
            request.setWatcherUserIds(Collections.singletonList(userId));
        }
        if (request.getIsCreatedByMe() != null && request.getIsCreatedByMe()) {
            request.setCreatedBy(Collections.singletonList(userId));
        }
    }

    private void updateInQuery(Map<String, Object> inQuery, String column, Collection collection) {
        if (!CollectionUtils.isEmpty(collection)) {
            inQuery.put(column, StringUtils.join(collection, ","));
        }
    }

    private void updateQuery(Map<String, Object> query, String column, Object value) {
        if (value != null) {
            query.put(column, value);
        }
    }

    private Set<String> fetchAllUsersIncludedInDL(Collection<String> userIds) {
        Set<String> ids = userIds.stream()
                .map(assigneeId -> emailDLService.getDLsByEmail(assigneeId))
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());
        ids.addAll(userIds);
        return ids;
    }

    private List<String> getAuthorizedLocations(Long tenant, List<BaseOdinEntry> authContexts) throws BaseException {
        Set<String> centreIds = authService.filterTenantContexts(tenant, authContexts, AuthType.LOCATION);
        if (centreIds.isEmpty()) {
            return Collections.emptyList();
        }
        return locationService.findByReferenceIds(centreIds, tenant).stream()
                .map(baseEntry -> String.valueOf(baseEntry.getId()))
                .collect(Collectors.toList());
    }
}