package com.curefit.odin.user.repositories;

import com.curefit.commons.sf.repository.BaseMySQLRepository;
import com.curefit.odin.enums.Status;
import com.curefit.odin.user.models.Ticket;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface TicketDAO extends BaseMySQLRepository<Ticket> {

    Optional<Ticket> findByIdAndActiveTrue(Long id);

    @Modifying
    @Query("UPDATE Ticket set closedAt = :closedAt, lastModifiedOn = :lastModifiedOn where id = :ticketId")
    int updateTicketClosedAt(Long ticketId, Date closedAt, Date lastModifiedOn);

    @Query(value = "SELECT ticket FROM Ticket ticket " +
            "WHERE ticket.status NOT IN :closedStatus " +
            "AND ticket.slaReminderSentAt IS NULL " +
            "AND ticket.dueDate BETWEEN :startTime AND :endTime ")
    List<Ticket> fetchOpenTicketsToSendSlaReminderDueBetween(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("closedStatus") List<Status> closedStatus);

    List<Ticket> getByStatusNotInAndDueDateBetweenAndActive(List<Status> statusList, Date dueDateStart, Date dueDateEnd, Boolean active);

    List<Ticket> findAllByAssetReferenceIdAndIssueTemplateIdAndLocationAndStatusNotIn(String assetReferenceId, Long issueTemplateId, Long location, List<Status> statues);

    List<Ticket> findAllByCreatedBy(String createdBy);
}
