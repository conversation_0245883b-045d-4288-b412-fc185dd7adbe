package com.curefit.odin.user.controllers;

import java.util.List;

import com.curefit.odin.utils.ApiKeyValidator;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.curefit.common.data.exception.BaseException;
import com.curefit.odin.admin.controllers.BaseOdinController;
import com.curefit.odin.user.models.Comment;
import com.curefit.odin.user.pojo.CommentEntry;
import com.curefit.odin.user.service.CommentService;

@RestController
@RequestMapping("/comment")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CommentController extends BaseOdinController<Comment, CommentEntry> {

  @Autowired
  ApiKeyValidator apiKeyValidator;

  public CommentController(CommentService baseMySQLService) {
    super(baseMySQLService);
  }

  @RequestMapping(method = RequestMethod.DELETE, value = "/{id}")
  public ResponseEntity<CommentEntry> deleteById(@PathVariable Long id) throws BaseException {
    return new ResponseEntity<>(((CommentService) baseMySQLService).deleteById(id), HttpStatus.OK);
  }

  @GetMapping
  public ResponseEntity<List<CommentEntry>> findByTicketId(@RequestParam Long ticketId)
      throws BaseException {
    return new ResponseEntity<>(((CommentService) baseMySQLService).findByTicketId(ticketId),
        HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET, value = "/active")
  public ResponseEntity<List<CommentEntry>> findActiveByTicketId(@RequestParam Long ticketId, @RequestParam Boolean includeInternal, @RequestParam Boolean includeExternal) throws BaseException {
    return new ResponseEntity<>(((CommentService) baseMySQLService).findActiveByTicketId(ticketId, includeInternal, includeExternal),
        HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.POST, value = "/external")
  public ResponseEntity<CommentEntry> createFromExternalSource(@RequestHeader("apiKey") String apiKey, @RequestBody CommentEntry entry) throws BaseException {
    return new ResponseEntity(baseMySQLService.create(entry), HttpStatus.OK);
  }
}
