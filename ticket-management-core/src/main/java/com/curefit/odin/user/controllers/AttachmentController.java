package com.curefit.odin.user.controllers;

import com.curefit.odin.user.pojo.S3Bucket;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.curefit.common.data.exception.BaseException;
import com.curefit.odin.admin.controllers.BaseOdinController;
import com.curefit.odin.user.models.Attachment;
import com.curefit.odin.user.pojo.AttachmentEntry;
import com.curefit.odin.user.service.AttachmentService;

@Configuration
@RestController
@RequestMapping("/attachment")
public class AttachmentController
    extends BaseOdinController<Attachment, AttachmentEntry> {

  @Value("${attachment.s3.bucket}")
  private String s3BucketName;

  public AttachmentController(AttachmentService attachmentService) {
    super(attachmentService);
  }

  @RequestMapping(method = RequestMethod.DELETE, value = "/{id}")
  public ResponseEntity<AttachmentEntry> deleteById(@PathVariable Long id) throws BaseException {
    return new ResponseEntity<>(((AttachmentService) baseMySQLService).deleteById(id),
        HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET, value = "/s3Bucket")
  public ResponseEntity<S3Bucket> getS3Bucket() {
    return new ResponseEntity<>(new S3Bucket(s3BucketName), HttpStatus.OK);
  }

  @GetMapping(value = "/pre_signed_url")
  public ResponseEntity<String> getPreSignedURL(@RequestParam String fileKey) throws BaseException {
    return new ResponseEntity<>(((AttachmentService) baseMySQLService).getS3SignedUrl(fileKey, s3BucketName), HttpStatus.OK);
  }
  @GetMapping(value = "/pre_signed_url_v2")
  public ResponseEntity<String >getPreSignedURLV2(@RequestParam String fileKey)throws BaseException{
    return new ResponseEntity<>(((AttachmentService)baseMySQLService).getS3SignedUrlv2(fileKey,s3BucketName),HttpStatus.OK);
  }
}
