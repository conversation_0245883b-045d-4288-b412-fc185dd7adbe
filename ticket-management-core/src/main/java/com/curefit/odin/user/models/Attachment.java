package com.curefit.odin.user.models;

import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.curefit.commons.sf.audit.annotation.Audit;
import com.curefit.commons.sf.audit.annotation.AuditParent;
import com.curefit.commons.sf.audit.annotation.IgnoreAudit;
import com.curefit.odin.admin.models.BaseMySQLModel;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Audit
@Table(name = "attachment")
public class Attachment extends BaseMySQLModel {
  /**
  * 
  */
  private static final long serialVersionUID = -5201995383775564961L;

  @IgnoreAudit
  String url;

  String name;

  String description;

  @ManyToOne
  @JoinColumn(name = "comment_id", referencedColumnName = "id")
  @AuditParent(parentId = "id")
  Comment comment;
  
  @ManyToOne
  @JoinColumn(name = "ticket_id", referencedColumnName = "id")
  @AuditParent(parentId = "id")
  Ticket ticket;
}
