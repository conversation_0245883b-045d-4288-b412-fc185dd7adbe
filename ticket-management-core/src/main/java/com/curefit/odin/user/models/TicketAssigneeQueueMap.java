package com.curefit.odin.user.models;

import com.curefit.odin.admin.models.BaseMySQLModel;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 */

@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "ticket_assignee_queue_map")
public class TicketAssigneeQueueMap extends BaseMySQLModel {

  @Column(name = "ticket_id")
  Long ticketId;

  @Column(name = "assignee_id")
  String assigneeId;
}
