package com.curefit.odin.user.service;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.curefit.odin.admin.service.SubCategoryService;
import com.curefit.odin.commons.Constants;
import com.curefit.commons.sf.crypto.EncryptionUtils;
import com.curefit.odin.enums.FieldType;
import com.curefit.odin.user.models.Ticket;
import com.curefit.odin.utils.AuthService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.odin.admin.models.CustomField;
import com.curefit.odin.admin.pojo.CustomFieldEntry;
import com.curefit.odin.admin.pojo.CustomFieldFilterRequestEntry;
import com.curefit.odin.admin.pojo.DataSourceValueEntry;
import com.curefit.odin.admin.service.CategoryService;
import com.curefit.odin.admin.service.CustomFieldService;
import com.curefit.odin.admin.service.DataSourceValueService;
import com.curefit.odin.enums.DataType;
import com.curefit.odin.exceptions.InvalidDataException;
import com.curefit.odin.user.models.FieldData;
import com.curefit.odin.user.pojo.FieldDataEntry;
import com.curefit.odin.user.pojo.FieldEntry;
import com.curefit.odin.user.pojo.FieldValueEntry;
import com.curefit.odin.user.pojo.TicketEntry;
import com.curefit.odin.user.repositories.FieldDataDAO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class FieldDataService extends BaseMySQLService<FieldData, FieldDataEntry> implements TicketCacheService<FieldDataEntry> {

  @Autowired
  CustomFieldService customFieldService;

  @Autowired
  CategoryService categoryService;

  @Autowired
  SubCategoryService subCategoryService;

  @Autowired
  DataSourceValueService dataSourceValueService;

  @Autowired
  RollbarService rollbarService;

  @Autowired
  AuthService authService;

  @Autowired
  TicketService ticketService;

  public FieldDataService(FieldDataDAO fieldDataDAO) {
    super(fieldDataDAO);
    objectMapper.enable(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY);
  }

  @Override
  public FieldDataEntry convertToEntry(FieldData fieldData) {
    FieldDataEntry fieldDataEntry = super.convertToEntry(fieldData);
    fieldDataEntry.setKey(new FieldEntry(fieldData.getField().getId(), fieldData.getField().getDataType(), fieldData.getField().getName()));
    if (fieldData.getField().getDataType() == DataType.LIST) {
      log.info("Converting: " + fieldDataEntry.getValue());
      try {
        List<Long> values = objectMapper.readValue((String) fieldDataEntry.getValue(), new TypeReference<ArrayList<Long>>() {
        });
        fieldDataEntry.setValue(values.stream().map(value -> {
          try {
            DataSourceValueEntry dataSourceValueEntry = dataSourceValueService.getValueFromDataSourceIdForField(value);
            return new FieldValueEntry(dataSourceValueEntry.getId(), dataSourceValueEntry.getValue());
          } catch (BaseException e) {
            log.error(e.getMessage(), e);
            rollbarService.error(e);
            return null;
          }
        }).filter(Objects::nonNull).collect(Collectors.toList()));
      } catch (IOException e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
      }
    } else {
      fieldDataEntry.setValue(fieldDataEntry.getValue());
    }
    return fieldDataEntry;
  }


  @Override
  public FieldData convertToEntity(FieldDataEntry fieldDataEntry) {
    FieldData fieldData = null;
    try {
      CustomField field = customFieldService.fetchEntityById(fieldDataEntry.getKey().getId());
      fieldDataEntry.setValue(getValue(field, fieldDataEntry.getValue()));
      fieldData = super.convertToEntity(fieldDataEntry);
      fieldData.setField(field);
    } catch (JsonProcessingException | BaseException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }

    return fieldData;
  }


  /**
   * @param cField
   * @param value
   * @return
   * @throws JsonProcessingException
   */
  private String getValue(CustomField cField, Object value) throws JsonProcessingException {
    switch (cField.getDataType()) {
      case LIST:
        List<FieldValueEntry> convertedValue =
                objectMapper.convertValue(value, new TypeReference<List<FieldValueEntry>>() {
                });
        return objectMapper.writeValueAsString(
                convertedValue.stream().map(FieldValueEntry::getId).collect(Collectors.toList()));
      case DATETIME:
      case DATE:
      case TIME:
      case NUMBER:
      case TEXT:
        return (String) value;
      default:
        return null;
    }
  }

  @Cacheable(value = "findFieldDataByTicketId", unless = "#result == null", key = "#ticketId")
  public List<FieldDataEntry> findFieldDataByTicketId(Long ticketId) {
    log.info("Fetching field data by ticket id {}", ticketId);
    List<FieldData> fieldDataList = ((FieldDataDAO) baseMySQLRepository).findAllByTicketId(ticketId);
    return fieldDataList.stream()
        .filter(fieldData -> fieldData.getField().getActive())
        .map(this::convertToEntry)
        .collect(Collectors.toList());
  }

  @CacheEvict(value = "findFieldDataByTicketId", key = "#ticketId")
  public void evictFindFieldDataByTicketIdCache(Long ticketId) {}

  public List<FieldDataEntry> fetchFieldDataByTicketId(Long id) throws BaseException {
    authService.checkAuthorized(id, ticketService);
    return findFieldDataByTicketId(id);
  }

  public void validate(TicketEntry entry) throws BaseException {
    validate(entry, false);
  }

  public void validateOnClosing(TicketEntry entry) throws BaseException {
    validate(entry, true);
  }

  /**
   * @param entry
   * @return
   * @throws InvalidDataException
   */
  private void validate(TicketEntry entry, boolean onClosing) throws BaseException {
    Set<Long> fieldsForTicket = checkIfMandatoryFieldsPresent(entry, onClosing);
    if (CollectionUtils.isNotEmpty(entry.getFields())) {
      checkIfDataTypesValid(entry.getFields());
      for (FieldDataEntry fieldDataEntry : entry.getFields()) {
        if (!fieldsForTicket.isEmpty() && !fieldsForTicket.contains(fieldDataEntry.getKey().getId())) {
          log.warn("Field id {} is not linked . Please provide correct values to proceed", fieldDataEntry.getKey().getId());
          throw new InvalidDataException("Field id " + fieldDataEntry.getKey().getId() + " is not linked . Please provide correct values to proceed");
        }
      }
    }
  }

  /**
   * @param fieldDataEntries
   * @throws InvalidDataException
   */
  private void checkIfDataTypesValid(List<FieldDataEntry> fieldDataEntries) throws BaseException {
    for (FieldDataEntry fieldDataEntry : fieldDataEntries) {
      validateDataType(fieldDataEntry.getValue(), customFieldService.findOneById(fieldDataEntry.getKey().getId()));
    }
  }

  /**
   * @param entry TicketEntry
   * @return list of fields linked to the category or subcategory id
   * @throws InvalidDataException
   */
  private Set<Long> checkIfMandatoryFieldsPresent(TicketEntry entry, boolean onClosing) throws InvalidDataException {
    log.info("Checking mandatory fields");
    CustomFieldFilterRequestEntry fieldFilterRequestEntry = new CustomFieldFilterRequestEntry();
    fieldFilterRequestEntry.setTenantId(entry.getTenantId());
    fieldFilterRequestEntry.setCategoryId(entry.getCategoryId());
    fieldFilterRequestEntry.setSubCategoryId(entry.getSubCategoryId());
    List<CustomFieldEntry> fieldEntries = customFieldService.filterActiveV2(fieldFilterRequestEntry);
    Set<Long> fieldsForTicket = fieldEntries.stream()
        .map(CustomFieldEntry::getId)
        .collect(Collectors.toSet());
    Map<Long, String> fieldIdNameMap = new HashMap<>();
    fieldEntries.forEach(customFieldEntry -> fieldIdNameMap.put(customFieldEntry.getId(), customFieldEntry.getName()));

    List<CustomFieldEntry> mandatoryFields = fieldEntries.stream()
        .filter(onClosing ? CustomFieldEntry::isMandatoryOnClosing : CustomFieldEntry::isMandatory)
        .sorted(Comparator.comparing(CustomFieldEntry::getId))
        .collect(Collectors.toList());
    List<CustomFieldEntry> defaultFieldEntries = mandatoryFields.stream()
        .filter(field -> FieldType.DEFAULT.equals(field.getType()))
        .collect(Collectors.toList());
    List<CustomFieldEntry> customFieldEntries = mandatoryFields.stream()
        .filter(field -> FieldType.CUSTOM.equals(field.getType()))
        .collect(Collectors.toList());

    checkIfDefaultFieldsPresent(entry, defaultFieldEntries);
    checkIfCustomFieldsPresent(entry, customFieldEntries, fieldIdNameMap);

    return fieldsForTicket;
  }

  private void checkIfCustomFieldsPresent(TicketEntry entry, List<CustomFieldEntry> fieldEntries, Map<Long, String> fieldIdNameMap)  throws InvalidDataException {
    List<FieldDataEntry> customFieldDataEntries = entry.getFields();
    Set<String> inputFieldNames = CollectionUtils.isNotEmpty(customFieldDataEntries)
        ? customFieldDataEntries.stream().map(fieldDataEntry -> fieldIdNameMap.get(fieldDataEntry.getKey().getId())).collect(Collectors.toSet())
        : new HashSet<>();

    List<String> missingFieldNames = fieldEntries.stream()
        .map(CustomFieldEntry::getName)
        .filter(name -> !inputFieldNames.contains(name))
        .collect(Collectors.toList());

    if (!missingFieldNames.isEmpty()) {
      throw new InvalidDataException("Mandatory Custom Fields absent. Missing mandatory fields: " + missingFieldNames);
    }
  }

  private void checkIfDefaultFieldsPresent(TicketEntry entry, List<CustomFieldEntry> fieldEntries)  throws InvalidDataException {
    List<String> missingFieldNames = new ArrayList<>();
    for(CustomFieldEntry fieldEntry : fieldEntries) {
      String fieldName = fieldEntry.getName();
      switch (fieldName) {
        case "categoryId":
          if(entry.getCategoryId() == null) missingFieldNames.add(fieldName);
          break;
        case "subCategoryId":
          if(entry.getSubCategoryId() == null
              && entry.getCategoryId() != null
              && !subCategoryService.findByCategoryId(entry.getCategoryId()).isEmpty())
            missingFieldNames.add(fieldName);
          break;
        case "location":
          if(entry.getLocationEntry() == null) missingFieldNames.add(fieldName);
          break;
        case "title":
          if(StringUtils.isEmpty(entry.getTitle())) missingFieldNames.add(fieldName);
          break;
        case "description":
          if(StringUtils.isEmpty(entry.getDescription())) missingFieldNames.add(fieldName);
          break;
        case "priority":
          if(entry.getPriority() == null) missingFieldNames.add(fieldName);
          break;
        case "dueDate":
          if(entry.getDueDate() == null) missingFieldNames.add(fieldName);
          break;
        case "assignee":
          if((entry.getAssigneeQueueUsers() == null || entry.getAssigneeQueueUsers().isEmpty()) && entry.getAssignedQueueId() == null) missingFieldNames.add(fieldName);
          break;
        case "userId":
          if(StringUtils.isEmpty(entry.getUserId())) missingFieldNames.add(fieldName);
          break;
      }
    }
    if (!missingFieldNames.isEmpty()) {
      throw new InvalidDataException("Mandatory Default Fields absent. Missing mandatory fields: " + missingFieldNames);
    }
  }


  /**
   * @param value
   * @param fieldEntry
   * @throws InvalidDataException
   */
  public void validateDataType(Object value, CustomFieldEntry fieldEntry)
          throws InvalidDataException {
    log.info("Validating field with field id {} and value {}", fieldEntry.getId(), value);
    switch (fieldEntry.getDataType()) {
      case TEXT:
        if (!(value instanceof java.lang.String)) {
          throw new InvalidDataException("Invalid data type");
        }
        break;
      case DATETIME:
      case DATE:
        try {
          Date date = (new SimpleDateFormat("yyyy-MM-dd").parse(String.valueOf(value)));
          if (date == null) {
            throw new InvalidDataException("Invalid data type");
          }
        } catch (ParseException e) {
          throw new InvalidDataException("Invalid data type");
        }
        break;
      case LIST:
        log.info("Validating:" + value);
        List<FieldValueEntry> listValues =
                objectMapper.convertValue(value, new TypeReference<List<FieldValueEntry>>() {
                });
        for (FieldValueEntry listValue : listValues) {
          try {
            dataSourceValueService.getValueFromActiveDataSourceIdForField(listValue.getId());
          } catch (BaseException e) {
            throw new InvalidDataException("Invalid data source");
          }
        }
        break;
      case NUMBER:
        try {
          if (!StringUtils.isNumeric((String) value)) {
            throw new InvalidDataException("Invalid data type");
          }
        } catch (RuntimeException e) {
          throw new InvalidDataException("Invalid data type");
        }
        break;
      case TIME:
        break;
        // TODO : need to add validation
      default:
        break;
    }
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  @CacheEvict(value = {"findFieldDataByTicketId"}, allEntries = true)
  public FieldDataEntry patchUpdate(Long id, FieldDataEntry entry) throws BaseException {
    FieldDataEntry fieldDataEntry = findOneById(id);
    authService.checkAuthorized(fieldDataEntry, this);
    if (entry.getKey() == null) {
      FieldEntry key = new FieldEntry();
      key.setId(fieldDataEntry.getKey().getId());
      entry.setKey(key);
    }
    FieldDataEntry customFieldDataEntry = super.patchUpdate(id, entry);
    // updateJsonFieldDataUpdate(fieldData.get().getTicket().getId());
    return customFieldDataEntry;
  }


  public JSONObject getJSONForFields(List<FieldDataEntry> fields) {
    JSONObject jsonFields = new JSONObject();
   fields.forEach(field -> {
      if (field.getKey().getDataType() == DataType.LIST) {
        JSONArray jsonArray = new JSONArray();
        ArrayList<FieldValueEntry> values = (ArrayList<FieldValueEntry>) field.getValue();
        for (FieldValueEntry value : values) {
          jsonArray.add(value.getId());
        }
        jsonFields.put(field.getKey().getId(), jsonArray);
      } else {
        jsonFields.put(field.getKey().getId(), field.getValue());
      }
    });
    return jsonFields;
  }

  @Override
  public Long findTicketIdById(Long id) {
    return null;
  }

  @Override
  public Long findTicketId(FieldDataEntry entry) {
    return entry.getTicketId();
  }

  public List<Long> customFieldFilter(Map<String, List<String>> fields) throws InvalidDataException {
    List<Long> fieldIds = fields.keySet().stream().map(Long::parseLong).collect(Collectors.toList());
    List<CustomFieldEntry> fieldEntries = customFieldService.findAllById(fieldIds);
    List<Long> ticketIds = null;
    for(CustomFieldEntry fieldEntry: fieldEntries) {
      Long fieldId = fieldEntry.getId();
      DataType fieldType = fieldEntry.getDataType();
      List<String> fieldValues = fields.get(fieldId.toString());
      String value;
      if(fieldValues==null || fieldValues.isEmpty()) return Collections.emptyList();
      switch (fieldType) {
        case TEXT:
        case NUMBER:
        case LIST:
          ticketIds = findTicketIdsByFieldIdAndValue(ticketIds, fieldId, fieldValues);
          break;
        case DATE:
          value = getFormattedDateTime(fieldType, fieldValues.get(0));
          ticketIds = findTicketIdsByFieldIdAndDateValue(ticketIds, fieldId, value);
          break;
        case TIME:
          value = getFormattedDateTime(fieldType, fieldValues.get(0));
          ticketIds = findTicketIdsByFieldIdAndTimeValue(ticketIds, fieldId, value);
          break;
        case DATETIME:
          value = getFormattedDateTime(fieldType, fieldValues.get(0));
          ticketIds = findTicketIdsByFieldIdAndDateTimeValue(ticketIds, fieldId, value);
          break;
      }
    }
    return ticketIds;
  }

  private String getFormattedDateTime(DataType type, String value) throws InvalidDataException {
    Date date;
    try {
      date = Constants.INPUT_DATE_FORMAT.parse(value);
    } catch (ParseException exception) {
      throw new InvalidDataException("Error in parsing DateTime value: " + value, exception);
    }
    switch(type) {
      case DATE:
        return Constants.DATE_FORMAT.format(date);
      case TIME:
        return Constants.TIME_FORMAT.format(date);
      case DATETIME:
        return Constants.DATE_TIME_FORMAT.format(date);
      default:
        throw new InvalidDataException("Invalid data type: "+type);
    }
  }

  private List<Long> findTicketIdsByFieldIdAndValue(List<Long> ticketIds, Long fieldId, List<String> values) {
    return ticketIds==null
        ? ((FieldDataDAO)baseMySQLRepository).findTicketIdsByFieldIdAndValues(fieldId, values)
        : ticketIds.isEmpty()
        ? Collections.emptyList() :
        ((FieldDataDAO)baseMySQLRepository).findTicketIdsByFieldIdAndValues(ticketIds, fieldId, values);
  }

  private List<Long> findTicketIdsByFieldIdAndDateValue(List<Long> ticketIds, Long fieldId, String value) {
    return ticketIds==null
        ? ((FieldDataDAO)baseMySQLRepository).findTicketIdsByFieldIdAndDateValue(fieldId, value)
        : ticketIds.isEmpty()
        ? Collections.emptyList() :
        ((FieldDataDAO)baseMySQLRepository).findTicketIdsByFieldIdAndDateValue(ticketIds, fieldId, value);
  }

  private List<Long> findTicketIdsByFieldIdAndTimeValue(List<Long> ticketIds, Long fieldId, String value) {
    return ticketIds==null
        ? ((FieldDataDAO)baseMySQLRepository).findTicketIdsByFieldIdAndTimeValue(fieldId, value)
        : ticketIds.isEmpty()
        ? Collections.emptyList() :
        ((FieldDataDAO)baseMySQLRepository).findTicketIdsByFieldIdAndTimeValue(ticketIds, fieldId, value);
  }

  private List<Long> findTicketIdsByFieldIdAndDateTimeValue(List<Long> ticketIds, Long fieldId, String value) {
    return ticketIds==null
        ? ((FieldDataDAO)baseMySQLRepository).findTicketIdsByFieldIdAndDateTimeValue(fieldId, value)
        : ticketIds.isEmpty()
        ? Collections.emptyList() :
        ((FieldDataDAO)baseMySQLRepository).findTicketIdsByFieldIdAndDateTimeValue(ticketIds, fieldId, value);
  }
}
