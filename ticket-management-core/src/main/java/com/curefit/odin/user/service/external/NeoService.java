package com.curefit.odin.user.service.external;

import com.curefit.common.data.enums.AppStatus;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.client.exception.HttpException;
import com.curefit.commons.client.pojo.HttpRequestDetail;
import com.curefit.commons.client.restTemplate.RestTemplateClient;
import com.curefit.commons.utils.cb.CircuitBreakerProperties;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class NeoService {
    private static final String GET_EMPLOYEE_MANAGERS_API_URL = "/employee/managers";

    @Value("${external.neo.baseUrl}")
    private String baseUrl;

    @Autowired
    protected RestTemplateClient restTemplateClient;
    private static final CircuitBreakerProperties circuitBreakerProperties = CircuitBreakerProperties.builder()
            .errorThresholdPercentage(40)
            .executionTimeoutInMilliseconds(5000)
            .requestVolumeThreshold(15)
            .sleepWindowInMilliseconds(5000)
            .circuitBreakerKey("ticketing-system")
            .enabled(true).build();

    @Retryable(value = {HttpClientErrorException.NotFound.class, HttpException.class}, maxAttemptsExpression = "${retry.maxAttempts}",
            backoff = @Backoff(delayExpression = "${retry.delay}"))
    public Map<Integer, List<String>> getEmployeeManagersByEmail(String employeeEmail, List<Integer> managerLevels) throws BaseException {
        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromUriString(baseUrl).path(GET_EMPLOYEE_MANAGERS_API_URL);
        String url = uriComponentsBuilder.build().toUriString();

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("email", employeeEmail);
        queryParams.put("levels", managerLevels.stream()
                .map(Object::toString)
                .collect(Collectors.joining(",")));

        Map<String, String> headers = new HashMap<>();
        headers.put("tenant", "cult");

        log.info("Triggering manager details api in neo for email {}", employeeEmail);
        Long start = System.nanoTime();
        HttpRequestDetail<String, Map<Integer, List<String>>> httpRequestDetail =
                new HttpRequestDetail<>(url, null, queryParams, headers,
                        circuitBreakerProperties, new TypeReference<Map<Integer, List<String>>>() {
                });
        ResponseEntity<Map<Integer, List<String>>> response = restTemplateClient.get(httpRequestDetail);

        Long end = System.nanoTime();

        org.springframework.http.HttpStatus responseStatus = response.getStatusCode();
        Map<Integer, List<String>> responseBody = response.getBody();
        log.info("Neo get manager details response received for email: {} in {} ms. Status: {}, Body : {}", employeeEmail,
                (end - start)/1e6, responseStatus, responseBody);

        if (responseStatus.is2xxSuccessful())  {
            return responseBody;
        }

        String errorMsg = String.format("Got invalid manager details " +
                "response from Neo. Got status code %s Reason Phrase %s and Response body %s", responseStatus, responseStatus.getReasonPhrase(), response.getBody());
        log.error(errorMsg);
        throw new BaseException(AppStatus.BAD_REQUEST);

    }
}
