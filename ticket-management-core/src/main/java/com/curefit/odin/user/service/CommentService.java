package com.curefit.odin.user.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.commons.sf.util.HeadersUtils;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.pojo.UserEntry;
import com.curefit.odin.admin.service.UserService;
import com.curefit.odin.user.models.Comment;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import com.curefit.odin.user.pojo.CommentEntry;
import com.curefit.odin.user.repositories.CommentDAO;
import com.curefit.odin.utils.AsyncService;
import com.curefit.odin.utils.AuthService;
import com.curefit.odin.utils.OdinStringUtils;
import com.curefit.odin.utils.UserIdParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


@Service
@Slf4j
public class CommentService extends BaseMySQLService<Comment, CommentEntry> implements TicketCacheService<CommentEntry> {


    @Autowired
    RollbarService rollbarService;
    @Autowired
    AttachmentService attachmentService;
    @Autowired
    UserService userService;
    @Autowired
    AuthService authService;
    @Autowired
    ExternalTicketServices externalTicketServices;
    @Autowired
    private TicketService ticketService;

    public CommentService(CommentDAO commentDao) {
        super(commentDao);
    }

    @Override
    public CommentEntry convertToEntry(Comment comment) {
        CommentEntry entry = super.convertToEntry(comment);
        if (null != comment.getTicket()) {
            entry.setTicketId(comment.getTicket().getId());
        }
        if (comment.getParentComment() != null) {
            entry.setParentCommentId(comment.getParentComment().getId());
        }
        entry.setRelatedComments(getRelatedComments(comment.getId()));
        try {
            entry.setUser(userService.findUserByMailId(comment.getCreatedBy()));
        } catch (BaseException e) {
            log.error(e.getMessage(), e);
            rollbarService.error(e);
        }
        if (comment.getAttachments() != null) {
            entry.setAttachments(comment.getAttachments().stream()
                    .map(a -> attachmentService.convertToEntry(a)).collect(Collectors.toList()));
            removeInvalidAttachments(entry);
        }

        if (comment.getMentions() != null) {
            entry.setUserMentions(comment.getMentions().stream().map(userId -> {
                try {
                    return userService.findUserByMailId(userId);
                } catch (BaseException e) {
                    log.error(e.getMessage(), e);
                    rollbarService.error(e);
                    return new UserEntry(userId, userId);
                }
            }).collect(Collectors.toList()));
        }
        return entry;
    }

    private void removeInvalidAttachments(CommentEntry commentEntry) {
        commentEntry.setAttachments(commentEntry.getAttachments().stream()
                .filter(BaseOdinEntry::getActive).collect(Collectors.toList()));
    }

    private List<CommentEntry> getRelatedComments(Long id) {
        List<Comment> relatedComments = ((CommentDAO) baseMySQLRepository).findAllByParentCommentId(id);
        return relatedComments.stream().map(this::convertToEntry).collect(Collectors.toList());
    }

    @Override
    public Comment convertToEntity(CommentEntry commentEntry) {
        Comment convertedEntity = super.convertToEntity(commentEntry);
        if (null != commentEntry.getTicketId()) {
            try {
                convertedEntity.setTicket(ticketService.fetchEntityById(commentEntry.getTicketId()));
            } catch (BaseException e) {
                log.error(e.getMessage(), e);
                rollbarService.error(e);
            }
        }
        if (null != commentEntry.getParentCommentId()) {
            try {
                convertedEntity.setParentComment(fetchEntityById(commentEntry.getParentCommentId()));
            } catch (BaseException e) {
                log.error(e.getMessage(), e);
                rollbarService.error(e);
            }
        }

        List<String> userIds = UserIdParser.getUserIds(commentEntry.getComment());
        convertedEntity.setMentions(userIds);
        return convertedEntity;
    }

    public List<CommentEntry> findByTicketId(Long id) throws BaseException {
        log.info("Fetching comments for ticket id {}", id);
        authService.checkAuthorized(id, ticketService);
        try {
            return search(0, -1, null, null, "ticket.id.eq:" + id + ";active.eq:true").getElements();
        } catch (InvalidSeachQueryException e) {
            log.error(e.getMessage(), e);
            rollbarService.error(e);
            throw new BaseException(e);
        }
    }

    public List<CommentEntry> findActiveByTicketId(Long id, Boolean includeInternal, Boolean includeExternal) throws BaseException {
        authService.checkAuthorized(id, ticketService);
        List<Comment> comments = new ArrayList<>();
        if (includeInternal) {
            log.info("Fetching internal active comments for ticket id {}", id);
            List<Comment> activeInternalComments = ((CommentDAO) baseMySQLRepository).findByTicketIdAndActiveAndIsInternal(id, true, true);
            comments.addAll(activeInternalComments);
        }
        if (includeExternal) {
            log.info("Fetching external active comments for ticket id {}", id);
            List<Comment> activeExternalComments = ((CommentDAO) baseMySQLRepository).findByTicketIdAndActiveAndIsInternal(id, true, false);
            comments.addAll(activeExternalComments);
        }
        return comments.stream().map(this::convertToEntry).collect(Collectors.toList());
    }

    @Override
    public CommentEntry create(CommentEntry entry) throws BaseException {
        entry.setComment(OdinStringUtils.replaceIncompatibleCharactersWithSpace(entry.getComment()));
        authService.checkAuthorized(entry, this);
        if (entry.getIsInternal() == null)
            entry.setIsInternal(false);
        CommentEntry commentEntry = super.create(entry);
        String namespace = HeadersUtils.getNamespace();
        AsyncService.submit(() -> {
            try {
                externalTicketServices.addComment(namespace, ticketService.findOneById(commentEntry.getTicketId()), commentEntry.getCreatedBy() + " added a comment - " + entry.getComment());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                rollbarService.error(e);
            }
        });

        return commentEntry;
    }

    @Override
    public CommentEntry patchUpdate(Long id, CommentEntry entry) throws BaseException {
        authService.checkAuthorized(id, this);
        entry.setId(id);
        return super.patchUpdate(id, entry);
    }

    @Transactional(rollbackFor = Exception.class)
    public CommentEntry deleteById(Long id) throws BaseException {
        log.info("Deleting comment {}", id);
        CommentEntry comment = new CommentEntry();
        comment.setActive(false);
        return patchUpdate(id, comment);
    }

    @Override
    @Cacheable(value = "findTicketIdById", unless = "#result == null")
    public Long findTicketIdById(Long id) throws BaseException {
        return super.findOneById(id).getTicketId();
    }

    @Override
    public Long findTicketId(CommentEntry entry) {
        return entry.getTicketId();
    }

    public Optional<CommentEntry> findLatestCommentOnTicketByUser(Long ticketId, String emailId) {
        Optional<Comment> commentOpt = ((CommentDAO) this.baseMySQLRepository).findTopByTicketIdAndCreatedByOrderByIdDesc(ticketId, emailId);
        return commentOpt.map(this::convertToEntry);
    }
}
