package com.curefit.odin.user.controllers;

import java.util.Arrays;
import java.util.List;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import com.curefit.odin.enums.Status;

@RestController
@RequestMapping("/status")
public class StatusController {

  @RequestMapping(method = RequestMethod.GET, value = "/all")
  public ResponseEntity<List<Status>> fetchAll() {
    return new ResponseEntity<>(Arrays.asList(Status.values()), HttpStatus.OK);
  }
}
