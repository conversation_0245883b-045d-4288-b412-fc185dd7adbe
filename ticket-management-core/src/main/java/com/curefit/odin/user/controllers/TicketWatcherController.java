package com.curefit.odin.user.controllers;

import java.util.List;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.controllers.BaseOdinController;
import com.curefit.odin.user.models.TicketWatcher;
import com.curefit.odin.user.pojo.TicketWatcherEntry;
import com.curefit.odin.user.service.TicketWatcherService;

@RestController
@RequestMapping("/ticket_watcher")
public class TicketWatcherController extends BaseOdinController<TicketWatcher, TicketWatcherEntry> {

  public TicketWatcherController(TicketWatcherService ticketWatcherService) {
    super(ticketWatcherService);
  }

  @RequestMapping(method = RequestMethod.DELETE, value = "/{id}")
  public ResponseEntity<TicketWatcherEntry> deleteById(@PathVariable Long id) throws BaseException {
    return new ResponseEntity<>(((TicketWatcherService) baseMySQLService).deleteById(id),
        HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET)
  public ResponseEntity<List<TicketWatcherEntry>> findByTicketId(@RequestParam Long ticketId)
          throws InvalidSeachQueryException, BaseException {
    return new ResponseEntity<>(((TicketWatcherService) baseMySQLService).fetchByTicketId(ticketId),
        HttpStatus.OK);
  }

}
