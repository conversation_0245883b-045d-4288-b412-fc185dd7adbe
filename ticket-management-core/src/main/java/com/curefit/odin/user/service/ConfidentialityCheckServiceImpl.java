package com.curefit.odin.user.service;

import com.curefit.cf.commons.pojo.BaseEntry;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.sf.crypto.ConfidentialityCheckService;
import com.curefit.commons.sf.model.BaseMySQLEntity;
import com.curefit.odin.admin.service.CategoryService;
import com.curefit.odin.user.models.Comment;
import com.curefit.odin.user.models.FieldData;
import com.curefit.odin.user.models.Ticket;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import com.curefit.odin.user.pojo.CommentEntry;
import com.curefit.odin.user.pojo.FieldDataEntry;
import com.curefit.odin.user.pojo.TicketEntry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class ConfidentialityCheckServiceImpl implements ConfidentialityCheckService {

  @Autowired
  CommentService commentService;

  @Autowired
  FieldDataService fieldDataService;

  @Autowired
  TicketService ticketService;

  @Autowired
  CategoryService categoryService;

  @Override
  public boolean isConfidential(BaseMySQLEntity entity) throws BaseException {
    Ticket ticket;
    if (entity.getClass() == Ticket.class) {
      ticket = ((Ticket) entity);
      return isTicketConfidential(ticket);
    }

    if (entity.getClass() == Comment.class) {
      Comment comment = ((Comment) entity);
      return isTicketConfidential(comment.getTicket());
    }

    if (entity.getClass() == FieldData.class) {
      FieldData fieldData = ((FieldData) entity);
      return isTicketConfidential(fieldData.getTicket());
    }
    return false;
  }

  @Override
  public boolean isConfidential(BaseEntry entry) throws BaseException {
    TicketCacheService ticketCacheService = null;
    if (entry.getClass() == TicketEntry.class) {
      ticketCacheService = ticketService;
    }

    if (entry.getClass() == CommentEntry.class) {
      ticketCacheService = commentService;
    }

    if (entry.getClass() == FieldDataEntry.class) {
      ticketCacheService = fieldDataService;
    }

    if (ticketCacheService != null) {
      TicketEntry ticketEntry;
      if (entry.getId() != null) {
        ticketEntry = ticketService.findTicketById(ticketCacheService.findTicketIdById(entry.getId()));
      } else {
        if (entry.getClass() == TicketEntry.class) {
          return isTicketConfidential(((TicketEntry)entry).getCategoryId());
        }
        if (entry.getClass() == FieldDataEntry.class) {
          return isTicketConfidential(((FieldDataEntry)entry).getCategoryId());
        }
        ticketEntry = ticketService.findTicketById(ticketCacheService.findTicketId((BaseOdinEntry) entry));
      }
      return ticketEntry.isConfidential();
    }
    return false;
  }


  private boolean isTicketConfidential(Ticket ticket) throws BaseException {
    if(ticket == null || ticket.getCategory() == null) {
      return false;
    }
    Boolean isConfidential = ticket.getCategory().getIsConfidential();
    if (isConfidential == null) {
      return false;
    }
    return isConfidential;
  }

  private boolean isTicketConfidential(Long categoryId) throws BaseException {
    if (categoryId == null) {
      return false;
    }
    Boolean isConfidential = categoryService.findOneById(categoryId).getIsConfidential();
    if (isConfidential == null) {
      return false;
    }
    return isConfidential;
  }
}