package com.curefit.odin.user.service;

import com.curefit.odin.enums.TicketDest;
import com.curefit.odin.enums.TicketSource;
import com.curefit.odin.integration.freshdesk.service.*;
import com.curefit.odin.user.pojo.TicketEntry;
import com.curefit.odin.utils.pojo.ExternalTicketResponse;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ExternalTicketServices {

    Map<TicketDest, ExternalTicketService> externalTicketServices = new EnumMap<>(TicketDest.class);
    Map<TicketSource, ExternalTicketService> externalTicketServicesForSource = new EnumMap<>(TicketSource.class);

    public ExternalTicketServices(FreshdeskTicketService freshdeskTicketService, HRFreshdeskTicketService hrFreshdeskTicketService, PartnerFreshdeskTicketService partnerFreshdeskTicketService, SugarfitFreshdeskTicketService sugarfitFreshdeskTicketService, SprinklrService sprinklrService) {
        externalTicketServices.put(TicketDest.CUSTOMER_SUPPORT, freshdeskTicketService);
        externalTicketServices.put(TicketDest.SUGARFIT, sugarfitFreshdeskTicketService);
        externalTicketServices.put(TicketDest.SPRINKLR, sprinklrService);
        externalTicketServicesForSource.put(TicketSource.CUSTOMER_SUPPORT, freshdeskTicketService);
        externalTicketServicesForSource.put(TicketSource.SPRINKLR, sprinklrService);
        externalTicketServicesForSource.put(TicketSource.SUGARFIT, sugarfitFreshdeskTicketService);
        externalTicketServicesForSource.put(TicketSource.HR_SUPPORT, hrFreshdeskTicketService);
        externalTicketServicesForSource.put(TicketSource.PARTNER_SUPPORT, partnerFreshdeskTicketService);
    }

    public ExternalTicketResponse createTicket(String namespace, TicketEntry ticketEntry) {
        log.info("createTicket {} {}", namespace, ticketEntry);
        return externalTicketServices.values().stream()
                .filter(externalTicketService -> externalTicketService != null && externalTicketService.isTicketNeedsToCreate(namespace, ticketEntry))
                .findFirst()
                .map(externalTicketService -> externalTicketService.createTicket(namespace, ticketEntry)).orElse(null);
    }

    public void updateTicket(TicketEntry ticketEntry) {
        if (ticketEntry.getDest() != null && !StringUtils.isEmpty(ticketEntry.getDestRefId())) {
            ExternalTicketService destinationExtService = externalTicketServices.get(ticketEntry.getDest());
            if (destinationExtService != null) {
                destinationExtService.updateTicket(ticketEntry.getDestRefId(), ticketEntry);
                log.info("updated ticket in external ticket service: {} with destRefId: {}", destinationExtService.getClass().getName(), ticketEntry.getDestRefId());
            }
        }
        if (ticketEntry.getSource() != null && !StringUtils.isEmpty(ticketEntry.getSourceRefId())) {
            ExternalTicketService externalTicketService = externalTicketServicesForSource.get(ticketEntry.getSource());
            if (externalTicketService != null) {
                externalTicketService.updateTicket(ticketEntry.getSourceRefId(), ticketEntry);
                log.info("updated ticket in external ticket service: {} with sourceRefId: {}", externalTicketService.getClass().getName(), ticketEntry.getSourceRefId());
            }
        }
    }

    public void updateAssignees(TicketEntry ticketEntry) throws Exception {
        if (ticketEntry.getDest() != null && !StringUtils.isEmpty(ticketEntry.getDestRefId())) {
            ExternalTicketService destinationExtService = externalTicketServices.get(ticketEntry.getDest());
            if (destinationExtService != null) {
                destinationExtService.updateAssignees(ticketEntry.getDestRefId(), ticketEntry);
            }
        }
        if (ticketEntry.getSource() != null && !StringUtils.isEmpty(ticketEntry.getSourceRefId())) {
            ExternalTicketService externalTicketService = externalTicketServicesForSource.get(ticketEntry.getSource());
            if (externalTicketService != null) {
                externalTicketService.updateAssignees(ticketEntry.getSourceRefId(), ticketEntry);
                log.info("updated assignee in external ticket service: {} with sourceRefId: {}", externalTicketService.getClass().getName(), ticketEntry.getSourceRefId());
            }
        }
    }

    public void updateStatus(TicketEntry ticketEntry) {
        if (ticketEntry.getSource() != null && !StringUtils.isEmpty(ticketEntry.getSourceRefId())) {
            ExternalTicketService externalTicketService = externalTicketServicesForSource.get(ticketEntry.getSource());
            if (externalTicketService != null) {
                externalTicketService.updateStatus(ticketEntry.getSourceRefId(), ticketEntry);
                log.info("updated status in external ticket service: {} with sourceRefId: {}", externalTicketService.getClass().getName(), ticketEntry.getSourceRefId());
            }
        }
    }

    public void updateStatusForDest(TicketEntry ticketEntry) {
        if (ticketEntry.getDest() != null && !StringUtils.isEmpty(ticketEntry.getDestRefId())) {
            ExternalTicketService externalTicketService = externalTicketServices.get(ticketEntry.getDest());
            if (externalTicketService != null) {
                externalTicketService.updateStatusForDest(ticketEntry.getDestRefId(), ticketEntry);
                log.info("updated status for dest in external ticket service: {} with destRefId {}", externalTicketService.getClass().getName(), ticketEntry.getDestRefId());
            }
        }
    }

    public void addComment(String namespace, TicketEntry ticketEntry, String message) {
        if (ticketEntry.getDest() != null && !StringUtils.isEmpty(ticketEntry.getDestRefId())) {
            ExternalTicketService externalTicketService = externalTicketServices.get(ticketEntry.getDest());
            if (externalTicketService != null) {
                Long externalId = externalTicketService.addComment(namespace, ticketEntry.getDestRefId(), message);
                log.info("added comment in external ticket service {} with id {}", externalTicketService.getClass().getName(), externalId);
            }
        } else if (ticketEntry.getSource() != null && !StringUtils.isEmpty(ticketEntry.getSourceRefId())) {
            addPrivateNoteAndUpdateStatusForSource(namespace, ticketEntry, message);
        }
    }

    private void addPrivateNoteAndUpdateStatusForSource(String namespace, TicketEntry ticketEntry, String message) {
        ExternalTicketService externalTicketService = externalTicketServicesForSource.get(ticketEntry.getSource());
        if (externalTicketService != null) {
            externalTicketService.addPrivateNoteAndUpdateStatus(namespace, ticketEntry.getSourceRefId(), ticketEntry, message);
            log.info("added private note and update status in external ticket service: {} with sourceRefId: {}", externalTicketService.getClass().getName(), ticketEntry.getSourceRefId());
        }
    }

    public void addMessage(String namespace, TicketEntry ticketEntry, String message) {
        if (ticketEntry.getDest() != null && !StringUtils.isEmpty(ticketEntry.getDestRefId())) {
            ExternalTicketService externalTicketService = externalTicketServices.get(ticketEntry.getDest());
            if (externalTicketService != null) {
                Long externalId = externalTicketService.addComment(namespace, ticketEntry.getDestRefId(), message);
                log.info("added message in external ticket service {} with id {}", externalTicketService.getClass().getName(), externalId);
            }
        } else if (ticketEntry.getSource() != null && !StringUtils.isEmpty(ticketEntry.getSourceRefId())) {
            addPrivateNoteForSource(namespace, ticketEntry, message);
        }
    }

    private void addPrivateNoteForSource(String namespace, TicketEntry ticketEntry, String message) {
        ExternalTicketService externalTicketService = externalTicketServicesForSource.get(ticketEntry.getSource());
        if (externalTicketService != null) {
            externalTicketService.addPrivateNote(namespace, ticketEntry.getSourceRefId(), message);
            log.info("added private note in external ticket service: {} with sourceRefId: {}", externalTicketService.getClass().getName(), ticketEntry.getSourceRefId());
        }
    }

}
