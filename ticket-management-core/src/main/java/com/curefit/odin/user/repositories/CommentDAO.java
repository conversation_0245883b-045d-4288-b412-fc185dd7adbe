package com.curefit.odin.user.repositories;

import java.util.List;
import java.util.Optional;

import com.curefit.commons.sf.repository.BaseMySQLRepository;
import com.curefit.odin.user.models.Comment;

public interface CommentDAO extends BaseMySQLRepository<Comment> {

	List<Comment> findByTicketId(Long id);

	List<Comment> findByTicketIdAndActive(Long id, Boolean active);

	List<Comment> findByTicketIdAndActiveAndIsInternal(Long id, Boolean active, Boolean isInternal);

	List<Comment> findAllByParentCommentId(Long id);

	Optional<Comment> findTopByTicketIdAndCreatedByOrderByIdDesc(Long  id, String createdBy);

}
