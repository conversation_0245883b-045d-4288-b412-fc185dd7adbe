package com.curefit.odin.user.models;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.curefit.cf.commons.pojo.crypto.EncryptionMode;
import com.curefit.commons.sf.audit.annotation.Audit;
import com.curefit.commons.sf.audit.annotation.EnableEncryption;
import com.curefit.commons.sf.audit.annotation.Encrypt;
import com.curefit.odin.admin.models.BaseMySQLModel;
import com.curefit.odin.admin.models.CustomField;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "field_data",
    uniqueConstraints = {@UniqueConstraint(columnNames = {"ticket_id", "field_id"})})
@EnableEncryption(mode = EncryptionMode.CONFIDENTIAL)
@Audit
public class FieldData extends BaseMySQLModel {

  /**
  * 
  */
  private static final long serialVersionUID = -845572440227226094L;

  @Encrypt
  @Column(name = "value")
  String value;

  @ManyToOne
  @JoinColumn(name = "ticket_id", referencedColumnName = "id")
  Ticket ticket;

  @ManyToOne
  @JoinColumn(name = "field_id", referencedColumnName = "id")
  CustomField field;

}
