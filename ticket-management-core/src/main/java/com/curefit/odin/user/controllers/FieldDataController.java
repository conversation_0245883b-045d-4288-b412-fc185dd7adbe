package com.curefit.odin.user.controllers;

import java.util.List;

import com.curefit.common.data.exception.BaseException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import com.curefit.odin.admin.controllers.BaseOdinController;
import com.curefit.odin.user.models.FieldData;
import com.curefit.odin.user.pojo.FieldDataEntry;
import com.curefit.odin.user.service.FieldDataService;

@RestController
@RequestMapping("/field_data")
public class FieldDataController extends BaseOdinController<FieldData, FieldDataEntry> {

  public FieldDataController(FieldDataService fieldDataService) {
    super(fieldDataService);
  }

  @RequestMapping(method = RequestMethod.GET, value = "tickets/{id}")
  public ResponseEntity<List<FieldDataEntry>> fetchDetail(@PathVariable Long id) throws BaseException {
    return new ResponseEntity<>(((FieldDataService) baseMySQLService).fetchFieldDataByTicketId(id),
        HttpStatus.OK);
  }
}
