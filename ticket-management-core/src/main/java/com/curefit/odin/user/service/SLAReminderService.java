package com.curefit.odin.user.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.odin.notification.NotificationHandler;
import com.curefit.odin.user.pojo.TicketEntry;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SLAReminderService {

  @Autowired
  TicketService ticketService;

  @Autowired
  NotificationHandler notificationHandler;

  @Transactional(rollbackFor = Exception.class)
  public void handleSLAReminderCallback() throws BaseException {
    List<TicketEntry> ticketEntries = ticketService.fetchOpenTicketsToSendSlaReminder();
    for (TicketEntry ticketEntry : ticketEntries) {
      this.notificationHandler.handleTicketSLAReminder(ticketEntry);
      ticketService.markSLAReminderSent(ticketEntry.getId());
    }
  }
}
