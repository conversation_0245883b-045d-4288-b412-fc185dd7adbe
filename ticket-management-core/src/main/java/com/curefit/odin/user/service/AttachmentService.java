package com.curefit.odin.user.service;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.odin.commons.Constants;
import com.curefit.odin.user.models.Attachment;
import com.curefit.odin.user.pojo.AttachmentEntry;
import com.curefit.odin.user.pojo.CommentEntry;
import com.curefit.odin.user.pojo.TicketEntry;
import com.curefit.odin.user.repositories.AttachmentDAO;
import com.curefit.odin.utils.AuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.net.URL;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Service
@Slf4j
public class AttachmentService extends BaseMySQLService<Attachment, AttachmentEntry> implements TicketCacheService<AttachmentEntry> {

    @Value("${external.neo.baseUrl}")
    private String baseUrl;

    @Value("${attachment.s3.bucket}")
    private String s3BucketName;

    @Autowired
    private TicketService ticketService;

    @Autowired
    private CommentService commentService;

    @Autowired
    RollbarService rollbarService;

    @Autowired
    AuthService authService;

    @Autowired
    AmazonS3 amazonS3;

    public AttachmentService(AttachmentDAO attachmentDao) {
        super(attachmentDao);
    }

    @Override
    public Attachment convertToEntity(AttachmentEntry entry) {
        Attachment convertedEntity = super.convertToEntity(entry);
        if (entry.getTicketId() != null) {
            try {
                convertedEntity.setTicket(ticketService.fetchEntityById(entry.getTicketId()));
            } catch (BaseException e) {
                log.error(e.getMessage(), e);
                rollbarService.error(e);
            }
        }
        if (entry.getCommentId() != null) {
            try {
                convertedEntity.setComment(commentService.fetchEntityById(entry.getCommentId()));
            } catch (BaseException e) {
                log.error(e.getMessage(), e);
                rollbarService.error(e);
            }
        }
        return convertedEntity;
    }

    @Override
    public AttachmentEntry convertToEntry(Attachment entity) {
        AttachmentEntry convertedEntry = super.convertToEntry(entity);
        if (entity.getComment() != null) {
            convertedEntry.setCommentId(entity.getComment().getId());
        }
        if (entity.getTicket() != null) {
            convertedEntry.setTicketId(entity.getTicket().getId());
        }
        return convertedEntry;
    }

    @Override
    public AttachmentEntry create(AttachmentEntry entry) throws BaseException {
        authService.checkAuthorized(entry, this);
        return super.create(entry);
    }

    @Override
    public AttachmentEntry patchUpdate(Long id, AttachmentEntry entry) throws BaseException {
        authService.checkAuthorized(id, this);
        return super.patchUpdate(id, entry);
    }

    @Transactional(rollbackFor = Exception.class)
    public AttachmentEntry deleteById(Long id) throws BaseException {
        AttachmentEntry entry = new AttachmentEntry();
        entry.setActive(false);
        AttachmentEntry updatedAttachmentEntry = patchUpdate(id, entry);
        updateLastModified(updatedAttachmentEntry);
        return updatedAttachmentEntry;
    }

    @Override
    public AttachmentEntry findOneById(Long id) throws BaseException {
        AttachmentEntry entry = super.findOneById(id);
        authService.checkAuthorized(entry, this);
        return entry;
    }

    @Override
    @Cacheable(value = "findTicketIdById", unless = "#result == null")
    public Long findTicketIdById(Long id) throws BaseException {
        AttachmentEntry entry = super.findOneById(id);
        return findTicketId(entry);
    }

    @Override
    public Long findTicketId(AttachmentEntry entry) throws BaseException {
        if (entry.getTicketId() != null) {
            return entry.getTicketId();
        } else {
            return commentService.findTicketIdById(entry.getCommentId());
        }
    }

    private void updateLastModified(AttachmentEntry attachmentEntry) throws BaseException {
        if (attachmentEntry.getCommentId() != null) {
            CommentEntry commentEntry = new CommentEntry();
            commentEntry.setLastModifiedOn(attachmentEntry.getLastModifiedOn());
            commentService.patchUpdate(attachmentEntry.getCommentId(), commentEntry);
        } else {
            TicketEntry ticketEntry = new TicketEntry();
            ticketEntry.setLastModifiedOn(attachmentEntry.getLastModifiedOn());
            ticketService.patchUpdate(attachmentEntry.getTicketId(), ticketEntry);
        }
    }

    public List<AttachmentEntry> addAttachmentsToTicket(Long ticketId, List<AttachmentEntry> attachmentEntries) throws BaseException {
        authService.checkAuthorized(ticketId, this.ticketService);
        attachmentEntries.forEach(attachment -> attachment.setTicketId(ticketId));
        sanitizeAttachments(ticketId, attachmentEntries);
        return bulkCreate(attachmentEntries);
    }

    private void sanitizeAttachments(Long ticketId, List<AttachmentEntry> attachmentEntries) {
        attachmentEntries.stream().filter(AttachmentEntry::isURLAbsolute)
                .forEach(attachmentEntry -> {
                    try (InputStream inputStream = new URL(attachmentEntry.getUrl()).openStream()) {
                        String attachmentUrlKey = UUID.randomUUID().toString() + getURLExtension(attachmentEntry.getUrl());
                        amazonS3.putObject(s3BucketName, attachmentUrlKey, inputStream, null);
                        log.info("Object uploaded in s3 bucket successfully for ticketId: {}, url: {}, attachmentUrlKey: {}", ticketId, attachmentEntry.getUrl(), attachmentUrlKey);
                        attachmentEntry.setUrl(attachmentUrlKey);
                    } catch (Exception e) {
                        log.error("Error while uploading url: {} to s3 bucket for ticketId: {}, error: {}", attachmentEntry.getUrl(), ticketId, e);
                    }
                });
    }

    private String getURLExtension(String url) {
        url = url.substring(0, url.indexOf("?"));
        return url.substring(url.lastIndexOf("."));
    }

    public String getS3SignedUrl(String fileKey) throws BaseException {
        return getS3SignedUrlv2(fileKey, s3BucketName);
    }
    public String getS3SignedUrlv2(String fileKey, String bucket) throws BaseException {
        log.info("Generating signed url for http method: {}. Generated object key was: {}.", com.amazonaws.HttpMethod.GET, fileKey);
        int retryCount = 1;
        while (true) {
            try {
                GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest(bucket, fileKey)
                        .withMethod(com.amazonaws.HttpMethod.GET)
                        .withExpiration(getExpirationTimeForPreSignedUrl(Constants.S3_BUCKET_EXPIRATION_MILLIS));
                URL preSignedUrl = amazonS3.generatePresignedUrl(generatePresignedUrlRequest);
                log.info("The generated pre signed url was: {}.", preSignedUrl.toString());
                return preSignedUrl.toString();
            } catch (RuntimeException e) {
                log.error("Error in generating s3 pre-signed url. Retry count {}", retryCount, e);
                if (retryCount == Constants.S3_GET_PRE_SIGNED_URL_RETRY) {
                    throw new BaseException("Error in generating s3 pre-signed url", e);
                } else {
                    retryCount++;
                }
            }
        }
    }

    public String getS3SignedUrl(String fileKey, String bucket) throws BaseException {
        log.info("Generating signed url for http method: {}. Generated object key was: {}.", com.amazonaws.HttpMethod.PUT, fileKey);
        int retryCount = 1;
        while (true) {
            try {
                GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest(bucket, fileKey)
                        .withMethod(com.amazonaws.HttpMethod.PUT)
                        .withExpiration(getExpirationTimeForPreSignedUrl(Constants.S3_BUCKET_EXPIRATION_MILLIS));
                URL preSignedUrl = amazonS3.generatePresignedUrl(generatePresignedUrlRequest);
                log.info("The generated pre signed url was: {}.", preSignedUrl.toString());
                return preSignedUrl.toString();
            } catch (RuntimeException e) {
                log.error("Error in generating s3 pre-signed url. Retry count {}", retryCount, e);
                if (retryCount == Constants.S3_GET_PRE_SIGNED_URL_RETRY) {
                    throw new BaseException("Error in generating s3 pre-signed url", e);
                } else {
                    retryCount++;
                }
            }
        }
    }

    private Date getExpirationTimeForPreSignedUrl(long expiryTime) {
        Date expiration = new Date();
        long expTimeMillis = expiration.getTime() + expiryTime;
        expiration.setTime(expTimeMillis);
        return expiration;
    }
}
