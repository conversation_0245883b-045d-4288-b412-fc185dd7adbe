package com.curefit.odin.user.repositories;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import com.curefit.commons.sf.repository.BaseMySQLRepository;
import com.curefit.odin.user.models.FieldData;

@Repository
public interface FieldDataDAO extends BaseMySQLRepository<FieldData> {

  List<FieldData> findAllByTicketId(Long ticketId);

  Optional<FieldData> findById(Long id);

  @Query(value = "select distinct ticket_id from field_data " +
      "where field_id = :fieldId and value in :values", nativeQuery = true)
  List<Long> findTicketIdsByFieldIdAndValues(Long fieldId, List<String> values);

  @Query(value = "select distinct ticket_id from field_data " +
      "where ticket_id in :ticketIds and field_id = :fieldId and value in :values", nativeQuery = true)
  List<Long> findTicketIdsByFieldIdAndValues(List<Long> ticketIds, Long fieldId, List<String> values);

  @Query(value = "select distinct ticket_id from field_data " +
      "where field_id = :fieldId and DATE(STR_TO_DATE(substr(value,1, 16), '%Y-%m-%dT%H:%i')) = :value", nativeQuery = true)
  List<Long> findTicketIdsByFieldIdAndDateValue(Long fieldId, String value);

  @Query(value = "select distinct ticket_id from field_data " +
      "where ticket_id in :ticketIds and field_id = :fieldId and DATE(STR_TO_DATE(substr(value,1, 16), '%Y-%m-%dT%H:%i')) = :value", nativeQuery = true)
  List<Long> findTicketIdsByFieldIdAndDateValue(List<Long> ticketIds, Long fieldId, String value);

  @Query(value = "select distinct ticket_id from field_data " +
      "where field_id = :fieldId and TIME(STR_TO_DATE(substr(value,1, 19), '%Y-%m-%dT%H:%i')) = :value", nativeQuery = true)
  List<Long> findTicketIdsByFieldIdAndTimeValue(Long fieldId, String value);

  @Query(value = "select distinct ticket_id from field_data " +
      "where ticket_id in :ticketIds and field_id = :fieldId and TIME(STR_TO_DATE(substr(value,1, 19), '%Y-%m-%dT%H:%i')) = :value", nativeQuery = true)
  List<Long> findTicketIdsByFieldIdAndTimeValue(List<Long> ticketIds, Long fieldId, String value);

  @Query(value = "select distinct ticket_id from field_data " +
      "where field_id = :fieldId and STR_TO_DATE(substr(value,1, 16), '%Y-%m-%dT%H:%i') = :value", nativeQuery = true)
  List<Long> findTicketIdsByFieldIdAndDateTimeValue(Long fieldId, String value);

  @Query(value = "select distinct ticket_id from field_data " +
      "where ticket_id in :ticketIds and field_id = :fieldId and STR_TO_DATE(substr(value,1, 16), '%Y-%m-%dT%H:%i') = :value", nativeQuery = true)
  List<Long> findTicketIdsByFieldIdAndDateTimeValue(List<Long> ticketIds, Long fieldId, String value);

}
