package com.curefit.odin.user.controllers;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.curefit.odin.admin.controllers.BaseOdinController;
import com.curefit.odin.admin.models.Filter;
import com.curefit.odin.admin.pojo.FilterEntry;
import com.curefit.odin.admin.service.FilterService;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/filter")
@Slf4j
public class FilterController extends BaseOdinController<Filter, FilterEntry> {

  public FilterController(FilterService filterService) {
    super(filterService);
  }
}
