package com.curefit.odin.user.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.freshdesk.enums.TicketStatus;
import com.curefit.odin.enums.Status;
import com.curefit.odin.enums.TicketDest;
import com.curefit.odin.enums.TicketSource;
import com.curefit.odin.user.pojo.CommentEntry;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class FreshdeskUpdatesHandler {

  @Autowired
  TicketService ticketService;

  @Autowired
  CommentService commentService;

  @Transactional(rollbackFor = Exception.class)
  public void handleFDTicketStatusUpdate(String fdTicketId, TicketStatus ticketStatus, String freshdeskNamespace) throws BaseException {
    freshdeskNamespace = StringUtils.defaultString(freshdeskNamespace);
    Long ticketId = ticketService.fetchTicketIdByDestRef(TicketDest.DEST_MAP.getOrDefault(freshdeskNamespace, TicketDest.CUSTOMER_SUPPORT), fdTicketId);
    if (ticketId != null) {
      if (ticketStatus == TicketStatus.Resolved || ticketStatus == TicketStatus.Closed) {
        ticketService.updateStatus(ticketId, Status.RESOLVED);
      }
    } else {
      ticketId = ticketService.fetchTicketIdBySourceRef(TicketSource.SOURCE_MAP.getOrDefault(freshdeskNamespace, TicketSource.CUSTOMER_SUPPORT), fdTicketId);
      if (ticketId != null) {
        CommentEntry commentEntry = new CommentEntry();
        commentEntry.setTicketId(ticketId);
        commentEntry.setComment("Customer Support Ticket status updated to " + ticketStatus);
        commentService.create(commentEntry);
      }
    }
  }
}
