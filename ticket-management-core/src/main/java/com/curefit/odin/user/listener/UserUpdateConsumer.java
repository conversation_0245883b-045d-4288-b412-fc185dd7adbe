package com.curefit.odin.user.listener;

import com.amazonaws.regions.Regions;
import com.amazonaws.services.sqs.model.Message;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.messaging.consumer.BaseSqsConsumer;
import com.curefit.odin.admin.service.UserService;
import com.curefit.odin.config.OdinConfigurations;
import com.curefit.odin.user.pojo.*;
import com.curefit.odin.user.service.TicketFilterService;
import com.curefit.odin.user.service.external.NeoService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
@Profile("!local & !alpha")
public class UserUpdateConsumer extends BaseSqsConsumer {


    @Autowired
    TicketFilterService ticketFilterService;

    @Autowired
    RollbarService rollbarService;

    @Autowired
    NeoService neoService;

    static ObjectMapper objectMapper = new ObjectMapper();

    public UserUpdateConsumer(OdinConfigurations odinConfigurations) {
        super(odinConfigurations.getUpdateQueueName(), Regions.AP_SOUTH_1, odinConfigurations.getNeoEmployeeUpdateEventsQueueWaitTimeInSec(),
                odinConfigurations.getNeoEmployeeUpdateEventsQueueBatchSize());
    }

    @Override
    public List<Boolean> process(List<Message> list) {
        List<Boolean> isProcessed = new ArrayList<>(list.size());
        for (Message message : list) {
            try {
                TicketSystemQueueRequest ticketSystemQueueRequest = objectMapper.readValue(message.getBody(), TicketSystemQueueRequest.class);
                EmployeeDetails employee = ticketSystemQueueRequest.getEmployee().getEmployee();
                String employeeStatus = "";
                List<AdditionalDetailsBlock> detailsBlockList = ticketSystemQueueRequest.getEmployee().getDetailsCollection();
                for (AdditionalDetailsBlock adb : detailsBlockList) {
                    if (adb.getAttributeType().getFieldName().equals("EmployeeStatus")) {
                        employeeStatus = adb.getAdditionalDetails().getAttributeNumber();
                    }
                }
                if (employeeStatus.equals("InActive")) {
                    String userId = employee.getEmail();

                    TicketFilterRequestEntry filterRequest = new TicketFilterRequestEntry();
                    filterRequest.setIsAssignedToMe(true);
                    Map<Integer, List<String>> employeeManagersByEmail = neoService.getEmployeeManagersByEmail(userId, Collections.singletonList(1));
                    Set<String> result = new HashSet<>();
                    employeeManagersByEmail.forEach((level, managerEmails) -> result.addAll(managerEmails));

                    BulkUpdateTicketAssigneeRequest request = new BulkUpdateTicketAssigneeRequest();
                    request.setFilter(filterRequest);
                    request.setUserIds(new ArrayList<>(result));

                    ticketFilterService.bulkUpdateAssignees(userId, ticketSystemQueueRequest.getTenant(), request);
                }
                isProcessed.add(Boolean.TRUE);
            } catch (Exception e) {
                String errorMsg = "Error while processing the employee update event";
                isProcessed.add(Boolean.FALSE);
                rollbarService.error(e, errorMsg);
                log.error(errorMsg, e);
            }
        }
        return isProcessed;
    }

}
