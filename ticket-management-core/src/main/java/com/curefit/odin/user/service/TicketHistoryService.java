package com.curefit.odin.user.service;

import com.curefit.cf.commons.pojo.audit.Change;
import com.curefit.cf.commons.pojo.audit.EventType;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.crypto.DecryptionUtils;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.pojo.UserEntry;
import com.curefit.odin.admin.service.UserService;
import com.curefit.odin.audit.AuditEventsService;
import com.curefit.odin.audit.pojo.AuditEventEntry;
import com.curefit.odin.audit.pojo.TicketRevision;
import com.curefit.odin.user.models.Ticket;
import com.curefit.odin.user.pojo.TicketEntry;
import com.curefit.odin.utils.AuthService;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.security.GeneralSecurityException;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TicketHistoryService {

  static final String TRUE = "true";

  static final String FALSE = "false";

  static final String ACTIVE_FIELD = "active";

  @Autowired
  RollbarService rollbarService;

  @Autowired
  AuditEventsService auditEventsService;

  @Autowired
  UserService userService;

  @Autowired
  AuthService authService;

  @Autowired
  TicketService ticketService;

  @Autowired
  DecryptionUtils decryptionUtils;

  public List<TicketRevision> fetchHistory(Long ticketId) throws BaseException {
    authService.checkAuthorized(ticketId, ticketService);

    TicketEntry ticketEntry = ticketService.findTicketById(ticketId);
    boolean isConfidential = ticketEntry.isConfidential();

    String ticketSearchByEntityIdQuery = "entityId.eq:" + ticketId + ";" + "entity.eq:" + Ticket.class.getSimpleName();
    String ticketSearchByParentIdQuery = "parentId.eq:" + ticketId + ";" + "parentEntity.eq:" + Ticket.class.getSimpleName();

    try {
      List<AuditEventEntry> auditEventEntries = auditEventsService.search(0, -1, null, null, ticketSearchByEntityIdQuery).getElements();
      auditEventEntries.addAll(auditEventsService.search(0, -1, null, null, ticketSearchByParentIdQuery).getElements());

      return auditEventEntries.stream()
              .map(entry -> getRevisionData(entry, isConfidential))
              .sorted((o1, o2) -> o1.getModifiedOn().equals(o2.getModifiedOn()) ? 0 : o1.getModifiedOn().after(o2.getModifiedOn()) ? 1 : -1)
              .collect(Collectors.toList());

    } catch (InvalidSeachQueryException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }

    return Collections.emptyList();
  }

  private TicketRevision getRevisionData(AuditEventEntry auditEventEntry, boolean isConfidential) {

    TicketRevision ticketRevision = new TicketRevision();
    ticketRevision.setAction(auditEventEntry.getEventType());

    List<Change> changes = auditEventEntry.getChangeList();
    for (Change change : changes) {
      if (ACTIVE_FIELD.equalsIgnoreCase(change.getField()) && TRUE.equals(change.getFrom()) && FALSE.equals(change.getTo())) {
        ticketRevision.setAction(EventType.DELETE);
      }
      if (ACTIVE_FIELD.equalsIgnoreCase(change.getField()) && FALSE.equals(change.getFrom()) && TRUE.equals(change.getTo())) {
        ticketRevision.setAction(EventType.CREATE);
      }
      if (isConfidential) {
        try {
          if ("comment".equals(change.getField()) || "description".equals(change.getField()) || "title".equals(change.getField())) {
            if (StringUtils.isNotBlank(change.getFrom())) {
              change.setFrom((String) decryptionUtils.decrypt(change.getFrom()));
            }
            if (StringUtils.isNotBlank(change.getTo())) {
              change.setTo((String) decryptionUtils.decrypt(change.getTo()));
            }
          }
        } catch (GeneralSecurityException e) {
          log.error(e.getMessage(), e);
        }
      }
      change.setField(StringUtils.capitalize(change.getField()));
    }
    if (ticketRevision.getAction() == EventType.UPDATE) {
      ticketRevision.setChanges(auditEventEntry.getChangeList());
    }
    ticketRevision.setEntity(auditEventEntry.getEntity());
    ticketRevision.setModifiedOn(auditEventEntry.getModifiedOn());
    ticketRevision.setActionText(getActionText(ticketRevision.getAction()));

    try {
      ticketRevision.setUser(userService.findUserByMailId(auditEventEntry.getModifiedBy()));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
      ticketRevision.setUser(new UserEntry(auditEventEntry.getModifiedBy(), auditEventEntry.getModifiedBy()));
    }
    return ticketRevision;
  }

  private String getActionText(EventType action) {
    switch (action) {
      case DELETE:
        return "Deleted";
      case UPDATE:
        return "Updated";
      case CREATE:
        return "Added";
    }
    return null;
  }
}
