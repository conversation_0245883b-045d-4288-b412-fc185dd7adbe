package com.curefit.odin.sprinklr.models;

import com.curefit.commons.sf.audit.annotation.Audit;
import com.curefit.odin.admin.models.BaseMySQLModel;
import com.curefit.odin.sprinklr.pojo.webhook.SprinklrContact;
import com.vladmihalcea.hibernate.type.json.JsonStringType;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;
import java.util.List;
import java.util.Map;


@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "sprinklr_ticket")
@TypeDefs({@TypeDef(name = "json", typeClass = JsonStringType.class)})
@Where(clause = "deleted_on is NULL")
@Audit
public class SprinklrTicket extends BaseMySQLModel {

    @Column(name = "user_id")
    String userId;

    @Column(name = "case_id")
    String caseId;

    @Column(name = "case_number")
    String caseNumber;

    String subject;

    String description;

    String status;

    @Type(type = "json")
    @Column(name = "custom_properties", columnDefinition = "json")
    Map<String, List<Object>> customProperties;

    @Type(type = "json")
    @Column(columnDefinition = "json")
    SprinklrContact contact;

    @Column(name = "first_message_id")
    String firstMessageId;

    @Column(name = "conversation_id")
    String conversationId;

    @Column(name = "is_ticket")
    Boolean isTicket;

    @Column(name = "deleted_on")
    Date deletedOn;

    @Column(name = "odin_ticket_id")
    String odinTicketId;

    String email;
}
