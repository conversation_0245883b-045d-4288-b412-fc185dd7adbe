package com.curefit.odin.sprinklr.service;


import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.odin.sprinklr.models.SprinklrCultConfiguration;
import com.curefit.odin.sprinklr.pojo.SprinklrCultConfigurationEntry;
import com.curefit.odin.sprinklr.repositories.SprinklrCultConfigurationRepository;
import com.curefit.odin.utils.OdinStringUtils;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SprinklrCultConfigurationService extends BaseMySQLService<SprinklrCultConfiguration, SprinklrCultConfigurationEntry> {

    public SprinklrCultConfigurationService(SprinklrCultConfigurationRepository sprinklrCultConfigurationRepository) {
        super(sprinklrCultConfigurationRepository);
    }

    public SprinklrCultConfigurationEntry getSprinklrCultConfiguration(String liveChatL1, String liveChatSubL1, String status, String liveChatL2) {
        if (OdinStringUtils.isAnyEmpty(liveChatL1, liveChatSubL1, status, liveChatL2)) {
            return null;
        }
        return ((SprinklrCultConfigurationRepository) baseMySQLRepository)
                .findFirstByL1AndSubL1AndStatusAndL2(liveChatL1, liveChatSubL1, status, liveChatL2)
                .map(this::convertToEntry)
                .orElse(null);
    }
}


