package com.curefit.odin.sprinklr.models;

import com.curefit.odin.admin.models.BaseMySQLModel;
import com.curefit.odin.sprinklr.pojo.SchedulingInfo;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.Date;

@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "sprinklr_scheduling")
@Where(clause = "deleted_on is NULL")
@JsonIgnoreProperties(ignoreUnknown = true)
public class SprinklrScheduling extends BaseMySQLModel {

    @Column(name = "center_id")
    Long centerId;

    @Column(name = "workout_id")
    Long workoutId;

    @Column(name = "time_slot_id")
    Long timeSlotId;

    Long counter;

    @Type(type = "json")
    @Column(name = "scheduling_info", columnDefinition = "json")
    SchedulingInfo schedulingInfo;

    @Column(name = "deleted_on")
    Date deletedOn;
}