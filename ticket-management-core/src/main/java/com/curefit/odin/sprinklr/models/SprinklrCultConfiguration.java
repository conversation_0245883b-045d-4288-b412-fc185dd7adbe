package com.curefit.odin.sprinklr.models;

import com.curefit.cf.commons.pojo.crypto.EncryptionMode;
import com.curefit.commons.sf.audit.annotation.Audit;
import com.curefit.commons.sf.audit.annotation.EnableEncryption;
import com.curefit.odin.admin.models.BaseMySQLModel;
import com.curefit.odin.enums.SprinklrConfigurationActionType;
import com.curefit.odin.sprinklr.pojo.SprinklrCultConfigurationAction;
import com.vladmihalcea.hibernate.type.json.JsonStringType;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.Date;


@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "sprinklr_cult_configuration")
@Audit
@TypeDefs({@TypeDef(name = "json", typeClass = JsonStringType.class)})
@EnableEncryption(mode = EncryptionMode.CONFIDENTIAL)
@Where(clause = "deleted_on is NULL")
public class SprinklrCultConfiguration extends BaseMySQLModel {

    String l1;

    @Column(name = "sub_l1")
    String subL1;

    String status;

    String l2;

    @Column(name = "action_type")
    @Enumerated(EnumType.STRING)
    SprinklrConfigurationActionType actionType;

    @Type(type = "json")
    @Column(name = "action_value", columnDefinition = "json")
    SprinklrCultConfigurationAction actionValue;

    @Column(name = "deleted_on")
    Date deletedOn;

    String image;
}
