package com.curefit.odin.sprinklr.models;

import com.curefit.cf.commons.pojo.crypto.EncryptionMode;
import com.curefit.commons.sf.audit.annotation.Audit;
import com.curefit.commons.sf.audit.annotation.EnableEncryption;
import com.curefit.odin.admin.models.BaseMySQLModel;
import com.vladmihalcea.hibernate.type.json.JsonStringType;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;


@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "sprinklr_cult_custom_field_mapping")
@Audit
@TypeDefs({@TypeDef(name = "json", typeClass = JsonStringType.class)})
@EnableEncryption(mode = EncryptionMode.CONFIDENTIAL)
@Where(clause = "deleted_on is NULL")
public class SprinklrCultCustomFieldMapping extends BaseMySQLModel {

    @Column(name = "sprinklr_custom_field")
    String sprinklrCustomField;

    @Column(name = "cult_custom_field")
    String cultCustomField;

    @Column(name = "deleted_on")
    Date deletedOn;
}
