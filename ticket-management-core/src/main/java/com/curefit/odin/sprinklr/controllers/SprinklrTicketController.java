package com.curefit.odin.sprinklr.controllers;

import com.curefit.odin.admin.controllers.BaseOdinController;
import com.curefit.odin.sprinklr.models.SprinklrTicket;
import com.curefit.odin.sprinklr.pojo.SprinklrTicketEntry;
import com.curefit.odin.sprinklr.pojo.message.SprinklrMessageCreateRequest;
import com.curefit.odin.sprinklr.pojo.message.SprinklrMessageRequest;
import com.curefit.odin.sprinklr.pojo.webhook.SprinklrCommentWebhookRequest;
import com.curefit.odin.sprinklr.pojo.webhook.SprinklrTicketWebhookRequest;
import com.curefit.odin.sprinklr.service.SprinklrTicketService;
import org.hibernate.StaleObjectStateException;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.orm.hibernate5.HibernateOptimisticLockingFailureException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Configuration
@RestController
@Validated
@RequestMapping("/sprinklr-ticket")
public class SprinklrTicketController extends BaseOdinController<SprinklrTicket, SprinklrTicketEntry> {

    public SprinklrTicketController(SprinklrTicketService sprinklrTicketService) {
        super(sprinklrTicketService);
    }

    @RequestMapping(method = RequestMethod.POST, value = "/webhook")
    public ResponseEntity<?> processSprinklrWebhook(@RequestBody SprinklrTicketWebhookRequest sprinklrTicketWebhookRequest) throws Exception {
        ((SprinklrTicketService) baseMySQLService).processSprinklrWebhook(sprinklrTicketWebhookRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.POST, value = "/add-message")
    public ResponseEntity<?> addMessage(@RequestBody @Valid SprinklrMessageCreateRequest sprinklrMessageCreateRequest) throws Exception {
        ((SprinklrTicketService) baseMySQLService).addMessage(sprinklrMessageCreateRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.POST, value = "/comment-webhook")
    public ResponseEntity<?> processSprinklrCommentWebhook(@RequestBody SprinklrCommentWebhookRequest sprinklrCommentWebhookRequest) throws Exception {
        ((SprinklrTicketService) baseMySQLService).processCommentWebhook(sprinklrCommentWebhookRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.POST, value = "/reopen")
    @Retryable(value = {ObjectOptimisticLockingFailureException.class, StaleObjectStateException.class, HibernateOptimisticLockingFailureException.class}, maxAttempts = 3, backoff = @Backoff(delay = 500))
    public ResponseEntity<?> reOpenTicket(@RequestBody @Valid SprinklrMessageCreateRequest request) throws Exception {
        ((SprinklrTicketService) baseMySQLService).reOpenTicket(request);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.POST, value = "/fetch-message-conversations")
    public ResponseEntity<?> getMessageConversations(@RequestBody @Valid SprinklrMessageRequest request) throws Exception {
        return new ResponseEntity<>(((SprinklrTicketService) baseMySQLService).getMessageConversations(request), HttpStatus.OK);
    }
}
