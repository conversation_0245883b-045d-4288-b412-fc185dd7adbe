package com.curefit.odin.sprinklr.controllers;

import com.curefit.common.data.exception.BaseException;
import com.curefit.odin.admin.controllers.BaseOdinController;
import com.curefit.odin.sprinklr.models.SprinklrScheduling;
import com.curefit.odin.sprinklr.pojo.SprinklrSchedulingEntry;
import com.curefit.odin.sprinklr.service.SprinklrSchedulingService;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Configuration
@RestController
@RequestMapping("/sprinklr-scheduling")
@Validated
public class SprinklrSchedulingController extends BaseOdinController<SprinklrScheduling, SprinklrSchedulingEntry> {

    public SprinklrSchedulingController(SprinklrSchedulingService sprinklrSchedulingService) {
        super(sprinklrSchedulingService);
    }

    @RequestMapping(method = RequestMethod.POST, value = "/scheduling-ticket-create-flag")
    public String getSchedulingTicketCreateFlag(@RequestParam(value = "centerId") @Valid @NotNull Long centerId, @RequestParam(value = "workoutId") @Valid @NotNull Long workoutId, @RequestParam(value= "timeSlotId") @Valid @NotNull Long timeSlotId, @RequestParam(value =  "userId") @Valid @NotEmpty String userId) throws BaseException {
        return ((SprinklrSchedulingService) baseMySQLService).getSchedulingTicketCreateFlag(centerId, workoutId, timeSlotId, userId);
    }
}