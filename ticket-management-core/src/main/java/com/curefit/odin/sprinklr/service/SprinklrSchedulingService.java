package com.curefit.odin.sprinklr.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.odin.sprinklr.models.SprinklrScheduling;
import com.curefit.odin.sprinklr.pojo.SchedulingInfo;
import com.curefit.odin.sprinklr.pojo.SprinklrSchedulingEntry;
import com.curefit.odin.sprinklr.repositories.SprinklrSchedulingRepository;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.curefit.odin.commons.SprinklrConstants.SCHEDULING_TICKET_CREATE_THRESHOLD;

@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SprinklrSchedulingService extends BaseMySQLService<SprinklrScheduling, SprinklrSchedulingEntry> {

    public SprinklrSchedulingService(SprinklrSchedulingRepository sprinklrSchedulingRepository) {
        super(sprinklrSchedulingRepository);
    }

    public SprinklrSchedulingEntry incrementCounter(Long centerId, Long workoutId, Long timeSlotId, String userId) throws BaseException {
        SprinklrSchedulingEntry sprinklrSchedulingEntry = ((SprinklrSchedulingRepository) baseMySQLRepository)
                .findFirstByCenterIdAndWorkoutIdAndTimeSlotIdOrderByIdDesc(centerId, workoutId, timeSlotId)
                .map(this::convertToEntry)
                .orElse(null);

        if (sprinklrSchedulingEntry == null) {
            return super.create(new SprinklrSchedulingEntry(centerId, workoutId, timeSlotId, 1L, new SchedulingInfo(Collections.singletonList(userId))));
        } else {
            Calendar lastModifiedCalendar = Calendar.getInstance();
            lastModifiedCalendar.setTime(sprinklrSchedulingEntry.getLastModifiedOn());
            if (lastModifiedCalendar.get(Calendar.MONTH) != Calendar.getInstance().get(Calendar.MONTH) || lastModifiedCalendar.get(Calendar.YEAR) != Calendar.getInstance().get(Calendar.YEAR)) {
                sprinklrSchedulingEntry.setCounter(1L);
                sprinklrSchedulingEntry.setSchedulingInfo(new SchedulingInfo(Collections.singletonList(userId)));
            } else {
                if (!sprinklrSchedulingEntry.getSchedulingInfo().getUserIds().contains(userId)) {
                    sprinklrSchedulingEntry.setCounter(sprinklrSchedulingEntry.getCounter() + 1);
                    sprinklrSchedulingEntry.getSchedulingInfo().getUserIds().add(userId);
                }
            }
            return super.patchUpdate(sprinklrSchedulingEntry.getId(), sprinklrSchedulingEntry);
        }
    }

    public String getSchedulingTicketCreateFlag(Long centerId, Long workoutId, Long timeSlotId, String userId) throws BaseException {
        SprinklrSchedulingEntry sprinklrSchedulingEntry = incrementCounter(centerId, workoutId, timeSlotId, userId);
        return (sprinklrSchedulingEntry.getCounter() % SCHEDULING_TICKET_CREATE_THRESHOLD == 0) ? "1" : "0";
    }
}