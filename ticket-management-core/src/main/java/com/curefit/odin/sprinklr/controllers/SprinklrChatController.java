package com.curefit.odin.sprinklr.controllers;

import com.curefit.odin.admin.controllers.BaseOdinController;
import com.curefit.odin.sprinklr.models.SprinklrChat;
import com.curefit.odin.sprinklr.pojo.SprinklrChatEntry;
import com.curefit.odin.sprinklr.pojo.webhook.SprinklrMessageWebhookRequest;
import com.curefit.odin.sprinklr.service.SprinklrChatService;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Configuration
@RestController
@Validated
@RequestMapping("/sprinklr-chat")
public class SprinklrChatController extends BaseOdinController<SprinklrChat, SprinklrChatEntry> {

    public SprinklrChatController(SprinklrChatService sprinklrChatService) {
        super(sprinklrChatService);
    }

    @PostMapping("/webhook")
    public ResponseEntity<?> processSprinklrMessageWebhook(@RequestBody SprinklrMessageWebhookRequest sprinklrMessageWebhookRequest) throws Exception {
        ((SprinklrChatService) baseMySQLService).processSprinklrWebhook(sprinklrMessageWebhookRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

}
