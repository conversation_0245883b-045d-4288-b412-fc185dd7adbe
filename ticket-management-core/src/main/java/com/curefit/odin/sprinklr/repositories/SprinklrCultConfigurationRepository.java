package com.curefit.odin.sprinklr.repositories;

import com.curefit.commons.sf.repository.BaseMySQLRepository;
import com.curefit.odin.sprinklr.models.SprinklrCultConfiguration;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface SprinklrCultConfigurationRepository extends BaseMySQLRepository<SprinklrCultConfiguration> {

    Optional<SprinklrCultConfiguration> findFirstByL1AndSubL1AndStatusAndL2(String l1, String subL1, String status, String l2);
}
