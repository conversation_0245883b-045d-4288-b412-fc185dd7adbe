package com.curefit.odin.sprinklr.repositories;

import com.curefit.commons.sf.repository.BaseMySQLRepository;
import com.curefit.odin.sprinklr.models.SprinklrScheduling;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface SprinklrSchedulingRepository extends BaseMySQLRepository<SprinklrScheduling> {

    Optional<SprinklrScheduling> findFirstByCenterIdAndWorkoutIdAndTimeSlotIdOrderByIdDesc(Long centerId, Long workoutId, Long timeSlotId);
}