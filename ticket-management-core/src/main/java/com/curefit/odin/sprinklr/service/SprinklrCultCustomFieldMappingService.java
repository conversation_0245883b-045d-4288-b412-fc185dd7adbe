package com.curefit.odin.sprinklr.service;


import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.odin.sprinklr.models.SprinklrCultCustomFieldMapping;
import com.curefit.odin.sprinklr.pojo.SprinklrCultCustomFieldMappingEntry;
import com.curefit.odin.sprinklr.repositories.SprinklrCultCustomFieldMappingRepository;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SprinklrCultCustomFieldMappingService extends BaseMySQLService<SprinklrCultCustomFieldMapping, SprinklrCultCustomFieldMappingEntry> {

    public SprinklrCultCustomFieldMappingService(SprinklrCultCustomFieldMappingRepository sprinklrCultCustomFieldMappingRepository) {
        super(sprinklrCultCustomFieldMappingRepository);
    }

    @Cacheable(value = "sprinklrVsCultCustomFieldMappingMap", unless = "#result == null")
    public Map<String, String> sprinklrVsCultMapping() {
        List<SprinklrCultCustomFieldMappingEntry> fieldMappings = findAll();
        return fieldMappings.stream()
                .collect(Collectors.toMap(SprinklrCultCustomFieldMappingEntry::getSprinklrCustomField, SprinklrCultCustomFieldMappingEntry::getCultCustomField));
    }

    @Cacheable(value = "cultVsSprinklrCustomFieldMappingMap", unless = "#result == null")
    public Map<String, String> cultVsSprinklrMapping() {
        List<SprinklrCultCustomFieldMappingEntry> fieldMappings = findAll();
        return fieldMappings.stream()
                .collect(Collectors.toMap(SprinklrCultCustomFieldMappingEntry::getCultCustomField, SprinklrCultCustomFieldMappingEntry::getSprinklrCustomField));
    }

    private List<SprinklrCultCustomFieldMappingEntry> findAll() {
        Iterable<SprinklrCultCustomFieldMapping> fieldMappings = baseMySQLRepository.findAll();
        return StreamSupport.stream(fieldMappings.spliterator(), false).map(this::convertToEntry).collect(Collectors.toList());
    }

    public Map<String, String> getSprinklrCultPropertiesNames(List<String> sprinklrProperties) {
        Map<String, String> sprinklrCultPropertiesNames = new HashMap<>();
        Map<String, String> sprinklrVsCultMapping = sprinklrVsCultMapping();
        for (String sprinklrProperty : sprinklrProperties) {
            if (sprinklrVsCultMapping.containsKey(sprinklrProperty)) {
                sprinklrCultPropertiesNames.put(sprinklrVsCultMapping.get(sprinklrProperty), sprinklrProperty);
            }
        }
        return sprinklrCultPropertiesNames;
    }
}


