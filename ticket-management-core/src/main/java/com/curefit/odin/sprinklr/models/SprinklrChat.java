package com.curefit.odin.sprinklr.models;

import com.curefit.odin.admin.models.BaseMySQLModel;
import com.curefit.odin.sprinklr.pojo.message.SprinklrMessage;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.vladmihalcea.hibernate.type.json.JsonStringType;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "sprinklr_chat_v2")
@TypeDefs({@TypeDef(name = "json", typeClass = JsonStringType.class)})
@Where(clause = "deleted_on is NULL")
@JsonIgnoreProperties(ignoreUnknown = true)
public class SprinklrChat extends BaseMySQLModel {
    String text;

    @Column(name = "brand_post")
    Boolean brandPost;

    @Column(name = "conversation_id")
    String conversationId;

    @Column(name = "parent_message_id")
    String parentMessageId;

    @Column(name = "message_id")
    String messageId;

    @Column(name = "associated_case_number")
    Long associatedCaseNumber;

    @Type(type = "json")
    @Column(name = "message", columnDefinition = "json")
    SprinklrMessage message;

    @Column(name = "deleted_on")
    Date deletedOn;
}
