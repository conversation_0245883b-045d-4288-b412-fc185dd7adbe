package com.curefit.odin.sprinklr.controllers;

import com.curefit.odin.admin.controllers.BaseOdinController;
import com.curefit.odin.sprinklr.models.SprinklrCultConfiguration;
import com.curefit.odin.sprinklr.pojo.SprinklrCultConfigurationEntry;
import com.curefit.odin.sprinklr.service.SprinklrCultConfigurationService;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Configuration
@RestController
@RequestMapping("/sprinklr-cult-configuration")
public class SprinklrCultConfigurationController extends BaseOdinController<SprinklrCultConfiguration, SprinklrCultConfigurationEntry> {

    public SprinklrCultConfigurationController(SprinklrCultConfigurationService sprinklrCultConfigurationService) {
        super(sprinklrCultConfigurationService);
    }

}
