package com.curefit.odin.sprinklr.controllers;

import com.curefit.common.data.exception.BaseException;
import com.curefit.odin.admin.controllers.BaseOdinController;
import com.curefit.odin.sprinklr.models.SprinklrCultCustomFieldMapping;
import com.curefit.odin.sprinklr.pojo.SprinklrCultCustomFieldMappingEntry;
import com.curefit.odin.sprinklr.service.SprinklrCultCustomFieldMappingService;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

@Configuration
@RestController
@Validated
@RequestMapping("/sprinklr-cult-custom-field-mapping")
public class SprinklrCultCustomFieldMappingController extends BaseOdinController<SprinklrCultCustomFieldMapping, SprinklrCultCustomFieldMappingEntry> {

    public SprinklrCultCustomFieldMappingController(SprinklrCultCustomFieldMappingService sprinklrCultCustomFieldMappingService) {
        super(sprinklrCultCustomFieldMappingService);
    }

    @RequestMapping(method = RequestMethod.POST, path = "/cult-properties")
    public ResponseEntity<Map<String, String>> getCultProperties(@RequestBody @Valid @NotEmpty List<String> sprinklrProperties) throws BaseException {

        SprinklrCultCustomFieldMappingService service = (SprinklrCultCustomFieldMappingService) baseMySQLService;
        Map<String, String> result = service.getSprinklrCultPropertiesNames(sprinklrProperties);

        return new ResponseEntity<>(result, HttpStatus.OK);
    }

}
