package com.curefit.odin.sprinklr.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.odin.sprinklr.models.SprinklrChat;
import com.curefit.odin.sprinklr.pojo.SprinklrChatEntry;
import com.curefit.odin.sprinklr.pojo.message.SprinklrMessage;
import com.curefit.odin.sprinklr.pojo.webhook.SprinklrMessageWebhookRequest;
import com.curefit.odin.sprinklr.repositories.SprinklrChatRepository;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SprinklrChatService extends BaseMySQLService<SprinklrChat, SprinklrChatEntry> {

    public SprinklrChatService(SprinklrChatRepository sprinklrChatRepository) {
        super(sprinklrChatRepository);
    }

    public void processSprinklrWebhook(SprinklrMessageWebhookRequest sprinklrMessageWebhookRequest) throws BaseException {
        if (sprinklrMessageWebhookRequest == null || sprinklrMessageWebhookRequest.getPayload() == null) {
            return;
        }
        SprinklrChatEntry sprinklrChatEntry = convertWebhookToSprinklrChatEntry(sprinklrMessageWebhookRequest.getPayload());
        if (sprinklrChatEntry == null) {
            return;
        }
        this.create(sprinklrChatEntry);
    }

    private SprinklrChatEntry convertWebhookToSprinklrChatEntry(SprinklrMessage payload) {
        return SprinklrChatEntry.builder()
                .text(payload.getContent().getText())
                .brandPost(payload.getBrandPost())
                .conversationId(payload.getConversationId())
                .parentMessageId(payload.getParentMessageId())
                .messageId(payload.getMessageId())
                .associatedCaseNumber(payload.getAssociatedCaseNumber())
                .message(payload)
                .build();
    }
}
