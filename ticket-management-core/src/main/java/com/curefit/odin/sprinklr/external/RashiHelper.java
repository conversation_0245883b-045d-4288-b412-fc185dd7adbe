package com.curefit.odin.sprinklr.external;

import com.curefit.base.enums.AppTenant;
import com.curefit.odin.enums.RashiEventType;
import com.curefit.odin.sprinklr.models.RashiSprinklrTicketEvent;
import com.curefit.odin.utils.AsyncService;
import com.curefit.rashi.client.RashiClient;
import com.curefit.rashi.enums.UserEventType;
import com.curefit.rashi.pojo.UserEventEntry;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.json.simple.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.Map;


@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
@Service
@RequiredArgsConstructor
public class RashiHelper {

    @Autowired
    RashiClient rashiClient;

    @Autowired
    ObjectMapper objectMapper;

    private void enqueueSprinklrTicketEvent(RashiEventType rashiEventType, @NotNull Long userId, RashiSprinklrTicketEvent rashiSprinklrTicketEvent, Date timestamp, String tenant) {
        try {
            log.info("Sending User activity event to Rashi for userId: {}, event : {}", userId, rashiSprinklrTicketEvent);
            JSONObject jsonBody = new JSONObject(objectMapper.convertValue(rashiSprinklrTicketEvent, Map.class));
            AppTenant appTenant = getAppTenant(tenant);
            UserEventEntry userEventEntry = new UserEventEntry(userId, UserEventType.USER_ACTIVITY_EVENT, rashiEventType.name(),
                    timestamp, jsonBody, appTenant);
            rashiClient.publishUserEvent(userEventEntry, appTenant, null);
            log.info("User activity event sent to Rashi for userEventEntry : {}", userEventEntry);
        } catch (Exception e) {
            log.error("Error while sending User activity event to Rashi for userId: {}, event : {}", userId, rashiSprinklrTicketEvent, e);
        }
    }

    public void enqueueSprinklrTicketEventAsync(RashiEventType rashiEventType, @NotNull Long userId, RashiSprinklrTicketEvent rashiSprinklrTicketEvent, Date timestamp, String tenant) {
        AsyncService.submit(() -> enqueueSprinklrTicketEvent(rashiEventType, userId, rashiSprinklrTicketEvent, timestamp, tenant));
    }

    public AppTenant getAppTenant(String tenant) {
        return AppTenant.CUREFIT;
    }
}
