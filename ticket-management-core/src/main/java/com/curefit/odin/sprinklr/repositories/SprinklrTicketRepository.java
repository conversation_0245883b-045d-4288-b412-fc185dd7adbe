package com.curefit.odin.sprinklr.repositories;

import com.curefit.commons.sf.repository.BaseMySQLRepository;
import com.curefit.odin.sprinklr.models.SprinklrTicket;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface SprinklrTicketRepository extends BaseMySQLRepository<SprinklrTicket> {

    Optional<SprinklrTicket> findFirstByCaseNumberOrderByIdDesc(String caseNumber);
}
