package com.curefit.odin.config;

import java.lang.reflect.Method;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 *
 */
@Component
public class RedisCacheKeyGenerator implements KeyGenerator {

  @Override
  public Object generate(Object o, Method method, Object... params) {
    return o.getClass().getName() + "__" + method.getName() + "__"
        + StringUtils.arrayToDelimitedString(params, ",");
  }
}
