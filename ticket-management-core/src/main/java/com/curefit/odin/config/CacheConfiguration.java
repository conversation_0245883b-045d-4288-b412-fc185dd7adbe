package com.curefit.odin.config;

import java.time.Duration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;

/**
 * <AUTHOR>
 *
 */
@EnableCaching
@Configuration
public class CacheConfiguration extends CachingConfigurerSupport {

  @Value("${spring.cache.redis.key-prefix:ODIN}")
  String redisKeyPrefix;

  @Value("${spring.cache.redis.use-key-prefix:true}")
  Boolean redisUseKeyPrefix;

  @Value("${spring.redis.ttl}")
  Long ttl;

  @Bean
  public RedisCacheConfiguration redisCacheConfiguration() {
    return RedisCacheConfiguration.defaultCacheConfig().entryTtl(Duration.ofMinutes(ttl))
        .disableCachingNullValues().prefixKeysWith(redisKeyPrefix);
  }

  @Bean
  public KeyGenerator keyGenerator() {
    return new RedisCacheKeyGenerator();
  }
}