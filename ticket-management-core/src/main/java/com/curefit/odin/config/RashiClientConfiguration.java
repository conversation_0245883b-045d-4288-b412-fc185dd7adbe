package com.curefit.odin.config;

import com.amazonaws.regions.Regions;
import com.curefit.rashi.client.RashiClient;
import com.curefit.rashi.config.RashiClientSNSConfiguration;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class RashiClientConfiguration {

    @Value("${rashi.sns.topicArn}")
    private String topicArn;

    @Bean
    public RashiClient rashiClient() {
        RashiClientSNSConfiguration config = new RashiClientSNSConfiguration(topicArn, Regions.AP_SOUTH_1.getName());
        return new RashiClient(config, new ObjectMapper());
    }

}
