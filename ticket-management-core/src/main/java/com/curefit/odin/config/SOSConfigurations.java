package com.curefit.odin.config;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */

@Configuration
@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@PropertySource(value = "classpath:${spring.profiles.active}/sos.properties")
public class SOSConfigurations {

  @Value("${sos.ticket.tenant.id}")
  Long tenantId;

  @Value("${sos.ticket.category.id}")
  Long categoryId;

  @Value("${sos.ticket.subCategory.id}")
  Long subCategoryId;

  @Value("${sos.ticket.title}")
  String title;

  @Value("${sos.agent.contact.number}")
  String agentContactNumber;

  @Value("${sos.cult.sms.contacts}")
  List<String> cultSMSContacts;

  @Value("${sos.care.sms.contacts}")
  List<String> careSMSContacts;

  @Value("${sos.eat.sms.contacts}")
  List<String> eatSMSContacts;

  @Value("${sos.default.sms.contacts}")
  List<String> defaultSMSContacts;

  @Value("${sos.creative.id}")
  String creativeId;

  @Value("${sos.sms.creative.id}")
  String smsCreativeId;

  @Value("${sos.campaign.id}")
  String camapignId;

  @Value(("${sos.call.events.queue}"))
  String callEventsQueue;

  @Value(("${sos.call.events.queue.waitTime}"))
  Long callEventsQueueWaitTimeInSec;

  @Value(("${sos.call.events.queue.batchSize}"))
  Integer callEventsQueueBatchSize;

  @Value(("${sos.notification.custom.field.id}"))
  Long notificationFieldId;

  @Value(("${sos.freshdesk.baseUrl}"))
  String freshdeskBaseUrl;

  @Value(("${sos.freshdesk.apiKey}"))
  String freshdeskAPIKey;

  @Value(("${sos.helpline.doc.url}"))
  String helplineDocUrl;


  public List<String> getSmsContacts(String tenantCode) {
    List<String> contacts = new ArrayList<>(defaultSMSContacts);
    switch (tenantCode) {
      case "cult":
        contacts.addAll(cultSMSContacts);
        break;
      case "care":
        contacts.addAll(careSMSContacts);
        break;
      case "eat":
        contacts.addAll(eatSMSContacts);
        break;
      default:
        break;
    }
    return contacts;
  }
}
