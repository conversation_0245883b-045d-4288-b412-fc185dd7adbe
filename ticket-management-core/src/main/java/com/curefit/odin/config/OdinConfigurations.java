package com.curefit.odin.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import lombok.Getter;

/**
 * <AUTHOR>
 *
 */
@Configuration
@Getter
public class OdinConfigurations {

  @Value(("${freshdesk.baseUrl}"))
  String freshdeskBaseUrl;

  @Value(("${freshdesk.apiKey}"))
  String freshdeskAPIKey;

  @Value("${freshdesk.source.email}")
  String freshdeskSourceEmail;

  @Value("${google.location.url}")
  String googleLocationUrl;

  @Value("${google.location.api.key}")
  String googleLocationAPIKey;

  @Value("${escalation.events.queue.name}")
  String escalationEventsQueue;

  @Value("${escalation.events.queue.batchSize}")
  int escalationEventsQueueBatchSize;

  @Value("${escalation.events.queue.waitTime}")
  long escalationEventsQueueWaitTimeInSec;

  @Value(("${mozart.job.config.id}"))
  String mozartJobConfigId;

  @Value(("${mozart.sla.breach.next.day.job.config.id}"))
  String mozartSLAAboutToBreachTomorrowJobConfigId;

  @Value("${google.oauth.token.directory}")
  String oauthTokenDirectory;

  @Value("${customer.support.email}")
  String customerSupportEmail;

  @Value(("${hr.freshdesk.baseUrl}"))
  String hrFreshdeskBaseUrl;

  @Value(("${hr.freshdesk.apiKey}"))
  String hrFreshdeskAPIKey;

  @Value(("${partner.freshdesk.baseUrl}"))
  String partnerFreshdeskBaseUrl;

  @Value(("${partner.freshdesk.apiKey}"))
  String partnerFreshdeskAPIKey;

  @Value("${watchmen.apiKey}")
  String watchmenApiKey;

  @Value(("${watchmen.membership.queue.name}"))
  String watchmenMembershipQueue;

  @Value(("${watchmen.membership.queue.waitTime}"))
  Long watchmenMembershipQueueWaitTimeInSec;

  @Value(("${watchmen.membership.queue.batchSize}"))
  Integer watchmenMembershipQueueBatchSize;

  @Value(("${sugarfit.freshdesk.baseUrl}"))
  String sugarfitFreshdeskBaseUrl;

  @Value(("${sugarfit.freshdesk.apiKey}"))
  String sugarfitFreshdeskAPIKey;

  @Value("${ticketing-system.employee.update.queue}")
  String updateQueueName;

  @Value("${neo.employee.update.events.queue.batchSize}")
  int neoEmployeeUpdateEventsQueueBatchSize;

  @Value("${neo.employee.update.events.queue.waitTime}")
  long neoEmployeeUpdateEventsQueueWaitTimeInSec;
}
