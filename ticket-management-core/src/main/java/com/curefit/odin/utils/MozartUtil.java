package com.curefit.odin.utils;

import com.curefit.common.data.exception.BaseException;
import com.curefit.mozart.MozartClient;
import com.curefit.mozart.pojo.JobEntry;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

@Slf4j
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@Service
public class MozartUtil {
    static final ObjectMapper objectMapper = new ObjectMapper();
    final MozartClient mozartClient;

    public <T> void submitJobAsync(String namespace, String refId, Date atSchedule, String jobConfigId, T payload) throws BaseException {
        JobEntry jobRequest = new JobEntry();
        jobRequest.setSource(namespace);
        jobRequest.setRefId(refId);
        jobRequest.setJobConfigId(jobConfigId);
        jobRequest.setAtSchedule(atSchedule);
        jobRequest.setPayload(objectMapper.convertValue(payload, new TypeReference<ObjectNode>() {
        }));
        mozartClient.submitJobAsync(jobRequest);
    }
}
