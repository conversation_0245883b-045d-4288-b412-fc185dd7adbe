package com.curefit.odin.utils;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

public class URLUtils {

    public static String addParamsToUrl(String url, Map<String, String> params) {
        if (params == null || params.isEmpty()) {
            return url;
        }

        StringBuilder urlWithParams = new StringBuilder(url);

        // Check if the URL already has query parameters
        boolean hasQuery = url.contains("?");
        urlWithParams.append(hasQuery ? "&" : "?");

        params.forEach((key, value) -> {
            try {
                String encodedKey = URLEncoder.encode(key, StandardCharsets.UTF_8);
                String encodedValue = URLEncoder.encode(value, StandardCharsets.UTF_8);
                urlWithParams.append(encodedKey).append("=").append(encodedValue).append("&");
            } catch (Exception e) {
                throw new RuntimeException("Error encoding URL parameters", e);
            }
        });

        // Remove the trailing '&' character
        urlWithParams.setLength(urlWithParams.length() - 1);

        return urlWithParams.toString();
    }
}
