package com.curefit.odin.utils;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.GetObjectRequest;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;

/**
 * <AUTHOR>
 */

@Slf4j
@Component
@FieldDefaults(level = AccessLevel.PRIVATE)
public class S3Utils {

    @Autowired
    AmazonS3 amazonS3;

    @Value("${attachment.s3.bucket}")
    String bucketName;

    public boolean downloadFile(String s3Key, File localFile) {
        try {
            if (localFile.exists() || localFile.isDirectory()) {
                throw new IllegalArgumentException(
                        String.format("Requested file Name Already Exists: %s", localFile));
            }
            amazonS3.getObject(new GetObjectRequest(bucketName, s3Key), localFile);
            boolean success = localFile.exists() && localFile.canRead();
            if (!success) {
                log.error("Error downloading S3 file {}", s3Key);
            }
            return success;
        } catch (Exception e) {
            log.error("exception in downloading file from s3", e);
        }
        return false;
    }

    public void uploadFile(String s3Key, File localFile) {
        try {
            amazonS3.putObject(bucketName, s3Key, localFile);
        } catch (Exception e) {
            log.error("exception in uploading file to s3", e);
        }
    }
}
