package com.curefit.odin.utils.models;

import javax.persistence.*;

import com.curefit.odin.admin.models.BaseMySQLModel;
import com.curefit.odin.enums.LocationType;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "location",
    uniqueConstraints = {@UniqueConstraint(columnNames = {"reference_id", "location_source_id"})})
public class Location extends BaseMySQLModel {

  /**
  * 
  */
  private static final long serialVersionUID = 5861923203842652201L;

  @ManyToOne
  @JoinColumn(name = "location_source_id", referencedColumnName = "id")
  LocationSource locationSource;

  @Column(name = "reference_id")
  String referenceId;

  @Column(name = "center_id")
  String centerId;

  @Column(name = "center_name")
  String centerName;

  @Column(name = "cluster_id")
  String clusterId;

  @Column(name = "cluster_name")
  String clusterName;

  @Column(name = "city_id")
  String cityId;

  @Column(name = "city_name")
  String cityName;

  @Column(name = "country_id")
  String countryId;

  @Column(name = "country_name")
  String countryName;
  
  Double latitude;

  Double longitude;

  String address;

  @Column(name = "neo_code")
  String neoCode;

  @Enumerated(value = EnumType.STRING)
  LocationType type;

  @Column(name = "center_service_ref_id")
  String centerServiceRefId;

}
