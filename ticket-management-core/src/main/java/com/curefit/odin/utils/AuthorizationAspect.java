package com.curefit.odin.utils;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;

import java.util.Arrays;

/**
 * <AUTHOR>
 */

@Component
@Aspect
@EnableAspectJAutoProxy
public class AuthorizationAspect {

  @Autowired
  AuthService authService;

  @Before("@annotation(com.curefit.odin.utils.Secured)")
  public void before(JoinPoint joinPoint) {
    MethodSignature signature = (MethodSignature) joinPoint.getSignature();
    Secured secured = signature.getMethod().getAnnotation(Secured.class);

    if (!authService.getRoles().containsAll(Arrays.asList(secured.allowedRoles()))) {
      throw new HttpClientErrorException(HttpStatus.UNAUTHORIZED, "Only " + Arrays.toString(secured.allowedRoles()) + " roles are allowed for " + signature.getMethod().getName());
    }
  }
}
