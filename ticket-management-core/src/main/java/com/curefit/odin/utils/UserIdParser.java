package com.curefit.odin.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */

public class UserIdParser {

  // Using the [user:<emailId>] pattern for mentioned users
  private static final String USER_ID_PATTERN_REGEX = "\\[user:[a-zA-Z0-9_!#$%&’*+/=?`{|}~^.-]+@[a-zA-Z0-9.-]+]";


  /**
   * This method search for pattern [user:<email>] in given text and returns the list of emails
   * @param text Description
   * @return List
   */

  public static List<String> getUserIds(String text) {
    List<String> userIds = new ArrayList<>();

    if (text == null) {
      return userIds;
    }

    Pattern pattern = Pattern.compile(USER_ID_PATTERN_REGEX);
    Matcher matcher = pattern.matcher(text);

    while (matcher.find()) {
      String atUserString = text.substring(matcher.start(), matcher.end() - 1);
      String[] keyValue = atUserString.split(":");
      if (keyValue.length == 2) {
        userIds.add(keyValue[1]);
      }
    }
    return userIds;
  }
}