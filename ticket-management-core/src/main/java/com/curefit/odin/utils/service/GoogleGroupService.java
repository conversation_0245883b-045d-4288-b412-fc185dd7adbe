package com.curefit.odin.utils.service;

import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.odin.utils.pojo.DomainEntry;
import com.curefit.odin.utils.pojo.GoogleGroup;
import com.google.api.services.admin.directory.Directory;
import com.google.api.services.admin.directory.DirectoryScopes;
import com.google.api.services.admin.directory.model.Group;
import com.google.api.services.admin.directory.model.Groups;
import com.google.api.services.admin.directory.model.Member;
import com.google.api.services.admin.directory.model.Members;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class GoogleGroupService extends GoogleService {

  private static final int MAX_RESULT = 200;

  @Autowired
  RollbarService rollbarService;

  public List<GoogleGroup> fetchGroups(DomainEntry domain) throws IOException, GeneralSecurityException {
    Directory service = getDirectoryService(domain.getName());

    rollbarService.log("Started fetching dls for domain " + domain.getName());
    String nextPageToken = "";
    List<Group> groups = new ArrayList<>();
    do {
      Groups groupsResult = service.groups().list()
              .setMaxResults(MAX_RESULT)
              .setPageToken(nextPageToken)
              .setDomain(domain.getUrl())
              .execute();
      groups.addAll(groupsResult.getGroups());
      nextPageToken = groupsResult.getNextPageToken();
    } while (nextPageToken != null);

    return groups.stream()
            .map(group -> GoogleGroup.builder().dl(group.getEmail()).groupKey(group.getId()).build())
            .collect(Collectors.toList());
  }

  public void updateGroupMembers(DomainEntry domain, GoogleGroup group) throws IOException, GeneralSecurityException {
    Directory service = getDirectoryService(domain.getName());
    String membersNextPageToken = "";
    List<Member> members = new ArrayList<>();
    do {
      Members membersResult = null;
      try {
        membersResult = service.members()
                .list(group.getGroupKey())
                .setMaxResults(MAX_RESULT)
                .setPageToken(membersNextPageToken)
                .execute();
      } catch (IOException e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
      }

      if (membersResult != null && membersResult.getMembers() != null) {
        members.addAll(membersResult.getMembers());
        membersNextPageToken = membersResult.getNextPageToken();
      }
    } while (StringUtils.isNotEmpty(membersNextPageToken));

    group.setMembers(members.stream()
            .map(Member::getEmail)
            .collect(Collectors.toList()));
  }

  @Override
  public List<String> getScopes() {
    return Arrays.asList(DirectoryScopes.ADMIN_DIRECTORY_GROUP_READONLY,
            DirectoryScopes.ADMIN_DIRECTORY_GROUP, DirectoryScopes.ADMIN_DIRECTORY_GROUP_MEMBER, DirectoryScopes.ADMIN_DIRECTORY_GROUP_MEMBER_READONLY);
  }
}
