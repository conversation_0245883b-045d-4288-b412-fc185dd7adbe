package com.curefit.odin.utils.listener;

import com.amazonaws.regions.Regions;
import com.amazonaws.services.sqs.model.Message;
import com.curefit.common.data.enums.AppStatus;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.enums.LogType;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.messaging.consumer.BaseSqsConsumer;
import com.curefit.commons.sf.auth.IdentityHelper;
import com.curefit.odin.admin.pojo.UserEntry;
import com.curefit.odin.admin.service.UserService;
import com.curefit.odin.config.OdinConfigurations;
import com.curefit.odin.utils.pojo.WatchmenMembershipEvent;
import com.curefit.odin.utils.service.MaverickService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;

import java.util.List;
import java.util.stream.Collectors;

import static com.curefit.odin.commons.Constants.TENANT;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
@Profile("!local")
public class WatchmenMembershipListener extends BaseSqsConsumer {

    @Qualifier(value = "odinObjectMapper")
    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    RollbarService rollbarService;

    @Autowired
    UserService userService;

    @Autowired
    MaverickService maverickService;

    @Autowired
    IdentityHelper identityHelper;

    public WatchmenMembershipListener(OdinConfigurations odinConfigurations) {
        super(odinConfigurations.getWatchmenMembershipQueue(), Regions.AP_SOUTH_1,
                odinConfigurations.getWatchmenMembershipQueueWaitTimeInSec(), odinConfigurations.getWatchmenMembershipQueueBatchSize());
    }

    @Override
    public List<Boolean> process(List<Message> messages) {
        return messages.stream().map(message -> {
            log.info("message for watchmen membership: {} ", message.getBody());
            try {
                WatchmenMembershipEvent watchmenMembershipEvent = objectMapper.readValue(message.getBody(), WatchmenMembershipEvent.class);
                if ("EMAIL".equals(watchmenMembershipEvent.getUserIdType())) {
                    String name = watchmenMembershipEvent.getUserId();
                    try {
                        name = identityHelper.fetchIdentity(TENANT, watchmenMembershipEvent.getUserId(), null).getName();
                    } catch (Exception e) {
                        log.error("user not present in identity {}", watchmenMembershipEvent.getUserId(), e);
                    }

                    UserEntry userEntry = new UserEntry(name, watchmenMembershipEvent.getUserId());
                    userService.createExternalUserIfNotExist(userEntry);

                    // refresh users in maverik service
                    maverickService.refreshUsers();
                }
                return true;
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                rollbarService.error(e);
            }
            return false;
        }).collect(Collectors.toList());
    }
}