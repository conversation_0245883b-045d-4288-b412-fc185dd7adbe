package com.curefit.odin.utils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;

/**
 * <AUTHOR>
 */

//TODO : move this to watchmen
@Component
public class ApiKeyValidator {

    @Value("${apiKey}")
    String authApiKey;

    public void validateApiKey(String apiKey) {
        if (!authApiKey.equals(apiKey)) {
            throw new HttpClientErrorException(HttpStatus.UNAUTHORIZED, "Invalid API key");
        }
    }
}
