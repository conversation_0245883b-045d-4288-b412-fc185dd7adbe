package com.curefit.odin.utils.service;

import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.iris.models.SendCampaignNotificationsResponseStatus;
import com.curefit.iris.models.UserContext;
import com.curefit.odin.config.SOSConfigurations;
import com.curefit.odin.notification.NotificationService;
import com.curefit.odin.user.pojo.TicketEntry;
import com.curefit.odin.utils.pojo.SOSRequestEntry;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import static com.curefit.odin.commons.Constants.*;

/**
 * <AUTHOR>
 */

@Slf4j
@Component
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SOSNotificationHelper {

  @Autowired
  NotificationService notificationService;

  @Autowired
  SOSConfigurations sosConfigurations;

  @Autowired
  RollbarService rollbarService;

  public SendCampaignNotificationsResponseStatus sendClickToCallNotification(SOSRequestEntry.EmployeeDetails employeeDetails, Long ticketId) {
    List<UserContext> userContexts = new ArrayList<>();
    UserContext userContext = new UserContext();
    userContext.setPhone(sosConfigurations.getAgentContactNumber());
    userContext.setTags(new HashMap<String, Object>() {{
      put(AGENT_NUMBER, employeeDetails.getPhone());
      put(TICKET_ID, ticketId.toString());
    }});
    userContexts.add(userContext);
    log.info("SOS: UserContext is: {}, for SOS ticketId: {}", userContexts.get(0).toString(), ticketId);

    return notificationService.sendNotification(sosConfigurations.getCamapignId(), sosConfigurations.getCreativeId(), userContexts, null);
  }

  public void sendSMSNotification(String tenantCode, SOSRequestEntry.EmployeeDetails employeeDetails, TicketEntry ticketEntry, boolean callSent) {
    try {
      List<String> contacts = sosConfigurations.getSmsContacts(tenantCode);
      if (CollectionUtils.isEmpty(contacts)) {
        return;
      }
      List<UserContext> userContexts = sosConfigurations.getSmsContacts(tenantCode).stream().map(phone -> {
        UserContext userContext = new UserContext();
        userContext.setPhone(phone);
        return userContext;
      }).collect(Collectors.toList());

      SendCampaignNotificationsResponseStatus status = notificationService.sendNotification(sosConfigurations.getCamapignId(), sosConfigurations.getSmsCreativeId(), userContexts, new HashMap<String, Object>() {{
        put(EMPLOYEE_DETAILS, employeeDetails);
        put(TICKET_ID, ticketEntry.getId());
        put(FRESHDESK_TICKET_ID, ticketEntry.getDestRefId());
        put(CALL_SENT, callSent);
      }});

      log.info("SOS SMS notification response {} " , status);

    } catch (Exception e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
  }
}
