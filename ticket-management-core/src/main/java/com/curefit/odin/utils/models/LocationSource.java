package com.curefit.odin.utils.models;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import com.curefit.odin.admin.models.BaseMySQLModel;
import com.curefit.odin.admin.models.Tenant;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

@Setter
@Getter
@Entity
@ToString
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "location_source")
public class LocationSource extends BaseMySQLModel {

  /**
  * 
  */
  private static final long serialVersionUID = 6389659763768636765L;
  
  @ManyToOne
  @JoinColumn(name = "tenant_id", referencedColumnName = "id")
  Tenant tenant;
  
  String type;

  String name;

  String url;

  String headers;

  @Column(name = "root_path")
  String rootPath;

  @Column(name = "key_mapping", columnDefinition = "TEXT")
  String keyMapping;

  @Column(name = "sync_duration_in_hours")
  int syncDurationInHours;

}
