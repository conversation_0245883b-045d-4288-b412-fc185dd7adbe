package com.curefit.odin.utils.controller;

import com.curefit.common.data.exception.BaseException;
import com.curefit.odin.user.pojo.TicketEntry;
import com.curefit.odin.utils.pojo.SOSRequestEntry;
import com.curefit.odin.utils.service.SOSService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

/**
 * <AUTHOR>
 */

@RestController
@RequestMapping("/sos")
public class SOSController {

  @Autowired
  SOSService sosService;

  @RequestMapping(method = RequestMethod.POST)
  public ResponseEntity<TicketEntry> createTicket(@RequestBody SOSRequestEntry request, @RequestHeader("X_USER_ID") String userEmail) throws BaseException {
    return new ResponseEntity<>(sosService.createTicket(request, userEmail), HttpStatus.OK);
  }
}
