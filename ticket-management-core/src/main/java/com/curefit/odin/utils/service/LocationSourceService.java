package com.curefit.odin.utils.service;

import java.io.IOException;
import java.util.*;

import com.curefit.odin.commons.Constants;
import com.curefit.odin.enums.LocationType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.rest.client.CommonHttpHelper;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.odin.admin.repositories.LocationSourceDAO;
import com.curefit.odin.admin.service.TenantService;
import com.curefit.odin.utils.models.LocationSource;
import com.curefit.odin.utils.pojo.LocationSourceEntry;
import com.curefit.odin.utils.pojo.LocationEntry;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONArray;

@Service
@Slf4j
public class LocationSourceService extends BaseMySQLService<LocationSource, LocationSourceEntry> {

  static ObjectMapper objectMapper = new ObjectMapper();

  @Autowired
  private CommonHttpHelper httpHelper;

  @Autowired
  TenantService tenantService;

  @Autowired
  RollbarService rollbarService;

  @Override
  public LocationSource convertToEntity(LocationSourceEntry entry) {
    LocationSource convertedEntity = super.convertToEntity(entry);
    try {
      convertedEntity.setKeyMapping(objectMapper.writeValueAsString(entry.getFieldMapping()));
      convertedEntity.setHeaders(objectMapper.writeValueAsString(entry.getHeadersObject()));
      if (entry.getTenantId() != null) {
        convertedEntity.setTenant(tenantService.fetchEntityById(entry.getTenantId()));
      }
    } catch (JsonProcessingException | BaseException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    return convertedEntity;
  }

  public LocationSourceEntry convertToEntry(LocationSource entity) {
    log.info("entity:"+entity);
    LocationSourceEntry convertedEntry = super.convertToEntry(entity);
    try {
      convertedEntry.setHeadersObject(objectMapper.readValue(entity.getHeaders(),
          new TypeReference<HashMap<String, String>>() {}));
      convertedEntry.setFieldMapping(objectMapper.readValue(entity.getKeyMapping(),
          new TypeReference<HashMap<String, String>>() {}));
      if (entity.getTenant() != null) {
        convertedEntry.setTenantId(entity.getTenant().getId());
      }
    } catch (IOException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    return convertedEntry;
  }


  public LocationSourceService(LocationSourceDAO dataSourceDao) {
    super(dataSourceDao);
  }


  public List<LocationEntry> parseResponse(Long id, String json, String centerFieldMap,
      Map<String, String> centersFieldsMap) {
    List<LocationEntry> listOfTargets = new ArrayList<>();
    Object document = Configuration.defaultConfiguration()
        .addOptions(Option.DEFAULT_PATH_LEAF_TO_NULL).jsonProvider().parse(json);
    JSONArray centers = JsonPath.read(document, centerFieldMap);
    for (Object elementDoc : centers) {
      String centerRefId = JsonPath.read(elementDoc, "id") != null ? JsonPath.read(elementDoc, "id").toString() : "";
      ObjectNode centerObj = objectMapper.createObjectNode();
      for (Map.Entry<String, String> centerFieldEntry : centersFieldsMap.entrySet()) {
        String key = centerFieldEntry.getKey();
        String[] values = centerFieldEntry.getValue().split("\\+");
        String value = "";
        try {
          StringJoiner parsedValueJoiner = new StringJoiner(" ");
          for (int idx = 0; idx < values.length; idx++) {
            value = values[idx];
            parsedValueJoiner.add(JsonPath.read(elementDoc, value).toString());
          }
          centerObj.put(key, parsedValueJoiner.toString());
        } catch (Exception e) {
          String message = String.format("Error while parsing location data - sourceId: %d, centerRefId: %s, sourceKey:%s, destinationKey: %s. Error message: %s", id, centerRefId, value, key, e.getMessage());
          log.error(message, e);
          centerObj.put(key, objectMapper.createObjectNode().toString());
        }
      }
      try {
        LocationEntry target = objectMapper.convertValue(centerObj, LocationEntry.class);
        target.setDataSourceId(id);
        if (StringUtils.isBlank(target.getCountryId())) {
          target.setCountryId(Constants.DEFAULT_COUNTRY_ID);
        }
        if (StringUtils.isBlank(target.getCountryName())) {
          target.setCountryName(Constants.DEFAULT_COUNTRY_NAME);
        }
        target.setType(LocationType.CENTRE);
        listOfTargets.add(target);
      } catch (Exception e) {
        String message = String.format("Error while mapping to LocationEntry - sourceId: %d, centerRefId: %s. Error message: %s", id, centerRefId, e.getMessage());
        log.error(message, e);
      }
    }
    return listOfTargets;
  }

  public List<LocationEntry> getValuesFromUrl(String url, Map<String, String> headerObject,
      Map<String, String> mapping, String rootPath, Long id) {
    log.info("updating datasource values in data source entry ");
    log.info("Fetching values from the url {}", url);
    ResponseEntity<String> response = httpHelper.request(url, HttpMethod.GET, null, headerObject);
    return parseResponse(id, response.getBody(), rootPath, mapping);
  }
}
