package com.curefit.odin.utils;

import com.curefit.common.rest.client.CommonHttpHelper;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.odin.config.OdinConfigurations;
import com.curefit.odin.utils.pojo.GooglePlaceDetail;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * <AUTHOR>
 */

@FieldDefaults(level = AccessLevel.PRIVATE)
@Service
@Slf4j
public class GoogleLocationService {

  @Autowired
  CommonHttpHelper httpHelper;

  @Autowired
  OdinConfigurations odinConfigurations;

  @Autowired
  RollbarService rollbarService;

  public String fetchAddress(double lat, double lng) {
    try {
      String url = UriComponentsBuilder.fromHttpUrl(odinConfigurations.getGoogleLocationUrl())
              .queryParam("latlng", lat + "," + lng)
              .queryParam("key", odinConfigurations.getGoogleLocationAPIKey())
              .toUriString();

      GooglePlaceDetail googlePlaceDetail = httpHelper.request(url, HttpMethod.GET, null, null, GooglePlaceDetail.class).getBody();

      if (googlePlaceDetail != null && googlePlaceDetail.getStatus().equals("OK")) {
        if (googlePlaceDetail.getResults().size() > 0) {
          return googlePlaceDetail.getResults().get(0).getFormattedAddress();
        }
      }
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    return "";
  }
}
