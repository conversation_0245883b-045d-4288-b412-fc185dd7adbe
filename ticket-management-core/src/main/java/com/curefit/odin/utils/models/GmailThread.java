package com.curefit.odin.utils.models;

import com.curefit.odin.admin.models.BaseMySQLModel;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 */

@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "gmail_thread")
public class GmailThread extends BaseMySQLModel {

  @Column(name = "thread_id")
  String threadId;

  @Column(name = "ticket_id")
  Long ticketId;
}
