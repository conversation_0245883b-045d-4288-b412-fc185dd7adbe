package com.curefit.odin.utils.controller;

import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.controllers.BaseOdinController;
import com.curefit.odin.scheduledTasks.LocationUpdater;
import com.curefit.odin.utils.models.LocationSource;
import com.curefit.odin.utils.pojo.LocationSourceEntry;
import com.curefit.odin.utils.service.LocationSourceService;

@RestController
@RequestMapping("/location_source")
public class LocationSourceController
    extends BaseOdinController<LocationSource, LocationSourceEntry> {


  @Autowired
  LocationUpdater locationUpdater;

  public LocationSourceController(LocationSourceService locationSourceService) {
    super(locationSourceService);
  }

  @RequestMapping(method = RequestMethod.POST, value = "/refresh")
  public void updateLocationsData() throws BaseException, InvalidSeachQueryException {
    locationUpdater.refreshLocations();
  }

}
