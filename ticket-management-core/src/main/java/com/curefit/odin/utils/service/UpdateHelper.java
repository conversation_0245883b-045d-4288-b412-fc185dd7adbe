package com.curefit.odin.utils.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.persistence.MappedSuperclass;
import org.apache.commons.collections.CollectionUtils;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.odin.admin.models.BaseMySQLModel;
import com.curefit.odin.admin.pojo.DataSourceValueEntry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@MappedSuperclass
@RequiredArgsConstructor
@Slf4j
public abstract class UpdateHelper<T extends BaseMySQLModel, E extends DataSourceValueEntry> {

  final protected BaseMySQLService<T, E> baseMySqlService;

  public void updateValues(List<E> valuesInDB, List<E> currentValues) throws BaseException {

    log.info("Updating values {}", currentValues);
    List<String> referenceIds = new ArrayList<>();

    if (CollectionUtils.isNotEmpty(currentValues)) {
      currentValues.forEach(value -> {
        Optional<E> valueInDb = valuesInDB.stream()
            .filter(
                dataSourceValue -> dataSourceValue.getReferenceId().equals(value.getReferenceId()))
            .findAny();
        valueInDb.ifPresent(dbValue -> value.setId(dbValue.getId()));
      });
      referenceIds.addAll(
          currentValues.stream().map(DataSourceValueEntry::getReferenceId).collect(Collectors.toList()));
      List<E> updated = baseMySqlService.bulkPatchUpdate(currentValues.stream()
          .filter(value -> value.getId() != null).collect(Collectors.toList()));
      log.info("Updated values : {}", updated);
      List<E> newEntries = baseMySqlService.bulkCreate(currentValues.stream()
          .filter(value -> value.getId() == null).collect(Collectors.toList()));
      log.info("Created values : {}", newEntries);
    }
    List<E> valuesToBeDeactivated =
        valuesInDB.stream().filter(dbValue -> !referenceIds.contains(dbValue.getReferenceId()))
            .collect(Collectors.toList());
    
    valuesToBeDeactivated.forEach(valueInDb -> valueInDb.setActive(false));
    log.info("Deactivated values : {}", valuesToBeDeactivated);
    baseMySqlService.bulkPatchUpdate(valuesToBeDeactivated);
  }
}
