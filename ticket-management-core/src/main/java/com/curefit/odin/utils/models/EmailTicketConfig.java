package com.curefit.odin.utils.models;

import com.curefit.odin.admin.models.BaseMySQLModel;
import com.curefit.odin.enums.Priority;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import javax.persistence.*;

/**
 * <AUTHOR>
 */

@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "email_ticket_config")
public class EmailTicketConfig extends BaseMySQLModel {

  String email;

  @Column(name = "alias_email")
  String aliasEmail;

  @Column(name = "tenant_id")
  Long tenantId;

  @Column(name = "category_id")
  Long categoryId;

  @Column(name = "sub_category_id")
  Long subCategoryId;

  @Column(name = "location_id")
  Long locationId;

  @Enumerated(value = EnumType.STRING)
  Priority priority;

}
