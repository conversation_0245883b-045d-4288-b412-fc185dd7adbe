package com.curefit.odin.utils.service;

import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.odin.admin.repositories.ReleaseHistoryDAO;
import com.curefit.odin.utils.models.ReleaseHistory;
import com.curefit.odin.utils.pojo.ReleaseHistoryEntry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */

@Slf4j
@Service
public class ReleaseHistoryService extends BaseMySQLService<ReleaseHistory, ReleaseHistoryEntry> {

  public ReleaseHistoryService(ReleaseHistoryDAO releaseHistoryDAO) {
    super(releaseHistoryDAO);
  }

}
