package com.curefit.odin.utils.models;

import com.curefit.odin.admin.models.BaseMySQLModel;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.hibernate.annotations.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Set;

/**
 * <AUTHOR>
 */

@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "domain")
public class Domain extends BaseMySQLModel {

  String name;

  String url;

  @Type(type = "json")
  @Column(columnDefinition = "json", name = "whitelisted_tenants")
  Set<Long> whitelistedTenants;
}
