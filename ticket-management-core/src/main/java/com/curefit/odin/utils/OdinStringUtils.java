package com.curefit.odin.utils;

import org.springframework.util.StringUtils;

import java.util.Arrays;

public class OdinStringUtils {

    public static String replaceIncompatibleCharactersWithSpace(String input) {
        if (input == null) {
            return null;
        }
        return input.codePoints()
                .map(cp -> (cp >= 0x0000 && cp <= 0xFFFF) ? cp : 0x20)
                .collect(StringBuilder::new,
                        StringBuilder::appendCodePoint,
                        StringBuilder::append)
                .toString();
    }

    public static boolean isAnyEmpty(String... values) {
        return Arrays.stream(values).anyMatch(StringUtils::isEmpty);
    }

    public static boolean isNumeric(String value) {
        return org.apache.commons.lang3.StringUtils.isNumeric(value);
    }
}
