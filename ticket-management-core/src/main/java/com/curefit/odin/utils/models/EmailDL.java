package com.curefit.odin.utils.models;

/**
 * <AUTHOR>
 */

import com.curefit.odin.admin.models.BaseMySQLModel;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import javax.persistence.Entity;
import javax.persistence.Table;

@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "email_dl")
public class EmailDL extends BaseMySQLModel {

  String dl;

  String email;
}
