package com.curefit.odin.utils.controller;

import com.curefit.odin.admin.controllers.BaseOdinController;
import com.curefit.odin.utils.models.ReleaseHistory;
import com.curefit.odin.utils.pojo.ReleaseHistoryEntry;
import com.curefit.odin.utils.service.ReleaseHistoryService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */

@RestController
@RequestMapping("/release_history")
public class ReleaseHistoryController extends BaseOdinController<ReleaseHistory, ReleaseHistoryEntry> {

  public ReleaseHistoryController(ReleaseHistoryService releaseHistoryService) {
    super(releaseHistoryService);
  }
}
