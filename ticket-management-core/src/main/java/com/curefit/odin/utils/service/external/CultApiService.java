package com.curefit.odin.utils.service.external;

import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.client.exception.HttpException;
import com.curefit.commons.client.pojo.HttpRequestDetail;
import com.curefit.commons.client.restTemplate.RestTemplateClient;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.utils.cb.CircuitBreakerProperties;
import com.curefit.odin.utils.pojo.HomeCenterId;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class CultApiService {
    private static final String GET_HOME_CENTER_ID_API_URL = "/trainers/v1/identity/%s/homeCenter";

    @Value("${external.cult-api.baseUrl}")
    private String baseUrl;

    @Autowired
    protected RestTemplateClient restTemplateClient;

    @Autowired
    private RollbarService rollbarService;

    private static final CircuitBreakerProperties circuitBreakerProperties = CircuitBreakerProperties.builder()
            .errorThresholdPercentage(40)
            .executionTimeoutInMilliseconds(5000)
            .requestVolumeThreshold(15)
            .sleepWindowInMilliseconds(5000)
            .circuitBreakerKey("fitness-accounting-service")
            .enabled(true).build();

    @Retryable(value = {HttpClientErrorException.NotFound.class, HttpException.class}, maxAttemptsExpression = "${retry.maxAttempts}",
            backoff = @Backoff(delayExpression = "${retry.delay}"))
    public HomeCenterId getHomeCenterId(Long identityId, String osName, String appVersion) throws BaseException {
        final String uri = String.format(GET_HOME_CENTER_ID_API_URL, identityId);
        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromUriString(baseUrl).path(uri);
        String url = uriComponentsBuilder.build().toUriString();

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Accept", "application/json");
        headers.put("osname", osName);
        headers.put("appversion", appVersion);

        log.info("Triggering fetching homeCenterId API in cult-api for identityId {}", identityId);

        Long start = System.nanoTime();
        HttpRequestDetail<String, HomeCenterId> httpRequestDetail =
                new HttpRequestDetail<>(url, null, null, headers,
                        circuitBreakerProperties, new TypeReference<HomeCenterId>() {
                });
        ResponseEntity<HomeCenterId> response = restTemplateClient.get(httpRequestDetail);

        Long end = System.nanoTime();

        org.springframework.http.HttpStatus responseStatus = response.getStatusCode();
        HomeCenterId orderPaymentDataResponse = response.getBody();
        log.info("Cult-api get homeCenterId response received for identityId: {} in {} ms. Status: {}, Body : {}", identityId,
                (end - start) / 1e6, responseStatus, orderPaymentDataResponse);

        if (responseStatus.is2xxSuccessful()) {
            return orderPaymentDataResponse;
        }

        String errorMsg = String.format("Got invalid homeCenterId " +
                "response from cult-api. Got status code %s Reason Phrase %s and Response body %s", responseStatus, responseStatus.getReasonPhrase(), response.getBody());
        log.error(errorMsg);
        throw new BaseException(errorMsg);

    }

}
