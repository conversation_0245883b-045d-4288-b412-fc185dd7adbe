package com.curefit.odin.utils.service;

import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.odin.enums.GmailMessageFormat;
import com.curefit.odin.utils.GoogleEmailMapper;
import com.curefit.odin.utils.pojo.GoogleEmail;
import com.google.api.services.gmail.Gmail;
import com.google.api.services.gmail.GmailScopes;
import com.google.api.services.gmail.model.ListMessagesResponse;
import com.google.api.services.gmail.model.Message;
import com.google.api.services.gmail.model.Thread;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.curefit.odin.commons.Constants.ODIN_GMAIL_ACCOUNT_NAME;

@Component
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class GoogleEmailService extends GoogleService {
    private static final String USER = "me";
    private static final DateTimeFormatter gmailDateFormatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");

    @Autowired
    RollbarService rollbarService;

    @Autowired
    GoogleEmailProcessor googleEmailProcessor;

    @Autowired
    GmailMessageService gmailMessageService;

    @Autowired
    GoogleEmailMapper googleEmailMapper;

    Gmail gmailService;

    private Gmail getGmailService() {
        if (this.gmailService == null) {
            try {
                this.gmailService = getGmailService(ODIN_GMAIL_ACCOUNT_NAME);
            } catch (Exception e) {
                throw new RuntimeException("Failed to initialize GmailService", e);
            }
        }
        return this.gmailService;
    }

    @Override
    public List<String> getScopes() {
        return Collections.singletonList(GmailScopes.GMAIL_READONLY);
    }

    public Message getMessage(String messageId, GmailMessageFormat messageFormat) throws IOException {
        return getGmailService().users()
                .messages()
                .get(USER, messageId)
                .setFormat(messageFormat.getFormatName())
                .execute();
    }

    public List<Message> getMessages(Set<String> messageIds, GmailMessageFormat messageFormat) throws IOException {
        List<Message> messages = new ArrayList<>();
        for (String messageId : messageIds) {
            messages.add(getMessage(messageId, messageFormat));
        }
        return messages;
    }

    public List<Message> getMessagesInMinimalFormat(LocalDate date) throws IOException {
        List<Message> messages = new ArrayList<>();

        String searchQuery = String.format("in:inbox after:%s before:%s", date.format(gmailDateFormatter), date.plusDays(1).format(gmailDateFormatter));
        Gmail.Users.Messages.List request = getGmailService()
                .users()
                .messages()
                .list(USER)
                .setQ(searchQuery);

        ListMessagesResponse response = request.execute();
        while (response.getMessages() != null) {
            messages.addAll(response.getMessages());
            if (response.getNextPageToken() != null) {
                request.setPageToken(response.getNextPageToken());
                response = request.execute();
            } else {
                break;
            }
        }
        return messages;
    }

    public Thread getThread(String threadId, GmailMessageFormat messageFormat) throws IOException {
        return getGmailService()
                .users()
                .threads()
                .get(USER, threadId)
                .setFormat(messageFormat.getFormatName())
                .execute();
    }

    public void processMessage(Message message) {
        GoogleEmail googleEmail;
        try {
            log.info("Processing message. messageId: {}. Converting to google email.", message.getId());
            googleEmail = googleEmailMapper.convertToGoogleEmail(message);
        } catch (Exception e) {
            throw new RuntimeException(String.format("Failed to process messageId: %s. Failed to convert to google email.", message.getId()), e);
        }

        try {
            log.info("Processing message. messageId: {}. googleEmail: {}", message.getId(), googleEmail);
            googleEmailProcessor.processEmail(googleEmail);
        } catch (Exception e) {
            throw new RuntimeException("Failed to process messageId: " + message.getId(), e);
        }

        log.info("Successfully processed message. messageId: {}", message.getId());
    }

    public void processMessages(Set<String> messageIds) throws Exception {
        Set<String> unprocessedMessageIds = gmailMessageService.getUnprocessedMessageIds(messageIds);
        List<Message> messages = getMessages(unprocessedMessageIds, GmailMessageFormat.FULL);
        messages.sort(Comparator.comparing(Message::getInternalDate));

        Map<String, TreeSet<Message>> messagesByThreadIdMap = messages.stream()
                .collect(Collectors.groupingBy(
                        Message::getThreadId,
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(Message::getInternalDate)))
                ));
        for (String threadId : messagesByThreadIdMap.keySet()) {
            TreeSet<Message> threadMessages = messagesByThreadIdMap.get(threadId);
            try {
                for (Message message : threadMessages) {
                    processMessage(message);
                }
            } catch (Exception e) {
                log.error("Failed to process some messages in threadId: {}", threadId, e);
            }
        }
    }

    public void processMessagesInThread(String threadId) throws Exception {
        Thread thread = getThread(threadId, GmailMessageFormat.MINIMAL);
        Set<String> messageIds = Optional.ofNullable(thread.getMessages())
                .orElse(Collections.emptyList())
                .stream()
                .map(Message::getId)
                .collect(Collectors.toSet());
        log.info("Found {} messages in threadId: {}", messageIds.size(), threadId);
        processMessages(messageIds);
    }

    public void processMessagesReceivedOnDate(LocalDate date) throws Exception {
        log.info("Processing messages for date: {}", date);
        List<Message> messages = getMessagesInMinimalFormat(date);
        Set<String> messageIds = messages.stream().map(Message::getId).collect(Collectors.toSet());

        log.info("Found {} messages to be processed for date: {}", messageIds.size(), date);
        processMessages(messageIds);
        log.info("Completed processing all {} messages.", messageIds.size());
    }
}
