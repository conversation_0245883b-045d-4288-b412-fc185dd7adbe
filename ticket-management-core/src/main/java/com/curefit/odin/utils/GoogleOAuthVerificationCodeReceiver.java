package com.curefit.odin.utils;

import com.curefit.commons.integrations.rollbar.RollbarService;
import com.google.api.client.extensions.java6.auth.oauth2.VerificationCodeReceiver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class GoogleOAuthVerificationCodeReceiver implements VerificationCodeReceiver {

  private final Lock lock = new ReentrantLock();

  private final Condition authorizationResponseCondition = lock.newCondition();

  private String code;

  private String error;

  private String authorizationUrl;
  private String accountName;

  @Value("${google.oauth.callback.url}")
  private String callbackUrl;

  @Autowired
  private RollbarService rollbarService;

  @Override
  public String getRedirectUri() {
    return callbackUrl;
  }

  @Override
  public String waitForCode() throws IOException {
    lock.lock();
    try {
      while (code == null && error == null) {
        log.info("Waiting for Google OAuth Verification Code");
        rollbarService.log("Account => " + accountName + ", Google OAuth Token Url => " + authorizationUrl);
        authorizationResponseCondition.awaitUninterruptibly();
      }
      if (error != null) {
        rollbarService.log("Google OAuth Error received in callback : " + error);
        throw new IOException("User authorization failed (" + error + ")");
      }
      rollbarService.log("Google OAuth Verification Code Received");
      log.info("Got Google OAuth Verification Code {} ", code);
      return code;
    } finally {
      lock.unlock();
    }
  }

  @Override
  public void stop() {
    code = null;
    error = null;
  }

  public void receiveCodeAndError(String code, String error) {
    lock.lock();
    try {
      this.code = code;
      this.error = error;
      this.authorizationResponseCondition.signal();
    } finally {
      lock.unlock();
    }
  }

  public void setAuthorizationUrl(String authorizationUrl) {
    this.authorizationUrl = authorizationUrl;
  }

  public void setAccountName(String accountName) {
    this.accountName = accountName;
  }

}
