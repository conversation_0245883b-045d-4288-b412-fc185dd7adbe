package com.curefit.odin.utils;

import com.curefit.odin.enums.TicketSource;
import com.curefit.odin.utils.pojo.GoogleEmail;
import com.google.api.services.gmail.model.Message;
import com.google.api.services.gmail.model.MessagePartBody;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.curefit.odin.commons.Constants.*;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class GoogleEmailMapper {

  @Autowired
  EmailParser emailParser;

  @Value("${odin.email}")
  String odinEmail;

  public GoogleEmail convertToGoogleEmail(Message message) {
    String messageString;
    MessagePartBody messagePartBody = message.getPayload().getBody();
    if (messagePartBody != null && messagePartBody.getSize() != 0) {
      messageString = new String(messagePartBody.decodeData());
    } else {
      messageString = new String(message.getPayload().getParts().get(0).getBody().decodeData());
    }
    GoogleEmail.GoogleEmailBuilder builder = GoogleEmail.builder();
    builder.isAutomatedEmail(false);
    message.getPayload().getHeaders().forEach(header -> {
      if (header.getName() != null) {
        switch (header.getName().toLowerCase()) {
          case FROM:
            String fromEmail = header.getValue();
            builder.from(parseEmailId(fromEmail));
            break;
          case SUBJECT:
            builder.subject(header.getValue());
            break;
          case TO:
            String toMailString = header.getValue();
            if (toMailString == null) toMailString = "";
            List<String> toMails = Arrays.stream(toMailString.split(","))
                .map(String::trim)
                .map(this::parseEmailId)
                .filter(email -> !StringUtils.isBlank(email))
                .collect(Collectors.toList());
            builder.to(toMails);
            break;
          // Check if mail is an automated message
          case AUTO_SUBMITTED:
            if (!header.getValue().equalsIgnoreCase("no")) builder.isAutomatedEmail(true);
            break;
          case X_AUTOREPLY:
            if (header.getValue().equalsIgnoreCase("yes")) builder.isAutomatedEmail(true);
            break;
          case PRECEDENCE:
            if (PRECEDENCE_VALUES.contains(header.getValue().toLowerCase())) builder.isAutomatedEmail(true);
            break;
          case X_AUTO_RESPONSE_SUPRESS:
            if (X_AUTO_RESPONSE_SUPRESS_VALUES.contains(header.getValue().toLowerCase())) builder.isAutomatedEmail(true);
            break;
        }
      }
    });

    GoogleEmail googleEmail = builder
            .messageId(message.getId())
            .threadId(message.getThreadId())
            .currentMessage(emailParser.parseEmail(messageString))
            .build();
    for (String toEmail : googleEmail.getTo()) {
      if (Objects.equals(odinEmail, emailParser.getEmailWithoutTag(toEmail))) {
        String toEmailTag = emailParser.getTagFromEmail(toEmail);
        if (!StringUtils.isEmpty(toEmailTag) && StringUtils.isNumeric(toEmailTag)) {
          googleEmail.setTicketId(Long.parseLong(toEmailTag));
          break;
        }
      }
    }
    if(googleEmail.getTicketId() == null) {
      googleEmail.setTicketId(emailParser.parseTicketId(googleEmail.getSubject()));
    }
    return googleEmail;
  }

  // To extract emailId from header strings "User <<EMAIL>>"
  private String parseEmailId(String userString) {
    String[] stringParts = userString.split(">")[0].split("<");
    if (stringParts.length == 2) {
      return stringParts[1];
    } else {
      return stringParts[0];
    }
  }
}
