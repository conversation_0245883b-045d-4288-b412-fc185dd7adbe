package com.curefit.odin.utils.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.repositories.DomainDAO;
import com.curefit.odin.admin.service.TenantService;
import com.curefit.odin.utils.models.Domain;
import com.curefit.odin.utils.pojo.DomainEntry;
import com.curefit.odin.utils.pojo.TenantEntry;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */


@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DomainService extends BaseMySQLService<Domain, DomainEntry> {

  @Autowired
  RollbarService rollbarService;

  @Autowired
  TenantService tenantService;

  public DomainService(DomainDAO domainDAO) {
    super(domainDAO);
  }

  @Override
  public DomainEntry convertToEntry(Domain entity) {
    DomainEntry domainEntry = super.convertToEntry(entity);
    List<TenantEntry> tenantEntries = new ArrayList<>();
    if (entity.getWhitelistedTenants() != null) {
      entity.getWhitelistedTenants().forEach(tenantId -> {
        try {
          tenantEntries.add(tenantService.findOneById(tenantId));
        } catch (BaseException e) {
          log.error(e.getMessage(), e);
          rollbarService.error(e);
        }
      });
    }
    domainEntry.setWhitelistedTenantEntries(tenantEntries);
    return domainEntry;
  }

  @Cacheable(value = "fetchAllDomains", unless = "#result == null")
  public List<DomainEntry> fetchAll() {
    try {
      return search(0, -1, null, null, "active.eq:true").getElements();
    } catch (InvalidSeachQueryException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    return Collections.emptyList();
  }

  @Cacheable(value = "fetchWhiteListedTenantsByDomain", unless = "#result == null")
  public List<TenantEntry> fetchWhiteListedTenantsByDomain(String domain) {
    try {
      List<DomainEntry> domainEntries = search(0, -1, null, null, "url.eq:" + domain + ";active.eq:true").getElements();
      if (domainEntries.size() != 0) {
        DomainEntry domainEntry = domainEntries.get(0);
        return domainEntry.getWhitelistedTenantEntries();
      }
    } catch (InvalidSeachQueryException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    return Collections.emptyList();
  }
}