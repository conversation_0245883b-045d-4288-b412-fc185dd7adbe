package com.curefit.odin.utils;

import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.service.TenantService;
import com.curefit.odin.utils.pojo.TenantEntry;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class EmailParser {

  private static final Pattern SIGNATURE_PATTERN = Pattern.compile("((^Sent from my (\\s*\\w+){1,3}$)|(^-\\w|^\\s?__|^\\s?--|^\u2013|^\u2014))", Pattern.DOTALL);
  private static final Pattern QUOTE_PATTERN = Pattern.compile("(^>+)", Pattern.DOTALL);
  private static List<Pattern> compiledQuoteHeaderPatterns;

  List<Pattern> subjectPatterns;


  @Autowired
  public EmailParser(TenantService tenantService) throws InvalidSeachQueryException {
    List<TenantEntry> tenantEntries = tenantService.search(0, -1, null, null, "active.eq:true").getElements();
    List<String> tenants = tenantEntries.stream().map(TenantEntry::getName).collect(Collectors.toList());

    subjectPatterns = tenants.stream()
            .map(String::toUpperCase)
            .map(tenant -> Pattern.compile("\\(" + tenant + "[ ]*/[ 0-9]+\\)"))
            .collect(Collectors.toList());

    List<String> quoteHeadersRegex = new ArrayList<String>() {{
      add("^(On\\s(.{1,500})wrote:)");
      add("^(On\\s(.{1,500})<)");
      add(".*> wrote");
      add("From:[^\\n]+\\n?([^\\n]+\\n?){0,2}To:[^\\n]+\\n?([^\\n]+\\n?){0,2}Subject:[^\\n]+");
      add("To:[^\\n]+\\n?([^\\n]+\\n?){0,2}From:[^\\n]+\\n?([^\\n]+\\n?){0,2}Subject:[^\\n]+");
    }};
    compiledQuoteHeaderPatterns = new ArrayList<>();
    for (String regex : quoteHeadersRegex) {
      compiledQuoteHeaderPatterns.add(Pattern.compile(regex, Pattern.MULTILINE | Pattern.DOTALL));
    }
  }

  public String parseEmail(String emailText) {
    emailText = emailText.replace("\r\n", "\n");
    List<String> lines = Arrays.asList(emailText.split("\n"));
    Collections.reverse(lines);
    int i = 0;
    for (int linesSize = lines.size(); i < linesSize; i++) {
      String line = lines.get(i);
      line = StringUtils.stripEnd(line, "\n");
      line = StringUtils.stripEnd(line, null);
      if (!StringUtils.isEmpty(line) && !isSignature(line) && !isQuoteHeader(line) && !isQuote(line)) {
        break;
      }
    }
    lines = lines.subList(i, lines.size());
    Collections.reverse(lines);
    return StringUtils.join(lines, "\n");
  }

  public Long parseTicketId(String input) {
    return subjectPatterns.stream()
            .map(pattern -> pattern.matcher(input))
            .filter(Matcher::find)
            .map(matcher -> input.substring(matcher.start(), matcher.end() - 1))
            .findFirst()
            .map(this::getTicketId).orElse(null);
  }


  private Long getTicketId(String tenantTicketString) {
    String[] tenantTicketId = tenantTicketString.split("/");
    if (tenantTicketId.length == 2) {
      String ticketId = tenantTicketId[1].trim();
      if (StringUtils.isNumeric(ticketId)) {
        return Long.parseLong(ticketId);
      }
    }
    return null;
  }

  private boolean isSignature(String line) {
    return SIGNATURE_PATTERN.matcher(line).find();
  }

  private boolean isQuote(String line) {
    return QUOTE_PATTERN.matcher(line).find();
  }

  private boolean isQuoteHeader(String line) {
    for (Pattern p : compiledQuoteHeaderPatterns) {
      if (p.matcher(line).find()) {
        return true;
      }
    }
    return false;
  }

  public String getTagFromEmail(String toMail) {
    String extraInfo = "";
    int startInd = toMail.indexOf("+");
    if (startInd != -1) {
      extraInfo = toMail.substring(startInd + 1, toMail.indexOf("@"));
    }
    return extraInfo;
  }

  public String getEmailWithoutTag(String toMail) {
    int plusInd = toMail.indexOf("+");
    int andInd = toMail.indexOf("@");
    if (plusInd == -1) {
      return toMail;
    } else {
      return toMail.substring(0, plusInd) + toMail.substring(andInd);
    }
  }
}
