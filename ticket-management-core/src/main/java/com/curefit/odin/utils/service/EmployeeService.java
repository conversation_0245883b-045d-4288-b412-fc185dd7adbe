package com.curefit.odin.utils.service;

import com.curefit.common.rest.client.CommonHttpHelper;
import com.curefit.odin.utils.pojo.EmployeeDetails;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import java.util.HashMap;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
public class EmployeeService {

  @Autowired
  private CommonHttpHelper httpHelper;

  @Value("${neo.url}")
  String baseUrl;

  public EmployeeDetails fetchEmployeeDetails(String tenantCode, String empId) {
    String url = baseUrl + "/employee/uin/" + empId + "?showAll=false";
    return httpHelper.request(url, HttpMethod.GET, null, new HashMap<String, String>() {{
      put("tenant", tenantCode);
    }}, EmployeeDetails.class).getBody();
  }

  public EmployeeDetails fetchEmployeeDetailsByPhone(String tenantCode, String phone, String countryCode) {
    String url = baseUrl + "/employee/phone/" + phone + "?countryCode=" + countryCode + "&showAll=false";
    return httpHelper.request(url, HttpMethod.GET, null, new HashMap<String, String>() {{
      put("tenant", tenantCode);
    }}, EmployeeDetails.class).getBody();
  }

  public EmployeeDetails fetchEmployeeDetailsByEmail(String tenantCode, String emailId) {
    String url = baseUrl + "/employee/email/" + emailId;
    return httpHelper.request(url, HttpMethod.GET, null, new HashMap<String, String>() {{
      put("tenant", tenantCode);
    }}, EmployeeDetails.class).getBody();
  }
}
