package com.curefit.odin.utils.service;

import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import com.curefit.common.data.enums.AppStatus;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.rest.client.CommonHttpHelper;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.odin.utils.pojo.MaverickUserEntry;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class MaverickService {

  @Value("${maverick.url}")
  String maverickURL;

  @Value("${maverick.etl.url}")
  String maverickEtlURL;

  @Autowired
  CommonHttpHelper httpHelper;

  @Autowired
  RollbarService rollbarService;

  @Cacheable(value = "fetchAllUsers", unless = "#result == null")
  public List<MaverickUserEntry> fetchAllUsers() throws BaseException {
    log.debug("Fetching all users");
    try {
      URIBuilder uriBuilder = new URIBuilder(maverickURL);
      uriBuilder.addParameter("result-size", "1000");
      ResponseEntity<List<MaverickUserEntry>> response =
          httpHelper.request(uriBuilder.build().toString(), HttpMethod.GET, null, null,
              new TypeReference<List<MaverickUserEntry>>() {});
      return response.getBody();
    } catch (URISyntaxException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
      throw new BaseException(e.getMessage());
    }
  }

  @Cacheable(value = "fetchUserByEmailId", unless = "#result == null")
  public MaverickUserEntry fetchUserByEmailId(String emailId) throws BaseException {
    log.debug("Fetching user: {} ", emailId);
    if (StringUtils.isBlank(emailId)) {
      throw new BaseException("User Not Found: " + emailId, AppStatus.BAD_REQUEST);
    }
    String url = maverickURL + "?q=" + emailId.replace("\u00a0","").trim();
    ResponseEntity<List<MaverickUserEntry>> response = httpHelper.request(url, HttpMethod.GET,
        null, null, new TypeReference<List<MaverickUserEntry>>() {});
    if (response.getBody() == null || response.getBody().isEmpty()) {
      throw new BaseException("User Not Found: " + emailId, AppStatus.BAD_REQUEST);
    } else {
      return response.getBody().get(0);
    }
  }

  public Optional<MaverickUserEntry> searchUser(String emailId) {
    try {
      return Optional.of(fetchUserByEmailId(emailId));
    } catch (BaseException e) {
      return Optional.empty();
    }
  }

  public void refreshUsers() {
    log.debug("Refreshing all users in maverick");
    try {
      URIBuilder uriBuilder = new URIBuilder(maverickEtlURL);
      httpHelper.request(uriBuilder.build().toString(), HttpMethod.POST, null, null, Object.class);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
  }

}
