package com.curefit.odin.utils.service;

import com.curefit.odin.enums.LocationType;
import com.curefit.odin.utils.pojo.LocationEntry;
import com.curefit.odin.utils.pojo.LocationHierarchyNode;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.curefit.odin.enums.LocationType.*;

/**
 * <AUTHOR>
 */

@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
@Component
public class LocationHierarchyService {

  private final static String ALL_NAME = "All";
  private final static String SEPARATOR = "__";

  private static final Map<LocationType, Function<LocationEntry, String>> locationHierarchyFunctionMap = new EnumMap<LocationType, Function<LocationEntry, String>>(LocationType.class) {{
    put(ALL, locationEntry -> ALL.name());
    put(COUNTRY, LocationEntry::getCountryId);
    put(CITY, LocationEntry::getCityId);
    put(CLUSTER, LocationEntry::getClusterId);
    put(CENTRE, locationEntry -> String.valueOf(locationEntry.getId()));
  }};

  public List<LocationHierarchyNode> getAllLocationNodes(LocationEntry locationEntry) {
    return LocationType.getAllReverseOrdered().stream().map(locationType -> {
              String locationTypeValue = locationHierarchyFunctionMap.get(locationType).apply(locationEntry);
              LocationHierarchyNode locationHierarchyNode = new LocationHierarchyNode();
              locationHierarchyNode.setCode(locationTypeValue);
              locationHierarchyNode.setType(locationType);
              return locationHierarchyNode;
            }).collect(Collectors.toList());
  }

  public LocationHierarchyNode getLocationHierarchyTree(List<LocationEntry> locationEntries) {
    LocationHierarchyNode rootNode = new LocationHierarchyNode(ALL.name(), ALL_NAME, ALL, new ArrayList<>());
    createTree(locationEntries, rootNode);
    return rootNode;
  }

  public List<LocationHierarchyNode> getFlatLocationHierarchy(List<LocationEntry> locationEntries) {
    Map<LocationHierarchyNode, Map<LocationHierarchyNode, Map<LocationHierarchyNode, List<LocationHierarchyNode>>>> data = locationEntries.stream()
            .collect(Collectors.groupingBy(entry -> new LocationHierarchyNode(entry.getCountryId(), entry.getCountryName(), COUNTRY, getCountryLocationEntry(entry)),
                    Collectors.groupingBy(entry -> new LocationHierarchyNode(entry.getCityId(), entry.getCityName(), CITY, getCityLocationEntry(entry)),
                            Collectors.groupingBy(entry -> new LocationHierarchyNode(entry.getClusterId(), entry.getClusterName(), CLUSTER, getClusterLocationEntry(entry)),
                                    Collectors.mapping(entry -> new LocationHierarchyNode(String.valueOf(entry.getId()), entry.getCenterName(), CENTRE, entry),
                                            Collectors.toList())))));

    List<LocationHierarchyNode> nodes = new ArrayList<>();
    LocationHierarchyNode rootNode = new LocationHierarchyNode(ALL.name(), ALL_NAME, ALL, LocationEntry.builder().build());
    nodes.add(rootNode);
    data.forEach((countryNode, countryNodeLocations) -> {
      nodes.add(countryNode);
      countryNodeLocations.forEach((cityNode, cityNodeLocations) -> {
        nodes.add(cityNode);
        cityNodeLocations.forEach((clusterNode, clusterNodeLocations) -> {
          if (!StringUtils.isEmpty(clusterNode.getCode())) {
            nodes.add(clusterNode);
          }
          nodes.addAll(clusterNodeLocations);
        });
      });
    });
    return nodes;
  }

  private LocationEntry getCountryLocationEntry(LocationEntry entry) {
    return LocationEntry.builder()
            .countryId(entry.getCountryId())
            .countryName(entry.getCountryName())
            .build();
  }

  private LocationEntry getCityLocationEntry(LocationEntry entry) {
    return LocationEntry.builder()
            .countryId(entry.getCountryId())
            .countryName(entry.getCountryName())
            .cityId(entry.getCityId())
            .cityName(entry.getCityName())
            .build();
  }

  private LocationEntry getClusterLocationEntry(LocationEntry entry) {
    return LocationEntry.builder()
            .countryId(entry.getCountryId())
            .countryName(entry.getCountryName())
            .cityId(entry.getCityId())
            .cityName(entry.getCityName())
            .clusterId(entry.getClusterId())
            .clusterName(entry.getClusterName())
            .build();
  }


  private void createTree(List<LocationEntry> locationEntries, LocationHierarchyNode rootNode) {
    locationEntries.forEach(locationEntry -> {

      LocationHierarchyNode countryNode = getOrCreateNode(locationEntry.getCountryId(), locationEntry.getCountryName(), COUNTRY, rootNode);
      LocationHierarchyNode cityNode = getOrCreateNode(locationEntry.getCityId(), locationEntry.getCityName(), CITY, countryNode);

      //cluster node if cluster exists, else skip it.
      LocationHierarchyNode clusterNode;
      if (locationEntry.getClusterId() == null) {
        clusterNode = cityNode;
      } else {
        clusterNode = getOrCreateNode(locationEntry.getClusterId(), locationEntry.getClusterName(), CLUSTER, cityNode);
      }

      getOrCreateNode(String.valueOf(locationEntry.getId()), locationEntry.getCenterName(), CENTRE, clusterNode);
    });
  }

  private <T> LocationHierarchyNode getOrCreateNode(String code, String name, LocationType type, LocationHierarchyNode<T> parent) {
    return parent.getChildren().stream()
            .filter(node -> node.getCode().equalsIgnoreCase(code) && node.getType().equals(type))
            .findFirst()
            .orElseGet(() -> {
              LocationHierarchyNode newNode = new LocationHierarchyNode(code, name, type, new ArrayList<>());
              parent.getChildren().add(newNode);
              return newNode;
            });
  }

  public String getCode(String completeCode) {
    int index = completeCode.lastIndexOf(SEPARATOR);
    if (index == -1) {
      return completeCode;
    }
    return completeCode.substring(index + SEPARATOR.length());
  }
}