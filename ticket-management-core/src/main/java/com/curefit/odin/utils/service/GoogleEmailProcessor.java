package com.curefit.odin.utils.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.util.HeadersUtils;
import com.curefit.odin.admin.pojo.CategoryEntry;
import com.curefit.odin.admin.pojo.DefaultAssigneeEntry;
import com.curefit.odin.admin.pojo.SubCategoryEntry;
import com.curefit.odin.admin.pojo.UserEntry;
import com.curefit.odin.admin.service.*;
import com.curefit.odin.config.OdinConfigurations;
import com.curefit.odin.enums.Entity;
import com.curefit.odin.enums.Status;
import com.curefit.odin.enums.TicketSource;
import com.curefit.odin.user.pojo.CommentEntry;
import com.curefit.odin.user.pojo.TicketEntry;
import com.curefit.odin.user.service.CommentService;
import com.curefit.odin.user.service.TicketService;
import com.curefit.odin.utils.EmailParser;
import com.curefit.odin.utils.pojo.*;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.curefit.odin.commons.Constants.NAMESPACE_HEADER;
import static com.curefit.odin.commons.Constants.USER_ID_HEADER;
import static com.curefit.odin.commons.Constants.ODIN_NAMESPACE;
import static com.curefit.odin.commons.Constants.ODIN_NOTIFICATION_EMAIL_IDS;


/**
 * <AUTHOR>
 */

@Component
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class GoogleEmailProcessor {

  @Autowired
  GmailThreadService gmailThreadService;

  @Autowired
  GmailMessageService gmailMessageService;

  @Autowired
  TicketService ticketService;

  @Autowired
  CommentService commentService;

  @Autowired
  UserService userService;

  @Autowired
  SLAService slaService;

  @Autowired
  DefaultAssigneeService defaultAssigneeService;

  @Autowired
  EmailTicketConfigService emailTicketConfigService;

  @Autowired
  OdinConfigurations odinConfigurations;

  @Autowired
  RollbarService rollbarService;

  @Autowired
  EmailParser emailParser;

  @Autowired
  private TenantService tenantService;

  @Autowired
  private CategoryService categoryService;

  @Autowired
  private SubCategoryService subCategoryService;

  @Autowired
  private LocationService locationService;


  @Value("${odin.email}")
  String odinEmail;

  static final Pattern emailSubjectPattern = Pattern.compile("\\[([^\\[\\]\\|]+)(\\|([^\\[\\]\\|]*))?\\][ \\t]*(\\[([^\\[\\]\\|]+)(\\|([^\\[\\]\\|]*))?\\])?");

  @Transactional(rollbackFor = Exception.class)
  public void processEmail(GoogleEmail email) throws Exception {
    try {
      setHeaders(email);

      boolean saveThread;

      Optional<GmailThreadEntry> threadEntryOpt = gmailThreadService.findByThreadId(email.getThreadId());
      log.info("thread entry from db {} for threadId {}", threadEntryOpt, email.getThreadId());

      if (threadEntryOpt.isPresent()) {
        email.setTicketId(threadEntryOpt.get().getTicketId());
        if (email.getTicketId() == null) {
          return;
        }
        saveThread = false;
      } else {
        saveThread = true;
      }

      if (email.getIsAutomatedEmail()) {
        log.info("Automated email, ignoring the email with messageId: {}", email.getMessageId());
        saveMessageId(email, null, null);
      } else if (ODIN_NOTIFICATION_EMAIL_IDS.contains(emailParser.getEmailWithoutTag(email.getFrom()))) {
        log.info("Odin notification email, ignoring the email with messageId: {}", email.getMessageId());
        saveMessageId(email, null, null);
      } else {
        if (email.getTicketId() == null) {
          Long ticketId = null;
          try {
            ticketId = createTicketFromMessage(email);
          } catch (Exception e) {
            log.error(String.format("Error in creating ticket from email for messageId: %s", email.getMessageId()), e);
            rollbarService.error(e, String.format("Error in creating ticket from email for messageId: %s", email.getMessageId()));
          }
          email.setTicketId(ticketId);
          saveMessageId(email, Entity.TICKET, ticketId);
        } else {
          Long commentId = null;
          List<String> allowedUsers = new ArrayList<>();
          try {
            TicketEntry ticketEntry = ticketService.findOneById(email.getTicketId());
            if (ticketEntry.getReporter() != null)
              allowedUsers.add(ticketEntry.getReporter().getEmailId());
            if (ticketEntry.getAssigneeQueueUsers() != null)
              allowedUsers.addAll(ticketEntry.getAssigneeQueueUsers().stream().map(UserEntry::getEmailId).collect(Collectors.toList()));
          } catch (BaseException e) {
            log.info(String.format("Ticket id %s not found. ignoring the email with messageId: %s", email.getTicketId(), email.getMessageId()));
          }
          if (allowedUsers.contains(email.getFrom())) {
            try {
              commentId = createCommentFromMessage(email.getTicketId(), email);
            } catch (Exception e) {
              log.error(String.format("Error in creating comment from email for ticketId: %s, messageId: %s", email.getTicketId(), email.getMessageId()), e);
              rollbarService.error(e, String.format("Error in creating comment from email for ticketId: %s, messageId: %s", email.getTicketId(), email.getMessageId()));
            }
          }
          saveMessageId(email, Entity.COMMENT, commentId);
        }
        if (saveThread) {
          saveThread(email, email.getTicketId());
        }
      }
    } finally {
      HeadersUtils.clearHeaders();
    }
  }

  private Long createTicketFromMessage(GoogleEmail email) throws Exception {
    if (email.getTo() == null || email.getTo().isEmpty()) {
      log.warn("To email is not passed in email message, ignoring the message {}", email);
      return null;
    }
    EmailTicketConfigEntry config = null;
    Optional<EmailTicketConfigEntry> configOpt;
    for (String toEmail : email.getTo()) {
      if (Objects.equals(odinEmail, emailParser.getEmailWithoutTag(toEmail))) {
        configOpt = emailTicketConfigService.findByEmail(emailParser.getTagFromEmail(toEmail));
      } else {
        configOpt = Optional.empty();
      }
      if (configOpt.isPresent()) {
        config = configOpt.get();
        break;
      } else {
        configOpt = emailTicketConfigService.findByAliasEmail(toEmail);
        if (configOpt.isPresent()) {
          config = configOpt.get();
          break;
        }
      }
    }
    if (config == null) {
      log.warn("email is not configured for ticket creation, ignoring the message {}", email);
      return null;
    }
    log.info("creating ticket from email message {}", email);

    TicketEntry ticketEntry = new TicketEntry();
    ticketEntry.setTenantId(config.getTenantId());
    if (config.getLocationId() != null) {
      LocationEntry locationEntry = new LocationEntry();
      locationEntry.setId(config.getLocationId());
      ticketEntry.setLocationEntry(locationEntry);
    }
    ticketEntry.setCategoryId(config.getCategoryId());
    ticketEntry.setSubCategoryId(config.getSubCategoryId());
    ticketEntry.setTitle(StringUtils.abbreviate(email.getSubject(), 255));
    ticketEntry.setDescription(StringUtils.abbreviate(email.getCurrentMessage(), 50_000));
    ticketEntry.setPriority(config.getPriority());
    ticketEntry.setSource(TicketSource.EMAIL);
    ticketEntry.setSourceRefId(email.getFrom());
    ticketEntry.setReporter(userService.findUserByMailId(email.getFrom()));
    ticketEntry.setCreatedBy(email.getFrom());
    ticketEntry.setStatus(Status.OPEN);

    this.populateDetailsFromEmailSubject(ticketEntry, email.getSubject());

    ticketEntry.setDueDate(new Date(slaService.getDueDate(System.currentTimeMillis(), ticketEntry.getCategoryId(), ticketEntry.getSubCategoryId(), ticketEntry.getPriority())));
    Long locationId = ticketEntry.getLocationEntry() != null ? ticketEntry.getLocationEntry().getId() : null;
    DefaultAssigneeEntry defaultAssigneeEntry = defaultAssigneeService.fetchDefaultAssignee(locationId, ticketEntry.getCategoryId(), ticketEntry.getSubCategoryId(), ticketEntry.getCreatedBy());
    if (defaultAssigneeEntry != null) {
      ticketEntry.setAssignedQueueId(defaultAssigneeEntry.getAssigneeQueueEntry().getId());
    }
    return ticketService.createWithoutCustomFields(ticketEntry).getId();
  }

  private void populateDetailsFromEmailSubject(TicketEntry ticketEntry, String subject) {
    Matcher matcher = emailSubjectPattern.matcher(subject);
    if (matcher.find()) {
      String tenantString = StringUtils.trim(matcher.group(1));
      Long tenantId = this.getTenantIdByName(tenantString);
      if (tenantId != null) {
        if (!Objects.equals(ticketEntry.getTenantId(), tenantId)) {
          ticketEntry.setLocationEntry(null);
          ticketEntry.setCategoryId(null);
          ticketEntry.setSubCategoryId(null);
        }
        ticketEntry.setTenantId(tenantId);
        String locationString = StringUtils.trim(matcher.group(3));
        LocationEntry locationEntry = this.getLocationEntryByTenantIdAndRefIdString(tenantId, locationString);
        if (locationEntry != null) {
          ticketEntry.setLocationEntry(locationEntry);
        }
        String categoryString = StringUtils.trim(matcher.group(5));
        Long categoryId = this.getCategoryIdByTenantIdAndCategoryString(tenantId, categoryString);
        if (categoryId != null) {
          if (!Objects.equals(ticketEntry.getCategoryId(), categoryId)) {
            ticketEntry.setSubCategoryId(null);
          }
          ticketEntry.setCategoryId(categoryId);
          String subCategoryString = StringUtils.trim(matcher.group(7));
          Long subCategoryId = this.getSubCategoryIdByCategoryIdAndSubCategoryString(categoryId, subCategoryString);
          if (subCategoryId != null) {
            ticketEntry.setSubCategoryId(subCategoryId);
          }
        }
      }
    }
  }

  private Long createCommentFromMessage(Long ticketId, GoogleEmail email) throws BaseException {
    // TODO : need to handle the case of Fwd mail from freshdesk : email.getTicketSource() != null
    if (email.getFrom().contains(odinConfigurations.getCustomerSupportEmail())) {
      return null;
    }
    Optional<CommentEntry> latestCommentOpt = commentService.findLatestCommentOnTicketByUser(ticketId, email.getFrom());
    if (latestCommentOpt.isPresent() && Objects.equals(latestCommentOpt.get().getComment(), email.getCurrentMessage())) {
      log.info("Duplicate comment, ignoring the email with messageId: {}", email.getMessageId());
      return null;
    }
    log.info("adding comment on ticket {} from email message {}", ticketId, email);
    CommentEntry commentEntry = new CommentEntry();
    commentEntry.setCreatedBy(email.getFrom());
    commentEntry.setTicketId(ticketId);
    commentEntry.setComment(StringUtils.abbreviate(email.getCurrentMessage(), 50_000));
    return commentService.create(commentEntry).getId();
  }

  private void saveMessageId(GoogleEmail email, Entity entity, Long entityId) throws BaseException {
    GmailMessageEntry entry = new GmailMessageEntry();
    entry.setCreatedBy(email.getFrom());
    entry.setMessageId(email.getMessageId());
    entry.setEntityName(entity);
    entry.setEntityId(entityId);
    gmailMessageService.create(entry);
  }

  private void saveThread(GoogleEmail email, Long ticketId) throws BaseException {
    GmailThreadEntry entry = new GmailThreadEntry();
    entry.setCreatedBy(email.getFrom());
    entry.setThreadId(email.getThreadId());
    entry.setTicketId(ticketId);
    gmailThreadService.create(entry);
  }

  private void setHeaders(GoogleEmail email) {
    HeadersUtils.addHeader(USER_ID_HEADER, email.getFrom());
    HeadersUtils.addHeader(NAMESPACE_HEADER, ODIN_NAMESPACE);
  }

  private Long getTenantIdByName(String tenantName) {
    if (StringUtils.isNotBlank(tenantName)) {
      TenantEntry tenantEntry = this.tenantService.fetchByTenantName(tenantName);
      if (tenantEntry != null) return tenantEntry.getId();
    }
    return null;
  }

  private LocationEntry getLocationEntryByTenantIdAndRefIdString(Long tenantId, String refIdString) {
    if (StringUtils.isNotBlank(refIdString) && StringUtils.isNumeric(refIdString)) {
      try {
        List<LocationEntry> locationEntries = this.locationService.findByReferenceIds(Collections.singleton(refIdString), tenantId);
        if (locationEntries != null && !locationEntries.isEmpty()) {
          return locationEntries.get(0);
        }
      } catch (BaseException e) {
        log.error("[EMAIL_TICKET] Error in fetching location. " + e.getMessage());
      }
    }
    return null;
  }

  private Long getCategoryIdByTenantIdAndCategoryString(Long tenantId, String categoryString) {
    if (StringUtils.isNotBlank(categoryString)) {
      if (StringUtils.isNumeric(categoryString)) {
        try {
          Long categoryId = Long.parseLong(categoryString);
          CategoryEntry categoryEntry = categoryService.findOneById(categoryId);
          if (Objects.equals(categoryEntry.getTenantId(), tenantId)) {
            return categoryEntry.getId();
          }
        } catch (BaseException | NumberFormatException e) {
          log.error("[EMAIL_TICKET] Error in fetching category. " + e.getMessage());
        }
      } else {
        CategoryEntry categoryEntry = categoryService.fetchByTenantIdAndName(tenantId, categoryString);
        if (categoryEntry != null) {
          return categoryEntry.getId();
        }
      }
    }
    return null;
  }

  private Long getSubCategoryIdByCategoryIdAndSubCategoryString(Long categoryId, String subCategoryString) {
    if (StringUtils.isNotBlank(subCategoryString)) {
      if (StringUtils.isNumeric(subCategoryString)) {
        try {
          Long subCategoryId = Long.parseLong(subCategoryString);
          SubCategoryEntry subCategoryEntry = subCategoryService.findOneById(subCategoryId);
          if (Objects.equals(subCategoryEntry.getCategoryId(), categoryId)) {
            return subCategoryEntry.getId();
          }
        } catch (BaseException | NumberFormatException e) {
          log.error("[EMAIL_TICKET] Error in fetching category. " + e.getMessage());
        }
      } else {
        SubCategoryEntry subCategoryEntry = subCategoryService.fetchByCategoryIdAndName(categoryId, subCategoryString);
        if (subCategoryEntry != null) {
          return subCategoryEntry.getId();
        }
      }
    }
    return null;
  }

}
