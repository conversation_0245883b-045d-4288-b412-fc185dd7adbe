package com.curefit.odin.utils;

import com.curefit.cf.commons.pojo.BaseEntry;
import com.curefit.common.data.enums.AppStatus;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.enums.LogType;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.util.HeadersUtils;
import com.curefit.odin.admin.pojo.DataSourceValueEntry;
import com.curefit.odin.admin.pojo.UserEntry;
import com.curefit.odin.commons.Constants;
import com.curefit.odin.config.OdinConfigurations;
import com.curefit.odin.enums.AuthType;
import com.curefit.odin.enums.UserRole;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import com.curefit.odin.user.pojo.TicketEntry;
import com.curefit.odin.user.pojo.TicketWatcherEntry;
import com.curefit.odin.user.service.TicketCacheService;
import com.curefit.odin.user.service.TicketService;
import com.curefit.odin.user.service.TicketWatcherService;
import com.curefit.odin.utils.pojo.LocationEntry;
import com.curefit.odin.utils.pojo.TenantEntry;
import com.curefit.odin.utils.pojo.UserContext;
import com.curefit.odin.utils.service.EmailDLService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Slf4j
@Component
public class AuthService {

  @Autowired
  TicketService ticketService;

  @Autowired
  RollbarService rollbarService;

  @Autowired
  ObjectMapper objectMapper;

  @Autowired
  TicketWatcherService ticketWatcherService;

  @Autowired
  EmailDLService emailDLService;

  @Autowired
  OdinConfigurations odinConfigurations;

  public static final String USER_MEMBERSHIP_HEADER = "X_USER_MEMBERSHIPS";

  public List<BaseOdinEntry> getContexts(AuthType authType) {
    Map<UserRole, Set<String>> userRoleContexts = getUserRoleContexts();
    Set<String> allContexts = userRoleContexts.values()
            .stream()
            .flatMap(Collection::stream)
            .collect(Collectors.toSet());
    Set<String> onlyParentContexts = getParentContexts(allContexts);
    List<List<String>> contextValues = onlyParentContexts
            .stream()
            .map(e -> Arrays.asList(e.split("\\.")))
            .collect(Collectors.toList());

    switch (authType) {
      case LOCATION:
        return contextValues.stream()
                .filter(contextValue -> contextValue.size() >= 2)
                .filter(contextValue -> StringUtils.isNumeric(contextValue.get(0)) && StringUtils.isNumeric(contextValue.get(1)))
                .map(contextValue -> {
                  LocationEntry locationEntry = new LocationEntry();
                  locationEntry.setTenantId(Long.valueOf(contextValue.get(0)));
                  locationEntry.setReferenceId(contextValue.get(1));
                  return locationEntry;
                })
                .collect(Collectors.toList());
      case TENANT:
        if (onlyParentContexts.contains(Constants.WATCHMEN_ALL_CONTEXT)) {
          return Collections.emptyList();
        }
        return contextValues.stream()
                .filter(contextValue -> contextValue.size() >= 1)
                .filter(contextValue -> StringUtils.isNumeric(contextValue.get(0)))
                .map(contextValue -> Long.valueOf(contextValue.get(0)))
                .distinct()
                .map(tenant -> {
                  TenantEntry tenantEntry = new TenantEntry();
                  tenantEntry.setId(tenant);
                  return tenantEntry;
                })
                .collect(Collectors.toList());
    }
    return Collections.emptyList();
  }

  public Set<String> filterTenantContexts(Long tenantId, List<BaseOdinEntry> contexts, AuthType authType) {
    switch (authType) {
      case LOCATION:
        if (!contexts.isEmpty()) {
          return contexts.stream()
                  .map(context -> ((LocationEntry) context))
                  .filter(context -> tenantId.equals(context.getTenantId()))
                  .map(DataSourceValueEntry::getReferenceId)
                  .collect(Collectors.toSet());
        }
        break;
    }
    return Collections.emptySet();
  }

  public Set<UserRole> getRoles() {
    Set<UserRole> userRoles = new HashSet<>(getUserRoleContexts().keySet());
    userRoles.add(UserRole.USER);
    return userRoles;
  }

  public void checkAuthorized(Long id, TicketCacheService service) throws BaseException {
    checkAuthorized(ticketService.findTicketById(service.findTicketIdById(id)));
  }

  public <T extends BaseOdinEntry> void checkAuthorized(T entry, TicketCacheService service) throws BaseException {
    checkAuthorized(ticketService.findTicketById(service.findTicketId(entry)));
  }

  private void checkAuthorized(TicketEntry ticketEntry) throws BaseException {
    List<BaseOdinEntry> tenantContexts = getContexts(AuthType.TENANT);

    if (!tenantContexts.isEmpty()) {
      List<TenantEntry> authorizedTenants = tenantContexts.stream()
              .map(context -> ((TenantEntry) context))
              .collect(Collectors.toList());
      Set<Long> authorizedTenantIds = authorizedTenants.stream().map(BaseEntry::getId).collect(Collectors.toSet());
      if (!authorizedTenantIds.contains(ticketEntry.getTenantId())) {
        throw new BaseException("TenantId " + ticketEntry.getTenantId() + " is not authorized to perform the action. Allowed tenants " + authorizedTenantIds, AppStatus.FORBIDDEN, LogType.WARNING);
      }
    }

    List<BaseOdinEntry> locationContexts = getContexts(AuthType.LOCATION);
    if (!locationContexts.isEmpty() && ticketEntry.getLocationEntry() != null) {
      Set<String> centreIds = filterTenantContexts(ticketEntry.getTenantId(), locationContexts, AuthType.LOCATION);
      if (!centreIds.isEmpty() && !centreIds.contains(ticketEntry.getLocationEntry().getReferenceId())) {
        throw new BaseException("Location reference id " + ticketEntry.getLocationEntry().getReferenceId() + " is not authorized to perform the action. Allowed locations " + centreIds, AppStatus.FORBIDDEN, LogType.WARNING);
      }
    }

    if (notAuthorizedIfConfidential(ticketEntry)) {
      throw new BaseException("User " + HeadersUtils.getCurrentUser() + " is not authorized to perform the action for ticket " + ticketEntry.getId(), AppStatus.FORBIDDEN, LogType.WARNING);
    }
  }

  public boolean notAuthorizedIfConfidential(TicketEntry ticketEntry) {
    if (ticketEntry.isConfidential()) {
      String currentUser = HeadersUtils.getCurrentUser();

      if (currentUser.equals("system") || currentUser.equals(odinConfigurations.getFreshdeskSourceEmail()) || currentUser.equals(ticketEntry.getReporter().getEmailId())) {
        return false;
      }
      Set<String> users = ticketEntry.getAssigneeQueueUsers() == null ? new HashSet<>() : ticketEntry.getAssigneeQueueUsers().stream()
              .map(UserEntry::getEmailId)
              .collect(Collectors.toSet());

      List<TicketWatcherEntry> entries = ticketWatcherService.findByTicketId(ticketEntry.getId());
      entries.forEach(entry -> users.add(entry.getUserId()));

      if (users.contains(currentUser)) {
        return false;
      }
      return users.stream().map(user -> emailDLService.getEmailsByDL(user)).flatMap(List::stream)
              .noneMatch(currentUser::equals);
    }
    return false;
  }

  private Map<UserRole, Set<String>> getUserRoleContexts() {
    try {
      String userMembership = HeadersUtils.getHeaderValue(USER_MEMBERSHIP_HEADER);
      if (userMembership != null) {
        List<UserContext> userContexts = objectMapper.readValue(userMembership, new TypeReference<List<UserContext>>() {
        });
        Map<UserRole, Set<String>> roleToContextsMap = userContexts.stream()
                .collect(Collectors.groupingBy(UserContext::getRole,
                        Collectors.mapping(userContext -> userContext.getContext().trim(), Collectors.toSet())));
        for (UserRole role : roleToContextsMap.keySet()) {
          Set<String> onlyParentContexts = getParentContexts(roleToContextsMap.get(role));
          if (!onlyParentContexts.isEmpty()) {
            roleToContextsMap.put(role, onlyParentContexts);
          }
        }
        return roleToContextsMap;
      }
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    return Collections.emptyMap();
  }

  private Set<String> getParentContexts(Collection<String> allContexts) {
    Set<String> contexts = new HashSet<>(allContexts);
    if (contexts.contains(Constants.WATCHMEN_ALL_CONTEXT)) {
      return new HashSet<>(Collections.singletonList(Constants.WATCHMEN_ALL_CONTEXT));
    }

    Set<String> tenantContexts = contexts.stream()
            .filter(x -> !x.contains(".") && StringUtils.isNumeric(x))
            .collect(Collectors.toSet());
    Set<String> centerContexts = contexts.stream()
            .filter(x -> {
              List<String> contextParts = Arrays.stream(x.split("\\.")).filter(StringUtils::isNotBlank).collect(Collectors.toList());
              return contextParts.size() >= 2 && StringUtils.isNumeric(contextParts.get(0)) && StringUtils.isNumeric(contextParts.get(1)) && !tenantContexts.contains(contextParts.get(0));
            })
            .collect(Collectors.toSet());
    return new HashSet<>(SetUtils.union(tenantContexts, centerContexts));
  }
}
