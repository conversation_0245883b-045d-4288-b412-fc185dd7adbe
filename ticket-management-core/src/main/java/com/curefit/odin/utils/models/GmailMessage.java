package com.curefit.odin.utils.models;

import com.curefit.odin.admin.models.BaseMySQLModel;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import javax.persistence.*;

/**
 * <AUTHOR>
 */

@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "gmail_message")
public class GmailMessage extends BaseMySQLModel {

  @Column(name = "message_id")
  String messageId;

  @Column(name = "entity_id")
  Long entityId;

  @Column(name = "entity_name")
  @Enumerated(value = EnumType.STRING)
  com.curefit.odin.enums.Entity entityName;

}
