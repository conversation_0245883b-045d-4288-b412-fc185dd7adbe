package com.curefit.odin.utils;

import com.curefit.odin.admin.pojo.BusinessHours;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

public class BusinessHoursUtils {
    private static final DateTimeFormatter BUSINESS_HOURS_TIME_FORMATTER = DateTimeFormatter.ofPattern("HHmm");

    private static LocalTime getLocalTime(String businessTime) {
        return LocalTime.parse(businessTime, BUSINESS_HOURS_TIME_FORMATTER).truncatedTo(ChronoUnit.MINUTES);
    }

    private static boolean isNightShift(BusinessHours businessHours) {
        LocalTime businessStartTime = BusinessHoursUtils.getLocalTime(businessHours.getStartTime());
        LocalTime businessEndTime = BusinessHoursUtils.getLocalTime(businessHours.getEndTime());
        return businessStartTime.isAfter(businessEndTime);
    }

    public static boolean isInsideBusinessHours(LocalDateTime curDateTime, BusinessHours businessHours) {
        LocalTime businessStartTime = BusinessHoursUtils.getLocalTime(businessHours.getStartTime());
        LocalTime businessEndTime = BusinessHoursUtils.getLocalTime(businessHours.getEndTime());
        LocalTime curTime = curDateTime.toLocalTime();
        if (isNightShift(businessHours)) {
            return curTime.equals(businessStartTime) || curTime.isAfter(businessStartTime) || curTime.isBefore(businessEndTime);
        } else {
            return curTime.equals(businessStartTime) || (curTime.isAfter(businessStartTime) && curTime.isBefore(businessEndTime));
        }
    }

    private static Long getRemainingBusinessMinutesInShift(LocalDateTime curDateTime, BusinessHours businessHours) {
        if (!BusinessHoursUtils.isInsideBusinessHours(curDateTime, businessHours)) {
            return 0L;
        }

        LocalTime businessEndTime = BusinessHoursUtils.getLocalTime(businessHours.getEndTime());
        LocalTime curTime = curDateTime.toLocalTime();
        if (isNightShift(businessHours)) {
            if (curTime.isBefore(LocalTime.MAX)) {
                return Duration.between(curTime, LocalTime.MAX).toMinutes() + 1 + Duration.between(LocalTime.MIDNIGHT, businessEndTime).toMinutes();
            } else {
                return Duration.between(curTime, businessEndTime).toMinutes();
            }
        } else {
            return Duration.between(curTime, businessEndTime).toMinutes();
        }
    }

    public static LocalDateTime getUpcomingBusinessStartTime(LocalDateTime curDateTime, BusinessHours businessHours) {
        if (BusinessHoursUtils.isInsideBusinessHours(curDateTime, businessHours)) {
            return curDateTime;
        }

        LocalTime businessStartTime = BusinessHoursUtils.getLocalTime(businessHours.getStartTime());
        LocalTime curTime = curDateTime.toLocalTime();
        if (BusinessHoursUtils.isNightShift(businessHours)) {
            return curDateTime.truncatedTo(ChronoUnit.DAYS)
                    .withHour(businessStartTime.getHour())
                    .withMinute(businessStartTime.getMinute());
        } else {
            return curDateTime.plusDays(curTime.isBefore(businessStartTime) ? 0 : 1)
                    .truncatedTo(ChronoUnit.DAYS)
                    .withHour(businessStartTime.getHour())
                    .withMinute(businessStartTime.getMinute());
        }
    }

    public static long calculateDueDateTime(long evaluationTimeInMilli, long taskMinutes, BusinessHours businessHours, ZoneId zoneId) {
        LocalDateTime evaluationTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(evaluationTimeInMilli), zoneId);
        LocalDateTime dueDateTime = BusinessHoursUtils.calculateDueDateTime(evaluationTime, taskMinutes, businessHours);
        return dueDateTime.atZone(zoneId).toInstant().toEpochMilli();
    }

    public static LocalDateTime calculateDueDateTime(LocalDateTime evaluationTime, long taskMinutes, BusinessHours businessHours) {
        LocalDateTime curDateTime = evaluationTime.truncatedTo(ChronoUnit.MINUTES);
        if (businessHours == null || businessHours.getStartTime() == null || businessHours.getEndTime() == null || businessHours.getStartTime().equals(businessHours.getEndTime())) {
            return curDateTime.plusMinutes(taskMinutes);
        }

        long minutesToAdd = Math.max(0L, taskMinutes);
        while (minutesToAdd > 0) {
            curDateTime = BusinessHoursUtils.getUpcomingBusinessStartTime(curDateTime, businessHours);
            long remainingBusinessMinutesInCurrentShift = BusinessHoursUtils.getRemainingBusinessMinutesInShift(curDateTime, businessHours);
            long minutesRequiredOutOfCurrentShift = Math.min(minutesToAdd, remainingBusinessMinutesInCurrentShift);
            curDateTime = curDateTime.plusMinutes(minutesRequiredOutOfCurrentShift);
            minutesToAdd -= minutesRequiredOutOfCurrentShift;
        }
        return curDateTime;
    }
}
