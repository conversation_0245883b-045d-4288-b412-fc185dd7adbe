package com.curefit.odin.utils.controller;

import com.curefit.odin.enums.GmailMessageFormat;
import com.curefit.odin.utils.DateUtils;
import com.curefit.odin.utils.service.GoogleEmailService;
import com.google.api.services.gmail.model.Message;
import com.google.api.services.gmail.model.Thread;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

@RestController
@RequestMapping("/readOdinEmails")
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class EmailReaderController {

    @Autowired
    GoogleEmailService googleEmailService;

    AtomicBoolean isRunning = new AtomicBoolean(false);

    @RequestMapping(method = RequestMethod.POST)
    public ResponseEntity<Object> readMails() {
        if (!isRunning.get()) {
            isRunning.set(true);
            try {
                LocalDate today = DateUtils.getCurrentISTLocalDateTime().toLocalDate();
                googleEmailService.processMessagesReceivedOnDate(today);
            } catch (Exception e) {
                log.error("Exception in email read job", e);
            }
            isRunning.set(false);
        }
        return new ResponseEntity<>("{}", HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.GET, value = "/messages/{messageId}")
    public ResponseEntity<Message> getMessageById(
            @PathVariable(value = "messageId") String messageId,
            @RequestParam(value = "messageFormat", required = false, defaultValue = "MINIMAL") @NotNull GmailMessageFormat messageFormat
    ) throws IOException {
        return ResponseEntity.ok(googleEmailService.getMessage(messageId, messageFormat));
    }

    @RequestMapping(method = RequestMethod.POST, value = "/messages/{messageId}/process")
    public ResponseEntity<String> processMessageById(
            @PathVariable(value = "messageId") String messageId
    ) throws Exception {
        googleEmailService.processMessages(Collections.singleton(messageId));
        return ResponseEntity.ok("{}");
    }

    @RequestMapping(method = RequestMethod.GET, value = "/thread/{threadId}")
    public ResponseEntity<Thread> getThreadById(
            @PathVariable(value = "threadId") String threadId,
            @RequestParam(value = "messageFormat", required = false, defaultValue = "MINIMAL") @NotNull GmailMessageFormat messageFormat
    ) throws IOException {
        return ResponseEntity.ok(googleEmailService.getThread(threadId, messageFormat));
    }

    @RequestMapping(method = RequestMethod.POST, value = "/thread/{threadId}/process")
    public ResponseEntity<String> processThreadById(
            @PathVariable(value = "threadId") String threadId
    ) throws Exception {
        googleEmailService.processMessagesInThread(threadId);
        return ResponseEntity.ok("{}");
    }

    @RequestMapping(method = RequestMethod.GET, value = "/messages/date/{date}")
    public ResponseEntity<List<Message>> getMessageByDate(
            @PathVariable(value = "date") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) @NotNull LocalDate date
    ) throws IOException {
        return ResponseEntity.ok(googleEmailService.getMessagesInMinimalFormat(date));
    }

    @RequestMapping(method = RequestMethod.POST, value = "/messages/date/{date}/process")
    public ResponseEntity<String> processMessageByDate(
            @PathVariable(value = "date") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) @NotNull LocalDate date
    ) throws Exception {
        googleEmailService.processMessagesReceivedOnDate(date);
        return ResponseEntity.ok("{}");
    }
}
