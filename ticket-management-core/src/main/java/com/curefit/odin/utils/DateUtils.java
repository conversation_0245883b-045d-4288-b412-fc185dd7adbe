package com.curefit.odin.utils;

import com.curefit.odin.admin.pojo.BusinessHours;
import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

import static com.curefit.odin.commons.Constants.*;

/**
 * <AUTHOR>
 */

public class DateUtils {

  private static final SimpleDateFormat DATE_FORMATTER = new SimpleDateFormat("yyyy-MM-dd");
  private static final SimpleDateFormat TIME_FORMATTER = new SimpleDateFormat("HH:mm:ss");
  public static final String UTC_DATE_STRING_PATTERN = "EEE MMM dd HH:mm:ss zzz yyyy";
  public static final ZoneId IST_ZONE = ZoneId.of("Asia/Kolkata");


  public static String getTimeString(Date date) {
    return TIME_FORMATTER.format(date);
  }

  public static Date dateDiff(Date date, int diff) {
    final Calendar cal = Calendar.getInstance();
    cal.setTime(date);
    cal.add(Calendar.DATE, diff);
    return cal.getTime();
  }

  public static String dateDiffParsed(Date date, int diff) {
    final Calendar cal = Calendar.getInstance();
    cal.setTime(date);
    cal.add(Calendar.DATE, diff);
    return DATE_FORMATTER.format(cal.getTime());
  }

  public static String yesterday() {
    final Calendar cal = Calendar.getInstance();
    cal.add(Calendar.DATE, -1);
    return DATE_FORMATTER.format(cal.getTime());
  }

  public static String today() {
    final Calendar cal = Calendar.getInstance();
    cal.add(Calendar.DATE, 0);
    return DATE_FORMATTER.format(cal.getTime());
  }

  public static String tomorrow() {
    final Calendar cal = Calendar.getInstance();
    cal.add(Calendar.DATE, 1);
    return DATE_FORMATTER.format(cal.getTime());
  }

  public static String format(Date date) {
    return DATE_FORMATTER.format(date);
  }

  // TODO : need to change variable names
  public static Long getBusinessHourTime(Long timeInMillies, int slaInMinutes, BusinessHours businessHours) {
    long totalSLAInMins = slaInMinutes;
    if (businessHours != null && StringUtils.isNotBlank(businessHours.getStartTime()) && StringUtils.isNotBlank(businessHours.getEndTime())) {
      Calendar calendar = Calendar.getInstance();
      calendar.setTimeZone(TimeZone.getTimeZone("Asia/Kolkata"));
      calendar.setTimeInMillis(timeInMillies);

      int hour = calendar.get(Calendar.HOUR_OF_DAY);
      int minute = calendar.get(Calendar.MINUTE);

      int minuteTime = hour * MINUTES_IN_HOUR + minute;

      int startTime = Integer.parseInt(businessHours.getStartTime());
      int endTime = Integer.parseInt(businessHours.getEndTime());
      int startMins = ((startTime / 100) * MINUTES_IN_HOUR) + (startTime % 100);
      int endMins = ((endTime / 100) * MINUTES_IN_HOUR) + (endTime % 100);

      // TODO: need to build for night shift as well
      if (startMins < endMins) {
        totalSLAInMins = 0;
        int totalBusinessInMins = endMins - startMins;

        if (minuteTime >= startMins && minuteTime <= endMins) {
          int endBusinessMinuteGap = endMins - minuteTime;
          if (endBusinessMinuteGap > slaInMinutes) {
            totalSLAInMins = slaInMinutes;
            slaInMinutes = 0;
          } else {
            totalSLAInMins = endBusinessMinuteGap;
            slaInMinutes = slaInMinutes - endBusinessMinuteGap;
            minuteTime += endBusinessMinuteGap;
          }
        }

        if (slaInMinutes != 0) {
          if (minuteTime <= startMins) {
            totalSLAInMins += (startMins - minuteTime);
          } else if (minuteTime >= endMins) {
            totalSLAInMins += (startMins - minuteTime) + MINUTES_IN_DAY;
          }
          // totalBusinessInMins is equivalent to 1 day
          totalSLAInMins += (slaInMinutes / totalBusinessInMins) * MINUTES_IN_DAY;
          totalSLAInMins += (slaInMinutes % totalBusinessInMins);
        }
      }
    }
    return totalSLAInMins * MILLIS_IN_MINUTE;
  }

  public static String changeToIST(String date, String pattern) throws ParseException {
    if (StringUtils.isBlank(date) || "null".equals(date)) {
      return date;
    }
    SimpleDateFormat utcFormat = new SimpleDateFormat(pattern);
    utcFormat.setTimeZone(TimeZone.getTimeZone("UTC"));

    Date utcDate = utcFormat.parse(date);

    DateFormat indianFormat = new SimpleDateFormat(pattern);
    indianFormat.setTimeZone(TimeZone.getTimeZone("Asia/Kolkata"));
    return indianFormat.format(utcDate);
  }

  public static Date getStartOfDayDateInTimezone(long currEpoch, String tz) { // tz = "UTC", "IST"
    Calendar cal = Calendar.getInstance();
    cal.setTimeZone(TimeZone.getTimeZone(tz));
    cal.setTimeInMillis(currEpoch);
    cal.set(Calendar.HOUR_OF_DAY, 0);
    cal.set(Calendar.MINUTE, 0);
    cal.set(Calendar.SECOND, 0);
    cal.set(Calendar.MILLISECOND, 0);
    return new Date(cal.getTimeInMillis());
  }

  public static Date getEndOfDayDateInTimezone(long currEpoch, String tz) {
    Calendar cal = Calendar.getInstance();
    cal.setTimeZone(TimeZone.getTimeZone(tz));
    cal.setTimeInMillis(currEpoch);
    cal.set(Calendar.HOUR_OF_DAY, 23);
    cal.set(Calendar.MINUTE, 59);
    cal.set(Calendar.SECOND, 59);
    cal.set(Calendar.MILLISECOND, 999);
    return new Date(cal.getTimeInMillis());
  }

  public static LocalDateTime getCurrentISTLocalDateTime() {
    return ZonedDateTime.now(IST_ZONE).toLocalDateTime();
  }
}
