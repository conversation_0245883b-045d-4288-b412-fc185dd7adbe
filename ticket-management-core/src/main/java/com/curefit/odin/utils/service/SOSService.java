package com.curefit.odin.utils.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.util.HeadersUtils;
import com.curefit.iris.models.ClickToCallReport;
import com.curefit.iris.models.Notification;
import com.curefit.iris.models.SendCampaignNotificationsResponseStatus;
import com.curefit.iris.services.spi.CampaignService;
import com.curefit.odin.admin.pojo.UserEntry;
import com.curefit.odin.admin.service.SLAService;
import com.curefit.odin.config.SOSConfigurations;
import com.curefit.odin.enums.Priority;
import com.curefit.odin.enums.Status;
import com.curefit.odin.integration.freshdesk.service.SOSFreshdeskTicketService;
import com.curefit.odin.user.pojo.CommentEntry;
import com.curefit.odin.user.pojo.TicketEntry;
import com.curefit.odin.user.service.CommentService;
import com.curefit.odin.user.service.TicketService;
import com.curefit.odin.utils.AsyncService;
import com.curefit.odin.utils.GoogleLocationService;
import com.curefit.odin.utils.pojo.EmployeeDetails;
import com.curefit.odin.utils.pojo.ExternalTicketResponse;
import com.curefit.odin.utils.pojo.SOSRequestEntry;
import com.curefit.odin.utils.pojo.UserInteractionEvent;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import static com.curefit.odin.commons.Constants.TICKET_ID;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SOSService {

  @Autowired
  TicketService ticketService;

  @Autowired
  CommentService commentService;

  @Autowired
  SLAService slaService;

  @Autowired
  SOSNotificationHelper sosNotificationHelper;

  @Autowired
  SOSConfigurations sosConfigurations;

  @Autowired
  EmployeeService employeeService;

  @Autowired
  RollbarService rollbarService;

  @Autowired
  CampaignService campaignService;

  @Autowired
  SOSFreshdeskTicketService sosFreshdeskTicketService;

  @Autowired
  GoogleLocationService googleLocationService;

  private final static DateFormat UTC_FORMAT = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
  private final static DateFormat IST_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");

  public TicketEntry createTicket(SOSRequestEntry sosRequestEntry, String userEmail) throws BaseException {

    log.info("SOS: User email is : {}", userEmail);
    fetchAndUpdateEmployeeDetails(sosRequestEntry, userEmail);
    TicketEntry ticketEntry = ticketService.create(buildTicketEntry(sosRequestEntry));

    log.info("SOS: ticket with id {}, created for sos for employee phone: {}", ticketEntry.getId(), sosRequestEntry.getEmployeeDetails().getPhone());
    AtomicBoolean clickToCallSent = new AtomicBoolean(true);
    if (StringUtils.isNotEmpty(sosRequestEntry.getEmployeeDetails().getPhone())) {
      SendCampaignNotificationsResponseStatus notificationsResponseStatus = sosNotificationHelper
          .sendClickToCallNotification(sosRequestEntry.getEmployeeDetails(), ticketEntry.getId());


      if (notificationsResponseStatus != null) {
        log.info("sos call to employee {} , sent status: {} ",
            sosRequestEntry.getEmployeeDetails().getPhone(), notificationsResponseStatus.getSent());
        if (notificationsResponseStatus.getSent() == null || !notificationsResponseStatus.getSent()) {
          clickToCallSent.set(false);
          rollbarService.error(new Exception("sos call failed in iris for notification id "
              + notificationsResponseStatus.getNotificationId()));
        }
      }
    }


    AsyncService.submit(() -> {
      try {
        log.debug("creating freshdesk ticket for sos for odin ticket id {}", ticketEntry.getId());
        createFreshdeskTicket(ticketEntry, sosRequestEntry.getEmployeeDetails());
        if (!clickToCallSent.get()) {
          addComment(ticketEntry.getId(), "FAILED (Internal Service Error)");
        }
      } catch (Exception e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
      }

      sosNotificationHelper.sendSMSNotification(sosRequestEntry.getTenantCode(),
          sosRequestEntry.getEmployeeDetails(), ticketEntry, clickToCallSent.get());
    });
    return ticketEntry;
  }

  private void fetchAndUpdateEmployeeDetails(SOSRequestEntry sosRequestEntry, String emailId) throws BaseException {
    EmployeeDetails neoEmployeeDetails = null;
    try {
      if (StringUtils.isNotBlank(emailId)) {
        log.info("SOS: Fetching employee details with emailID: {}", emailId);
        neoEmployeeDetails = employeeService.fetchEmployeeDetailsByEmail(sosRequestEntry.getTenantCode(), emailId);
      } else if (StringUtils.isNotBlank(sosRequestEntry.getEmployeeId())) {
        neoEmployeeDetails = employeeService.fetchEmployeeDetails(sosRequestEntry.getTenantCode(),
            sosRequestEntry.getEmployeeId());
      } else if (StringUtils.isNotBlank(sosRequestEntry.getEmployeeDetails().getPhone())) {
        neoEmployeeDetails = employeeService.fetchEmployeeDetailsByPhone(
            sosRequestEntry.getTenantCode(), sosRequestEntry.getEmployeeDetails().getPhone(),
            sosRequestEntry.getEmployeeDetails().getCountryCode());
      }
    } catch (Exception e) {
      log.error("exception from neo ", e);
    }
    log.info("SOS: Neo employee with emailID: {} is: {}", emailId, neoEmployeeDetails);

    SOSRequestEntry.EmployeeDetails employeeDetails = sosRequestEntry.getEmployeeDetails();
    if (neoEmployeeDetails != null) {
      employeeDetails.setName(neoEmployeeDetails.getEmployee().getName());
      employeeDetails.setEmail(neoEmployeeDetails.getEmployee().getEmail());
      employeeDetails.setPhone(neoEmployeeDetails.getEmployee().getMobileNumber());

      log.info("SOS: Employee details from neo: name: {}, email: {}, phone: {}",
              employeeDetails.getName(), employeeDetails.getEmail(), employeeDetails.getPhone());
      if (StringUtils.isNotBlank(neoEmployeeDetails.getJobDetails().getReportingManagerUin())) {
        try {
          EmployeeDetails managerDetails =
              employeeService.fetchEmployeeDetails(sosRequestEntry.getTenantCode(),
                  neoEmployeeDetails.getJobDetails().getReportingManagerUin());
          employeeDetails.setReportingManagerPhone(managerDetails.getEmployee().getMobileNumber());
        } catch (Exception e) {
          log.error(e.getMessage(), e);
          rollbarService.error(e);
        }
      }

      employeeDetails.setReportingManager(neoEmployeeDetails.getReportingManager());
      employeeDetails.setWorkLocation(neoEmployeeDetails.getJobDetails().getWorkLocation());
      employeeDetails
          .setDesignationName(neoEmployeeDetails.getHierarchyDetails().getDesignationName());
    } else {
      String countryCode = StringUtils.isBlank(employeeDetails.getCountryCode()) ? ""
          : employeeDetails.getCountryCode() + "-";
      employeeDetails.setPhone(countryCode + employeeDetails.getPhone());
      employeeDetails
          .setReportingManagerPhone(countryCode + employeeDetails.getReportingManagerPhone());
    }
    // if (StringConverterUtils.isBlank(sosRequestEntry.getEmployeeDetails().getPhone())) {
    // throw new BaseException("Employee phone is not present");
    // }
  }

  private void createFreshdeskTicket(TicketEntry ticketEntry,
      SOSRequestEntry.EmployeeDetails employeeDetails) throws BaseException {
    ExternalTicketResponse externalTicketResponse =
        sosFreshdeskTicketService.createTicket(ticketEntry, employeeDetails);
    ticketEntry.setDestRefId(externalTicketResponse.getId());
    TicketEntry updateEntry = new TicketEntry();
    updateEntry.setDest(externalTicketResponse.getDest());
    updateEntry.setDestRefId(externalTicketResponse.getId());
    ticketService.patchUpdate(ticketEntry.getId(), updateEntry);
  }

  public void findTicketAndUpdateStatus(UserInteractionEvent userInteractionEvent)
      throws BaseException {
    Notification notification =
        campaignService.getNotification(userInteractionEvent.getNotificationId());
    if (notification != null && notification.getUserContext() != null) {
      Long ticketId =
          Long.valueOf(((String) notification.getUserContext().getTags().get(TICKET_ID)));
      ClickToCallReport clickToCallReport =
          campaignService.getClickToCallReport(userInteractionEvent.getNotificationId());
      addComment(ticketId, clickToCallReport);
    }
  }

  private void addComment(Long ticketId, String callStatus) throws BaseException {
    CommentEntry commentEntry = new CommentEntry();
    commentEntry.setComment("Call Status : " + callStatus);
    commentEntry.setTicketId(ticketId);
    commentService.create(commentEntry);
    addFreshdeskComment(ticketId, commentEntry.getComment());
  }

  private void addComment(Long ticketId, ClickToCallReport clickToCallReport) throws BaseException {
    CommentEntry commentEntry = new CommentEntry();
    commentEntry.setComment(buildComment(clickToCallReport));
    commentEntry.setTicketId(ticketId);
    commentService.create(commentEntry);
    addFreshdeskComment(ticketId, commentEntry.getComment());
  }

  private void addFreshdeskComment(Long ticketId, String comment) throws BaseException {
    TicketEntry ticketEntry = ticketService.findOneById(ticketId);
    if (StringUtils.isNotBlank(ticketEntry.getDestRefId())) {
      sosFreshdeskTicketService.addPrivateNote(ticketEntry.getDestRefId(), comment);
    }
  }

  private TicketEntry buildTicketEntry(SOSRequestEntry sosRequestEntry) {
    TicketEntry ticketEntry = new TicketEntry();
    ticketEntry.setTitle(sosConfigurations.getTitle());
    ticketEntry.setDescription(buildDescription(sosRequestEntry));
    ticketEntry.setStatus(Status.OPEN);
    ticketEntry.setPriority(Priority.P0);
    ticketEntry.setCategoryId(sosConfigurations.getCategoryId());
    ticketEntry.setSubCategoryId(sosConfigurations.getSubCategoryId());
    ticketEntry.setTenantId(sosConfigurations.getTenantId());


    ticketEntry.setReporter(new UserEntry(HeadersUtils.getCurrentUser()));
    ticketEntry.setDueDate(new Date(slaService.getDueDate(ticketEntry.getCategoryId(),
        ticketEntry.getSubCategoryId(), ticketEntry.getPriority())));

    return ticketEntry;
  }

  private String buildDescription(SOSRequestEntry sosRequestEntry) {
    String reportingManager = sosRequestEntry.getEmployeeDetails().getReportingManager();
    reportingManager = StringUtils.isBlank(reportingManager) ? "" : reportingManager;

    Date lastLocationTime = sosRequestEntry.getLastLocationFetchTime();
    String lastLocationFetchTimeString = "";
    if (lastLocationTime != null) {
      lastLocationFetchTimeString = "Last Location Fetch Time " + lastLocationTime + "<br />";
    }

    String locationAddressString = "";
    String locationAddress = googleLocationService.fetchAddress(sosRequestEntry.getLatitude(),
        sosRequestEntry.getLongitude());
    if (!locationAddress.equals("")) {
      locationAddressString = "Address: " + locationAddress + "<br />";
    }

    return "<br /><b>Employee Details : </b><br />" + "Name: "
        + sosRequestEntry.getEmployeeDetails().getName() + "<br />" + "Email: "
        + sosRequestEntry.getEmployeeDetails().getEmail() + "<br />" + "Location Url: "
        + "https://www.google.com/maps/place/" + sosRequestEntry.getLatitude() + ","
        + sosRequestEntry.getLongitude() + "<br />" + locationAddressString
        + lastLocationFetchTimeString + "Reporting Manager: " + reportingManager + "<br />"
        + "WorkLocation: " + sosRequestEntry.getEmployeeDetails().getWorkLocation() + "<br />"
        + "Designation: " + sosRequestEntry.getEmployeeDetails().getDesignationName() + "<br />"
        + "<b>Helpline Doc: </b>" + sosConfigurations.getHelplineDocUrl();
  }

  private String buildComment(ClickToCallReport clickToCallReport) {

    // 2020-01-21T07:32:49.000Z
    UTC_FORMAT.setTimeZone(TimeZone.getTimeZone("UTC"));
    IST_FORMAT.setTimeZone(TimeZone.getTimeZone("Asia/Kolkata"));

    try {
      Date timestamp = UTC_FORMAT.parse(clickToCallReport.getStartTime());
      clickToCallReport.setStartTime(IST_FORMAT.format(timestamp));
    } catch (ParseException e) {
      log.error(e.getMessage(), e);
    }

    return "<b>Caller: </b>" + clickToCallReport.getCaller() + "<br />" + "<b>Receiver: </b>"
        + clickToCallReport.getReceiver() + "<br />" + "<b>Status: </b>"
        + clickToCallReport.getStatus() + "<br />" + "<b>Caller Status: </b>"
        + clickToCallReport.getStatusCaller() + "<br />" + "<b>Receiver Status: </b>"
        + clickToCallReport.getStatusReceiver() + "<br />" + "<b>Call Start Time (IST): </b>"
        + clickToCallReport.getStartTime() + "<br />" + "<b>Recording Path: </b>"
        + clickToCallReport.getRecordPath() + "<br />" + "<b>External Id: </b>"
        + clickToCallReport.getExternalId() + "<br />" + "<b>Duration: </b>"
        + clickToCallReport.getDuration() + " sec <br />";

  }
}
