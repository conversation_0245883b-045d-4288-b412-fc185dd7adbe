package com.curefit.odin.utils;

import com.google.api.client.googleapis.auth.oauth2.GoogleClientSecrets;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.jackson2.JacksonFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.*;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class GoogleAdminCredentials {

  @Value("${app.environment}")
  String env;

  private static final JsonFactory JSON_FACTORY = JacksonFactory.getDefaultInstance();

  public GoogleClientSecrets getClientSecrets(String account) throws IOException {
    InputStream inputStream = new ClassPathResource("/" + env + "/credentials-" + account + ".json").getInputStream();
    GoogleClientSecrets googleClientSecrets = GoogleClientSecrets.load(JSON_FACTORY, new InputStreamReader(inputStream));
    googleClientSecrets.getWeb().setClientSecret(getSystemProperty("GOOGLE_OAUTH_SECRET_" + account));

    return googleClientSecrets;
  }

  public String getSystemProperty(String key) {
    String value = System.getenv(key);
    if (StringUtils.isEmpty(value)) {
      return System.getProperty(key);
    }
    return value;
  }
}
