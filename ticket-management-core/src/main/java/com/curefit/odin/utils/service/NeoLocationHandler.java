package com.curefit.odin.utils.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.rest.client.CommonHttpHelper;
import com.curefit.odin.admin.service.NamespaceTenantsMappingService;
import com.curefit.odin.utils.pojo.LocationEntry;
import com.curefit.odin.utils.pojo.TenantEntry;
import com.jayway.jsonpath.JsonPath;
import lombok.extern.slf4j.Slf4j;

// TODO - fix me! To use NEO CLIENT
@Service
@Slf4j
@Configuration
public class NeoLocationHandler {
  
  @Autowired
  private NamespaceTenantsMappingService tenantNamespaceService;
  
  @Autowired
  private CommonHttpHelper httpHelper;
  
  @Autowired
  private LocationService locationService;
  
  
  @Value("${neo.url}")
  String baseUrl;

  public LocationEntry getLocation(String namespace, String userId) throws BaseException {
    log.info("Fetching work location with namespace {}", namespace);
    List<TenantEntry> tenants = tenantNamespaceService.getTenantByNamespace(namespace);
    Map<String, String> header = new HashMap<>();
    String employeeResponse = null;
    TenantEntry tenantEntry = null;
    LocationEntry location = null;
    String url = baseUrl + "/employee/email/" + userId;

    for (TenantEntry tenant : tenants) {
      header.put("tenant", tenant.getNeoCode());
      log.info("Hitting url {} with header {}", url, header);
      ResponseEntity<String> response = httpHelper.request(url, HttpMethod.GET, null, header);
      log.info("response {}", response);
      if (null != response && StringUtils.isNotEmpty(response.getBody())) {
        tenantEntry = tenant;
        employeeResponse = response.getBody();
        break;
      }
    }

    if (StringUtils.isNotEmpty(employeeResponse)) {
      String workLocation = JsonPath.read(employeeResponse, "$['jobDetails']['workLocation']");
      log.info("Work location obtained from neo : {}", workLocation);
      location = locationService.findByNeoCode(workLocation, tenantEntry.getId());
      log.info("Work location obtained {}", location);
    }
    return location;
  }
}
