package com.curefit.odin.utils.service;

import com.curefit.odin.admin.repositories.BlackListDLDAO;
import com.curefit.odin.utils.models.BlackListDL;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
public class BlackListDLService {

  @Autowired
  BlackListDLDAO blackListDLDAO;

  @Cacheable(value = "fetchAllBlackListDLs", unless = "#result == null")
  public Set<String> fetchAllBlackListDLs() {
    Iterable<BlackListDL> dls = blackListDLDAO.findAll();
    return ImmutableList.copyOf(dls).stream().map(BlackListDL::getDl).collect(Collectors.toSet());
  }
}
