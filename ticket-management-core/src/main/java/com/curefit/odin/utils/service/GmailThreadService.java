package com.curefit.odin.utils.service;

import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.odin.admin.repositories.GmailThreadDAO;
import com.curefit.odin.utils.models.GmailThread;
import com.curefit.odin.utils.pojo.GmailThreadEntry;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 */


@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class GmailThreadService extends BaseMySQLService<GmailThread, GmailThreadEntry> {

  public GmailThreadService(GmailThreadDAO gmailThreadDAO) {
    super(gmailThreadDAO);
  }

  public Optional<GmailThreadEntry> findByThreadId(String threadId) {
    return ((GmailThreadDAO) baseMySQLRepository).findByThreadId(threadId).map(this::convertToEntry);
  }

  public Optional<GmailThreadEntry> findByTicketId(String ticketId) {
    return ((GmailThreadDAO) baseMySQLRepository).findByTicketId(ticketId).map(this::convertToEntry);
  }
}