package com.curefit.odin.utils;

import com.curefit.odin.enums.RashiEventType;
import com.curefit.odin.sprinklr.pojo.SprinklrTicketEntry;
import com.curefit.odin.utils.models.Status;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

public class SprinklrUtils {
    public static Status getClosedStatus() {
        return new Status("CLOSED", "#000000");
    }

    public static Status getResolvedStatus() {
        return new Status("RESOLVED", "#60636b");
    }

    public static Status getOpenStatus() {
        return new Status("OPEN", "#57AC49");
    }

    public static Status getTicketStatus(SprinklrTicketEntry sprinklrTicketEntry) {
        return switch (sprinklrTicketEntry.getStatus()) {
            case "Hard Closed" -> getClosedStatus();
            case "Resolved", "Auto-Closed", "Closed", "Auto Closed" -> getResolvedStatus();
            case null, default -> getOpenStatus();
        };
    }

    public static RashiEventType getRashiEventType(String status) {
        return switch (status) {
            case "RESOLVED" -> RashiEventType.SPRINKLR_TICKET_RESOLVED;
            case "CLOSED" -> RashiEventType.SPRINKLR_TICKET_HARD_CLOSED;
            default -> null;
        };
    }

    public static String parseMessageText(String text) {
        String message = parseHtml(text);
        return removeDisclaimerFromMessage(message);
    }

    public static String parseHtml(String htmlString) {
        Document doc = Jsoup.parse(htmlString);
        doc.select("span[style*='display:none'], span[style*='font-size: 0px']").remove();
        doc.select("div.gmail_signature, div.gmail_quote.gmail_quote_container").remove();
        return doc.body().wholeText();
    }

    public static String removeDisclaimerFromMessage(String message) {
        return message.contains("Disclaimer") ? message.substring(0, message.indexOf("Disclaimer")).trim() : message;
    }
}
