package com.curefit.odin.utils.service;

import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.odin.admin.repositories.GmailMessageDAO;
import com.curefit.odin.utils.models.GmailMessage;
import com.curefit.odin.utils.pojo.GmailMessageEntry;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class GmailMessageService extends BaseMySQLService<GmailMessage, GmailMessageEntry> {

  public GmailMessageService(GmailMessageDAO gmailMessageDAO) {
    super(gmailMessageDAO);
  }

  @Cacheable(value = "findByMessageId", unless = "#result == null")
  public Optional<GmailMessageEntry> findByMessageId(String messageId) {
    return ((GmailMessageDAO) baseMySQLRepository).findByMessageId(messageId).map(this::convertToEntry);
  }

  public Set<String> getUnprocessedMessageIds(Collection<String> messageIds) {
    List<GmailMessage> existingMessages = ((GmailMessageDAO) baseMySQLRepository).findByMessageIdIn(messageIds);
    Set<String> existingMessageIds = existingMessages.stream()
            .map(GmailMessage::getMessageId)
            .collect(Collectors.toSet());
    return messageIds.stream()
            .filter(id -> !existingMessageIds.contains(id))
            .collect(Collectors.toSet());
  }
}
