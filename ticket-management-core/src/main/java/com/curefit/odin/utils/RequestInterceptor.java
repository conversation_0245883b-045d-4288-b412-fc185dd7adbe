package com.curefit.odin.utils;

import com.curefit.common.data.enums.AppStatus;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.enums.LogType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */

@Slf4j
public class RequestInterceptor extends HandlerInterceptorAdapter {

  @Override
  public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

    if (StringUtils.isBlank(request.getHeader("X_USER_ID"))) {
      log.error("X_USER_ID header is missing/empty in request");
      throw new BaseException("X_USER_ID header is missing/empty in request", AppStatus.NOT_AUTHENTICATED, LogType.WARNING);
    }
    if (StringUtils.isBlank(request.getHeader("X_NAMESPACE"))) {
      log.error("X_NAMESPACE header is missing/empty in request");
      throw new BaseException("X_NAMESPACE header is missing/empty in request", AppStatus.NOT_AUTHENTICATED, LogType.WARNING);
    }

    return super.preHandle(request, response, handler);
  }
}
