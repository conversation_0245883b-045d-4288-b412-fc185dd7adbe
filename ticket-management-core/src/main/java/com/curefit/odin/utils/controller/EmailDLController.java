package com.curefit.odin.utils.controller;

import com.curefit.odin.admin.controllers.BaseOdinController;
import com.curefit.odin.admin.pojo.UserEntry;
import com.curefit.odin.scheduledTasks.EmailDLUpdater;
import com.curefit.odin.utils.models.EmailDL;
import com.curefit.odin.utils.pojo.EmailDLEntry;
import com.curefit.odin.utils.service.EmailDLService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */

@RestController
@RequestMapping("/email_dl")
public class EmailDLController extends BaseOdinController<EmailDL, EmailDLEntry> {

  @Autowired
  EmailDLUpdater emailDLUpdater;

  public EmailDLController(EmailDLService emailDLService) {
    super(emailDLService);
  }

  @GetMapping
  public ResponseEntity<List<UserEntry>> fetchByDL(@RequestParam String dl) {
    return new ResponseEntity<>(((EmailDLService) baseMySQLService).getUserByDL(dl), HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET, value = "/refresh_dls")
  public ResponseEntity<?> refreshDLs() {
    emailDLUpdater.refreshEmailDLs();
    return new ResponseEntity<>(HttpStatus.OK);
  }
}
