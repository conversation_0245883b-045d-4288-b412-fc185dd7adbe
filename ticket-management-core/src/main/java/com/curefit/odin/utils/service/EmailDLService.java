package com.curefit.odin.utils.service;

import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.odin.admin.pojo.UserEntry;
import com.curefit.odin.admin.repositories.EmailDLDAO;
import com.curefit.odin.admin.service.UserService;
import com.curefit.odin.utils.models.EmailDL;
import com.curefit.odin.utils.pojo.EmailDLEntry;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class EmailDLService extends BaseMySQLService<EmailDL, EmailDLEntry> {

  @Autowired
  UserService userService;

  public EmailDLService(EmailDLDAO emailDLDAO) {
    super(emailDLDAO);
  }

  @Cacheable(value = "getDLsByEmail", unless = "#result == null")
  public List<String> getDLsByEmail(String email) {
    return ((EmailDLDAO) baseMySQLRepository).findByEmail(email).stream()
            .map(EmailDL::getDl)
            .collect(Collectors.toList());
  }

  @Cacheable(value = "getUserByDL", unless = "#result == null")
  public List<UserEntry> getUserByDL(String dl) {
    return ((EmailDLDAO) baseMySQLRepository).findByDl(dl).stream()
            .map(emailDL -> userService.findUserByMailIdWithDefault(emailDL.getEmail()))
            .collect(Collectors.toList());
  }

  @Cacheable(value = "getEmailsByDL", unless = "#result == null")
  public List<String> getEmailsByDL(String dl) {
    return ((EmailDLDAO) baseMySQLRepository).findByDl(dl).stream()
            .map(EmailDL::getEmail)
            .collect(Collectors.toList());
  }

  public List<EmailDL> fetchByDL(String dl) {
    return ((EmailDLDAO) baseMySQLRepository).findByDl(dl);
  }

  public void deleteAll(List<EmailDL> emailDLS) {
    baseMySQLRepository.deleteAll(emailDLS);
  }
}
