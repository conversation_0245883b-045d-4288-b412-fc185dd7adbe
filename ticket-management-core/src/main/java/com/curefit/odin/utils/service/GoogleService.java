package com.curefit.odin.utils.service;

import com.curefit.odin.config.OdinConfigurations;
import com.curefit.odin.utils.GoogleAdminCredentials;
import com.curefit.odin.utils.GoogleOAuthVerificationCodeReceiver;
import com.curefit.odin.utils.S3Utils;
import com.google.api.client.auth.oauth2.AuthorizationCodeRequestUrl;
import com.google.api.client.auth.oauth2.Credential;
import com.google.api.client.auth.oauth2.StoredCredential;
import com.google.api.client.extensions.java6.auth.oauth2.AuthorizationCodeInstalledApp;
import com.google.api.client.googleapis.auth.oauth2.GoogleAuthorizationCodeFlow;
import com.google.api.client.googleapis.auth.oauth2.GoogleClientSecrets;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.jackson2.JacksonFactory;
import com.google.api.client.util.store.FileDataStoreFactory;
import com.google.api.services.admin.directory.Directory;
import com.google.api.services.gmail.Gmail;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.*;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public abstract class GoogleService {

  private static final String APPLICATION_NAME = "Ticketing System Google Admin SDK Directory";
  private static final JsonFactory JSON_FACTORY = JacksonFactory.getDefaultInstance();

  @Autowired
  GoogleAdminCredentials googleAdminCredentials;

  @Autowired
  GoogleOAuthVerificationCodeReceiver receiver;

  @Autowired
  OdinConfigurations odinConfigurations;

  @Autowired
  S3Utils s3Utils;

  public Directory getDirectoryService(String accountName) throws IOException, GeneralSecurityException {
    final NetHttpTransport HTTP_TRANSPORT = GoogleNetHttpTransport.newTrustedTransport();
    return new Directory.Builder(HTTP_TRANSPORT, JSON_FACTORY, getCredentials(accountName, HTTP_TRANSPORT))
              .setApplicationName(APPLICATION_NAME)
              .build();
  }

  public Gmail getGmailService(String accountName) throws IOException, GeneralSecurityException {
    final NetHttpTransport HTTP_TRANSPORT = GoogleNetHttpTransport.newTrustedTransport();
    return new Gmail.Builder(HTTP_TRANSPORT, JSON_FACTORY, getCredentials(accountName, HTTP_TRANSPORT))
              .setApplicationName(APPLICATION_NAME)
              .build();
  }

  private Credential getCredentials(final String accountName, final NetHttpTransport HTTP_TRANSPORT) throws IOException {
    GoogleClientSecrets clientSecrets = googleAdminCredentials.getClientSecrets(accountName);

    File authDirectory = new File(odinConfigurations.getOauthTokenDirectory() + "_" + accountName);

    String dataStoreId = StoredCredential.DEFAULT_DATA_STORE_ID;
    File oAuthCredFile = new File(authDirectory, dataStoreId);

    String authFileS3Key = String.format("%s_%s", dataStoreId, accountName);

    boolean isS3UploadRequired = false;
    if (!oAuthCredFile.exists()) {
      isS3UploadRequired = !s3Utils.downloadFile(authFileS3Key, oAuthCredFile);
    }

    GoogleAuthorizationCodeFlow flow = new GoogleAuthorizationCodeFlow.Builder(
            HTTP_TRANSPORT, JSON_FACTORY, clientSecrets, getScopes())
            .setDataStoreFactory(new FileDataStoreFactory(authDirectory))
            .setAccessType("offline")
            .setApprovalPrompt("force")
            .build();

    AuthorizationCodeInstalledApp authorizationCodeInstalledApp = new AuthorizationCodeInstalledApp(flow, receiver);
    AuthorizationCodeRequestUrl authorizationUrl =
            authorizationCodeInstalledApp.getFlow().newAuthorizationUrl().setRedirectUri(authorizationCodeInstalledApp.getReceiver().getRedirectUri());
    log.info("Google OAuth Token Url {}", authorizationUrl);
    receiver.setAuthorizationUrl(String.valueOf(authorizationUrl));
    receiver.setAccountName(accountName);

    Credential credential = authorizationCodeInstalledApp.authorize("user");
    if (isS3UploadRequired) {
      s3Utils.uploadFile(authFileS3Key, oAuthCredFile);
    }
    return credential;
  }

  public abstract List<String> getScopes();
}
