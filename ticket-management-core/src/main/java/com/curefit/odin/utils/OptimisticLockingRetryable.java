package com.curefit.odin.utils;

import org.hibernate.StaleObjectStateException;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.orm.hibernate5.HibernateOptimisticLockingFailureException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Custom annotation for standardized retry configuration for optimistic locking exceptions.
 * This annotation can be applied to methods that need to handle optimistic locking failures
 * with a consistent retry policy.
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Retryable(value = {
        ObjectOptimisticLockingFailureException.class,
        StaleObjectStateException.class,
        HibernateOptimisticLockingFailureException.class
}, maxAttempts = 3, backoff = @Backoff(delay = 500))
public @interface OptimisticLockingRetryable {
}