package com.curefit.odin.utils.service;

import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.odin.admin.repositories.EmailTicketConfigDAO;
import com.curefit.odin.utils.models.EmailTicketConfig;
import com.curefit.odin.utils.pojo.EmailTicketConfigEntry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
public class EmailTicketConfigService extends BaseMySQLService<EmailTicketConfig, EmailTicketConfigEntry> {

  public EmailTicketConfigService(EmailTicketConfigDAO emailTicketConfigDAO) {
    super(emailTicketConfigDAO);
  }

  public Optional<EmailTicketConfigEntry> findByEmail(String email) {
    return ((EmailTicketConfigDAO) baseMySQLRepository).findByEmail(email)
            .map(this::convertToEntry);
  }

  public Optional<EmailTicketConfigEntry> findByAliasEmail(String email) {
    return ((EmailTicketConfigDAO) baseMySQLRepository).findByAliasEmail(email)
            .map(this::convertToEntry);
  }
}
