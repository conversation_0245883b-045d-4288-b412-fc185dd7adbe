package com.curefit.odin.utils;

import org.springframework.stereotype.Component;

import java.util.regex.Pattern;

@Component
public class EmailValidator {
    private static final String EMAIL_VALIDATION_REGEX = "^(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$"; // Pattern from Iris
    private static Pattern pattern;

    public EmailValidator() {
        pattern = Pattern.compile(EMAIL_VALIDATION_REGEX);
    }

    public boolean isValid(String email) {
        return pattern.matcher(email).matches();
    }
}
