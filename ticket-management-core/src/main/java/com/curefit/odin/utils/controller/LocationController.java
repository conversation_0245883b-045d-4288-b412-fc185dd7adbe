package com.curefit.odin.utils.controller;

import java.util.List;

import com.curefit.odin.utils.pojo.LocationHierarchyNode;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.curefit.common.data.exception.BaseException;
import com.curefit.odin.admin.controllers.BaseOdinController;
import com.curefit.odin.utils.models.Location;
import com.curefit.odin.utils.pojo.LocationEntry;
import com.curefit.odin.utils.service.LocationService;

@RestController
@RequestMapping("/location")
public class LocationController extends BaseOdinController<Location, LocationEntry> {

  public LocationController(LocationService locationService) {
    super(locationService);
  }

  @RequestMapping(method = RequestMethod.GET)
  public ResponseEntity<List<LocationEntry>> fetchForTenant(@RequestParam Long tenantId)
          throws BaseException {
    return new ResponseEntity<>(
            ((LocationService) baseMySQLService).getAllAuthorizedCentres(tenantId), HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET, value = "/tree")
  public ResponseEntity<LocationHierarchyNode> fetchTreeForTenant(@RequestParam Long tenantId) throws BaseException {
    return new ResponseEntity<>(((LocationService) baseMySQLService).fetchLocationTreeByTenantId(tenantId), HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET, value = "/tree/flat")
  public ResponseEntity<List<LocationHierarchyNode>> fetchFlatTreeForTenant(@RequestParam Long tenantId) throws BaseException {
    return new ResponseEntity<>(((LocationService) baseMySQLService).fetchFlatLocationTreeByTenantId(tenantId), HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET, value = "/cities")
  public ResponseEntity<List<LocationEntry>> fetchCitiesForTenant(@RequestParam Long tenantId)
          throws BaseException {
    return new ResponseEntity<>(
            ((LocationService) baseMySQLService).getAllCitiesByTenantId(tenantId), HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET, value = "/fetch")
  public ResponseEntity<LocationEntry> fetchByReferenceId(@RequestParam String referenceId, @RequestParam Long tenantId)
          throws BaseException {
    return new ResponseEntity<>(
            ((LocationService) baseMySQLService).findByReferenceId(referenceId, tenantId), HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET, value = "/locationEntry")
  public ResponseEntity<LocationEntry> fetchByIdentityId(@RequestParam Long identityId, @RequestParam Long tenantId, @RequestParam String osName, @RequestParam String appVersion)
          throws BaseException {
    return new ResponseEntity<>(
            ((LocationService) baseMySQLService).fetchByIdentityId(identityId, tenantId, osName, appVersion), HttpStatus.OK);
  }
}
