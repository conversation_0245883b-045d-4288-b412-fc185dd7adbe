package com.curefit.odin.utils.models;

import com.curefit.odin.admin.models.BaseMySQLModel;
import com.curefit.odin.utils.pojo.ReleaseChange;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;
import org.hibernate.annotations.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */

@Setter
@Getter
@Entity
@ToString
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "release_history")
public class ReleaseHistory  extends BaseMySQLModel {

  @Column(name = "release_version")
  Float releaseVersion;

  @Column(name = "release_date")
  Date releaseDate;

  @Column(name = "is_upcoming")
  Boolean isUpComing;

  @Type(type = "json")
  @Column(columnDefinition = "json", name = "release_changes")
  List<ReleaseChange> releaseChanges;
}
