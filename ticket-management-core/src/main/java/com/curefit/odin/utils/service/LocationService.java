package com.curefit.odin.utils.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.ResourceNotFoundException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.repositories.LocationDAO;
import com.curefit.odin.enums.AuthType;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import com.curefit.odin.utils.AuthService;
import com.curefit.odin.utils.models.Location;
import com.curefit.odin.utils.pojo.LocationEntry;
import com.curefit.odin.utils.pojo.LocationHierarchyNode;
import com.curefit.odin.utils.service.external.CultApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.curefit.odin.enums.LocationType.ALL;

@Service
@Slf4j
public class LocationService extends BaseMySQLService<Location, LocationEntry> {

    @Autowired
    RollbarService rollbarService;

    @Autowired
    LocationSourceService locationSourceService;

    @Autowired
    LocationHierarchyService locationHierarchyService;

    @Autowired
    AuthService authService;

    @Autowired
    CultApiService cultApiService;

    public LocationService(LocationDAO dataSourceValueDao) {
        super(dataSourceValueDao);
    }

    @Override
    public Location convertToEntity(LocationEntry entry) {
        Location convertedEntity = super.convertToEntity(entry);
        try {
            convertedEntity.setLocationSource(locationSourceService.fetchEntityById(entry.getDataSourceId()));
        } catch (BaseException e) {
            log.error(e.getMessage(), e);
            rollbarService.error(e);
        }
        return convertedEntity;
    }

    @Override
    public LocationEntry convertToEntry(Location entity) {
        LocationEntry convertedEntry = super.convertToEntry(entity);
        if (entity.getLocationSource().getTenant() != null) {
            convertedEntry.setTenantId(entity.getLocationSource().getTenant().getId());
        }
        convertedEntry.setDataSourceId(entity.getLocationSource().getId());
        return convertedEntry;
    }

    //No RBAC
    @Cacheable(value = "fetchAllForCity", unless = "#result == null")
    public List<LocationEntry> fetchAllForCity(Long tenantId, String cityId) {
        try {
            return getAllCentres(tenantId, "city.id.eq:" + cityId);
        } catch (BaseException e) {
            log.error(e.getMessage(), e);
            rollbarService.error(e);
            return Collections.emptyList();
        }
    }

    //No RBAC
    @Cacheable(value = "allLocationDataSourceValues", unless = "#result == null")
    public List<LocationEntry> getAllByDataSourceId(Long dataSourceId) throws BaseException {
        log.info("Getting values for data source id {}", dataSourceId);
        try {
            return search(0, -1, null, null, "locationSource.id.eq:" + dataSourceId)
                    .getElements();
        } catch (InvalidSeachQueryException e) {
            log.error(e.getMessage(), e);
            rollbarService.error(e);
            throw new BaseException(e.getMessage());
        }
    }

    // RBAC
    public LocationHierarchyNode fetchLocationTreeByTenantId(Long tenantId) throws BaseException {
        List<LocationEntry> locationEntries = getAllAuthorizedCentres(tenantId);
        return locationHierarchyService.getLocationHierarchyTree(locationEntries);
    }

    // RBAC
    public List<LocationHierarchyNode> fetchFlatLocationTreeByTenantId(Long tenantId) throws BaseException {
        List<LocationEntry> locationEntries = getAllAuthorizedCentres(tenantId);
        return locationHierarchyService.getFlatLocationHierarchy(locationEntries);
    }

    public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> map = new ConcurrentHashMap<>();
        return t -> map.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    // RBAC
    public List<LocationEntry> getAllCitiesByTenantId(Long tenantId) throws BaseException {
        log.info("Getting values for tenant id {}", tenantId);
        List<LocationEntry> locations = getAllAuthorizedCentres(tenantId);
        return locations.stream()
                .filter(distinctByKey(LocationEntry::getCityId))
                .collect(Collectors.toList());
    }

    // No RBAC
    public List<LocationEntry> getAllCentresByClusterId(Long tenantId, String clusterId) throws BaseException {
        return getAllCentres(tenantId, "clusterId.eq:" + clusterId);
    }

    // No RBAC
    public List<LocationEntry> getAllCentresByCityId(Long tenantId, String cityId) throws BaseException {
        return getAllCentres(tenantId, "cityId.eq:" + cityId);
    }

    // No RBAC
    public List<LocationEntry> getAllCentresByCountryId(Long tenantId, String countryId) throws BaseException {
        return getAllCentres(tenantId, "countryId.eq:" + countryId);
    }

    // RBAC
    public LocationEntry findByNeoCode(String neoCode, Long tenantId) throws BaseException {
        return getCentre(tenantId, "neoCode.eq:" + neoCode);
    }

    // RBAC
    public LocationEntry findByReferenceId(String referenceId, Long tenantId) throws BaseException {
        return getCentre(tenantId, "referenceId.eq:" + referenceId);
    }

    // RBAC
    public LocationEntry fetchByIdentityId(Long identityId, Long tenantId, String osName, String appVersion) throws BaseException {
        log.info("request to fetch locationEntry based on identityId :: {} and tenantId :: {}", identityId, tenantId);
        String homeCenterId = cultApiService.getHomeCenterId(identityId, osName, appVersion).getHomeCenterId();
        return findByReferenceId(homeCenterId, tenantId);
    }

    // No RBAC
    @Cacheable(value = "findByReferenceIds", unless = "#result == null")
    public List<LocationEntry> findByReferenceIds(Set<String> referenceIds, Long tenantId) throws BaseException {
        return getAllCentres(tenantId, "referenceId.in:" + StringUtils.join(referenceIds, ","));
    }

    // RBAC
    public List<LocationEntry> getAllAuthorizedCentres(Long tenantId) throws BaseException {
        return getAllAuthorizedCentres(tenantId, "");
    }

    // RBAC
    private LocationEntry getCentre(Long tenantId, String query) throws BaseException {
        List<LocationEntry> locationEntries = getAllAuthorizedCentres(tenantId, query);
        if (locationEntries.size() > 0) {
            return locationEntries.get(0);
        }
        throw new ResourceNotFoundException("Location Not found");
    }

    // RBAC
    private List<LocationEntry> getAllAuthorizedCentres(Long tenantId, String query) throws BaseException {
        query = appendAuthorizedLocations(tenantId, query);
        try {
            return search(0, -1, null, null, "locationSource.tenant.id.eq:" + tenantId + ";type.eq:CENTRE;active.eq:true;" + query)
                    .getElements();
        } catch (InvalidSeachQueryException e) {
            throw new BaseException(e.getMessage());
        }
    }

    // No RBAC
    private List<LocationEntry> getAllCentres(Long tenantId, String query) throws BaseException {
        try {
            return search(0, -1, null, null, "locationSource.tenant.id.eq:" + tenantId + ";type.eq:CENTRE;active.eq:true;" + query)
                    .getElements();
        } catch (InvalidSeachQueryException e) {
            throw new BaseException(e.getMessage());
        }
    }

    public LocationEntry getByCenterServiceIdAndTenant(String centerServiceRefId, Long tenantId) throws BaseException {
        try {
            List<LocationEntry> locationEntries = search(0, -1, null, null, "locationSource.tenant.id.eq:" + tenantId + ";type.eq:CENTRE;active.eq:true;centerServiceRefId.eq:" + centerServiceRefId)
                    .getElements();
            return !locationEntries.isEmpty() ? locationEntries.get(0) : null;
        } catch (InvalidSeachQueryException e) {
            throw new BaseException(e.getMessage());
        }
    }

    private String appendAuthorizedLocations(Long tenantId, String query) {
        List<BaseOdinEntry> contexts = authService.getContexts(AuthType.LOCATION);
        Set<String> centreIds = authService.filterTenantContexts(tenantId, contexts, AuthType.LOCATION);
        if (!centreIds.isEmpty()) {
            query = (StringUtils.isEmpty(query) ? query : query + ";") + "referenceId.in:" + StringUtils.join(centreIds, ",");
        }
        return query;
    }

    public boolean doesHierarchyContainAllLocations(List<LocationHierarchyNode> locationNodes) {
        return locationNodes.stream().anyMatch(node -> node.getType().equals(ALL));
    }

    // No RBAC
    @Cacheable(value = "getCenterLocations", unless = "#result == null")
    public List<String> getCenterLocations(Long tenantId, List<LocationHierarchyNode> locationNodes) {
        List<String> locationIds = new ArrayList<>();
        for (LocationHierarchyNode locationNode : locationNodes) {
            try {
                String locationCode = locationHierarchyService.getCode(locationNode.getCode());
                switch (locationNode.getType()) {
                    case COUNTRY:
                        locationIds.addAll(getLocationIds(getAllCentresByCountryId(tenantId, locationCode)));
                        break;
                    case CITY:
                        locationIds.addAll(getLocationIds(getAllCentresByCityId(tenantId, locationCode)));
                        break;
                    case CLUSTER:
                        locationIds.addAll(getLocationIds(getAllCentresByClusterId(tenantId, locationCode)));
                        break;
                    case CENTRE:
                        locationIds.add(locationCode);
                        break;
                }
            } catch (BaseException e) {
                log.error(e.getMessage(), e);
                rollbarService.error(e);
            }
        }
        return locationIds;
    }

    private List<String> getLocationIds(List<LocationEntry> locationEntries) {
        return locationEntries.stream().map(locationEntry -> String.valueOf(locationEntry.getId())).collect(Collectors.toList());
    }
}
