package com.curefit.odin.utils.listener;

import com.amazonaws.regions.Regions;
import com.amazonaws.services.sqs.model.Message;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.consumer.BaseSQSConsumer;
import com.curefit.odin.config.SOSConfigurations;
import com.curefit.odin.utils.pojo.UserInteractionEvent;
import com.curefit.odin.utils.service.SOSService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
@Component
@Profile("!local & !alpha")
public class SOSCallEventsListener extends BaseSQSConsumer {

  @Autowired
  ObjectMapper objectMapper;

  @Autowired
  RollbarService rollbarService;

  @Autowired
  SOSService sosService;

  public SOSCallEventsListener(SOSConfigurations sosConfigurations) {
    super(sosConfigurations.getCallEventsQueue(), Regions.AP_SOUTH_1,
            sosConfigurations.getCallEventsQueueWaitTimeInSec(), sosConfigurations.getCallEventsQueueBatchSize());
  }

  @Override
  public List<Boolean> process(List<Message> messages) {
    return messages.stream().map(message -> {
      try {
        log.info("message for sos call event : {} ", message.getBody());
        UserInteractionEvent userInteractionEvent = objectMapper.readValue(message.getBody(), UserInteractionEvent.class);
        sosService.findTicketAndUpdateStatus(userInteractionEvent);
        return true;
      } catch (Exception e) {
        rollbarService.error(e);
        log.error(e.getMessage(), e);
        return false;
      }
    }).collect(Collectors.toList());
  }
}
