package com.curefit.odin.utils.controller;

import com.curefit.odin.utils.GoogleOAuthVerificationCodeReceiver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

/**
 * <AUTHOR>
 */

@RestController
@RequestMapping("/google/oauth")
public class GoogleOAuthController {

  @Autowired
  GoogleOAuthVerificationCodeReceiver receiver;

  @Value("${google.oauth.redirect.url}")
  String redirectUrl;


  @RequestMapping(method = RequestMethod.GET, value = "/callback")
  public ModelAndView callback(@RequestParam(required = false) String code, @RequestParam(required = false) String error) {
    receiver.receiveCodeAndError(code, error);
    return new ModelAndView("redirect:" + redirectUrl);
  }
}
