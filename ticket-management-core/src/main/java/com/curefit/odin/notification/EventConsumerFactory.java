package com.curefit.odin.notification;

import com.curefit.cf.commons.pojo.audit.ChangeEvent;
import com.curefit.odin.enums.NotificationFormat;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.Map;
import java.util.function.Consumer;

import static com.curefit.odin.enums.NotificationFormat.*;

/**
 * <AUTHOR>
 */

@Component
@FieldDefaults(level = AccessLevel.PRIVATE)
public class EventConsumerFactory {

  @Autowired
  NotificationHandler notificationHandler;

  private Map<NotificationFormat, Consumer<ChangeEvent>> notificationFormatConsumerMap = new EnumMap<NotificationFormat, Consumer<ChangeEvent>>(NotificationFormat.class) {{
    put(TICKET_ASSIGN, event -> notificationHandler.handleAssigneeUpdate(event));
    put(TICKET_UPDATE, event -> notificationHandler.handleTicketUpdate(event));
    put(TICKET_COMMENT, event -> notificationHandler.handleTicketComment(event));
    put(TICKET_ATTACHMENT, event -> notificationHandler.handleAttachment(event));
  }};

  public Consumer<ChangeEvent> getEventConsumer(NotificationFormat notificationFormat) {
    return notificationFormatConsumerMap.get(notificationFormat);
  }
}
