package com.curefit.odin.notification;

import com.amazonaws.regions.Regions;
import com.amazonaws.services.sqs.model.Message;
import com.curefit.cf.commons.pojo.audit.ChangeEvent;
import com.curefit.cf.commons.pojo.audit.EventType;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.consumer.BaseSQSConsumer;
import com.curefit.odin.ChangeEventConfiguration;
import com.curefit.odin.user.models.Attachment;
import com.curefit.odin.user.models.Comment;
import com.curefit.odin.user.models.Ticket;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.curefit.odin.enums.NotificationFormat.*;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
@Profile("!local & !alpha")
public class NotificationEventsListener extends BaseSQSConsumer {

  @Qualifier(value = "odinObjectMapper")
  @Autowired
  ObjectMapper objectMapper;

  @Autowired
  RollbarService rollbarService;

  @Autowired
  EventConsumerFactory eventConsumerFactory;

  public NotificationEventsListener(ChangeEventConfiguration changeEventConfiguration) {
    super(changeEventConfiguration.getNotificationEventsQueue(), Regions.AP_SOUTH_1,
            changeEventConfiguration.getNotificationEventsQueueWaitTimeInSec(), changeEventConfiguration.getNotificationEventsQueueBatchSize());
  }

  @Override
  public List<Boolean> process(List<Message> messages) {
    return messages.stream().map(message -> {
      log.info("message for notification : {} ", message.getBody());
      try {
        ChangeEvent changeEvent = objectMapper.readValue(message.getBody(), ChangeEvent.class);

        String changeEntity = changeEvent.getChangeLog().getEntity();
        EventType eventType = changeEvent.getChangeLog().getEventType();

        Consumer<ChangeEvent> changeEventConsumer;
        if (changeEntity.equalsIgnoreCase(Ticket.class.getSimpleName())) {
          if (eventType.equals(EventType.UPDATE)) {
            changeEventConsumer = eventConsumerFactory.getEventConsumer(TICKET_UPDATE);
          } else if (eventType.equals(EventType.CREATE)) {
            changeEventConsumer = eventConsumerFactory.getEventConsumer(TICKET_ASSIGN);
          } else {
            log.warn("eating event");
            return true;
          }
        } else if (changeEntity.equalsIgnoreCase(Comment.class.getSimpleName())) {
          changeEventConsumer = eventConsumerFactory.getEventConsumer(TICKET_COMMENT);
        } else if (changeEntity.equalsIgnoreCase(Attachment.class.getSimpleName())) {
          changeEventConsumer = eventConsumerFactory.getEventConsumer(TICKET_ATTACHMENT);
        } else {
          log.warn("eating event");
          return true;
        }
        changeEventConsumer.accept(changeEvent);

        return true;

      } catch (Exception e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
      }
      return false;
    }).collect(Collectors.toList());
  }
}
