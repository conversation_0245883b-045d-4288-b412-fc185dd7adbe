package com.curefit.odin.notification;

import com.curefit.iris.models.UserContext;
import com.curefit.odin.enums.NotificationFormat;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Slf4j
@Component(value = "emailHelper")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class EmailHelper extends NotificationHelper {

  @Value("${email.ticket.assign.creative}")
  String ticketAssignCreativeId;

  @Value("${email.ticket.update.creative}")
  String ticketUpdateCreativeId;

  @Value("${email.ticket.comment.creative}")
  String ticketCommentCreativeId;

  @Value("${email.ticket.attachment.creative}")
  String ticketAttachmentCreativeId;

  @Value("${email.ticket.escalation.creative}")
  String ticketEscalationCreativeId;

  @Value("${email.ticket.slaReminder.creative}")
  String ticketSLAReminderCreativeId;

  @Value("${email.ticket.slaToBeBreachedTomorrow.slaReminder.creative}")
  String ticketSLAToBeBreachedTomorrowSLAReminderCreativeId;


  @Override
  public List<UserContext> getUserContexts(List<String> emails) {
    return emails.stream().map(email -> {
      UserContext userContext = new UserContext();
      userContext.setEmailId(email);
      return userContext;
    }).collect(Collectors.toList());
  }

  @Override
  public String getCreative(NotificationFormat notificationFormat) {
    switch (notificationFormat) {
      case TICKET_ASSIGN:
        return ticketAssignCreativeId;
      case TICKET_UPDATE:
        return ticketUpdateCreativeId;
      case TICKET_COMMENT:
        return ticketCommentCreativeId;
      case TICKET_ATTACHMENT:
        return ticketAttachmentCreativeId;
      case TICKET_ESCALATION:
        return ticketEscalationCreativeId;
      case TICKET_SLA_REMINDER:
        return ticketSLAReminderCreativeId;
      case TICKET_SLA_TO_BE_BREACHED_TOMORROW_SLA_REMINDER:
        return ticketSLAToBeBreachedTomorrowSLAReminderCreativeId;
    }
    return null;
  }
}
