package com.curefit.odin.notification;

import com.curefit.iris.models.UserContext;
import com.curefit.odin.admin.pojo.UserEntry;
import com.curefit.odin.enums.NotificationFormat;
import com.curefit.odin.utils.DateUtils;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

import static com.curefit.odin.utils.DateUtils.UTC_DATE_STRING_PATTERN;

/**
 * <AUTHOR>
 */

@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public abstract class NotificationHelper {

  @Autowired
  NotificationService notificationService;

  @Value("${email.ticket.notification.campaign}")
  String notificationCampaign;

  public UserEntry fetchUserDetail(String email) {
    return new UserEntry(email, email);
  }

  public void sendNotification(NotificationFormat notificationFormat, List<String> emails, Map<String, Object> tags) {
    List<UserContext> userContexts = getUserContexts(emails);
    String creativeId = getCreative(notificationFormat);
    notificationService.sendNotification(notificationCampaign, creativeId, userContexts, tags);
  }

  public void sendNotification(NotificationFormat notificationFormat, List<String> emails, Map<String, Object> tags, List<String> dateKeys) {
    if (dateKeys != null) {
      dateKeys.forEach(dateKey -> {
        if (tags.containsKey(dateKey)) {
          String date = (String) tags.get(dateKey);
            try {
              String istDate = DateUtils.changeToIST(date, UTC_DATE_STRING_PATTERN);
              tags.put(dateKey, istDate);
            } catch (ParseException e) {
              log.error(e.getMessage(), e);
            }
        }
      });
    }
    sendNotification(notificationFormat, emails, tags);
  }


  public abstract List<UserContext> getUserContexts(List<String> emails);

  public abstract String getCreative(NotificationFormat notificationFormat);
}
