package com.curefit.odin.notification;

import com.curefit.cf.commons.pojo.audit.Change;
import com.curefit.cf.commons.pojo.audit.ChangeEvent;
import com.curefit.cf.commons.pojo.audit.EventType;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.crypto.DecryptionUtils;
import com.curefit.odin.admin.pojo.UserEntry;
import com.curefit.odin.admin.service.NamespaceConfigService;
import com.curefit.odin.admin.service.NamespaceTenantsMappingService;
import com.curefit.odin.admin.service.UserService;
import com.curefit.odin.enums.EscalationLevel;
import com.curefit.odin.enums.NotificationFormat;
import com.curefit.odin.enums.TicketSource;
import com.curefit.odin.user.models.Attachment;
import com.curefit.odin.user.models.Comment;
import com.curefit.odin.user.models.Ticket;
import com.curefit.odin.user.pojo.TicketEntry;
import com.curefit.odin.user.service.AttachmentService;
import com.curefit.odin.user.service.CommentService;
import com.curefit.odin.user.service.TicketService;
import com.curefit.odin.user.service.TicketWatcherService;
import com.curefit.odin.utils.DateUtils;
import com.curefit.odin.utils.UserIdParser;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.security.GeneralSecurityException;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.curefit.cf.commons.pojo.audit.EventType.CREATE;
import static com.curefit.cf.commons.pojo.audit.EventType.UPDATE;
import static com.curefit.odin.commons.Constants.*;
import static com.curefit.odin.enums.NotificationFormat.*;
import static com.curefit.odin.utils.DateUtils.UTC_DATE_STRING_PATTERN;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@FieldDefaults(level = AccessLevel.PRIVATE)
public class NotificationHandler {

  private final static String TICKET_ID = "ticketId";
  private final static String TICKET_TITLE = "ticketTitle";
  private final static String TICKET_CATEGORY = "ticketCategory";
  private final static String TICKET_SUB_CATEGORY = "ticketSubCategory";
  private final static String LOCATION = "location";
  private final static String TENANT = "tenant";
  private final static String DUE_DATE = "dueDate";
  private final static String ACTION_TEXT = "action";
  private final static String MODIFIED_BY = "modifiedBy";
  private final static String MODIFIED_ON = "modifiedOn";
  private final static String CHANGES = "changes";
  private final static String OLD_ASSIGNEE = "oldAssignee";
  private final static String NEW_ASSIGNEE = "newAssignee";
  private final static String OLD_COMMENT = "oldComment";
  private final static String NEW_COMMENT = "newComment";
  private final static String COMMENT = "comment";
  private final static String ATTACHMENT_NAME = "attachmentName";
  private final static String NAMESPACE_CONFIG = "namespaceConfig";
  private final static String IS_USER_MENTION = "isUserMention";
  private final static String STATUS = "status";
  private final static String ESCALATION_LEVEL = "escalationLevel";
  private final static String CLOSED_AT = "closedAt";
  private final static String REOPENED_AT = "reOpenedAt";
  private final static String CREATED_ON = "createdOn";
  private final static String LAST_MODIFIED_ON = "lastModifiedOn";

  private final static List<String> DATE_KEYS = Arrays.asList(MODIFIED_ON, DUE_DATE, CLOSED_AT, REOPENED_AT, CREATED_ON, LAST_MODIFIED_ON);

  @Value("${email.enable}")
  boolean enableEmail;

  @Value("${notification.default.emails}")
  List<String> defaultNotificationEmails;

  @Value("${notification.ignored.emails}")
  List<String> ignoredNotificationEmails;

  @Autowired
  TicketService ticketService;

  @Autowired
  CommentService commentService;

  @Autowired
  AttachmentService attachmentService;

  @Autowired
  @Qualifier(value = "emailHelper")
  NotificationHelper notificationHelper;

  @Autowired
  TicketWatcherService ticketWatcherService;

  @Autowired
  RollbarService rollbarService;

  @Autowired
  UserService userService;

  @Autowired
  NamespaceTenantsMappingService namespaceTenantsMappingService;

  @Autowired
  NamespaceConfigService namespaceConfigService;

  @Autowired
  DecryptionUtils decryptionUtils;

  private static final List<String> ASSIGNEE_FIELDS = Arrays.asList(ASSIGNEE, ASSIGNEES);

  public void handleTicketUpdate(ChangeEvent changeEvent) {
    try {
      List<Change> changes = changeEvent.getChangeLog().getChanges();
      if (changes.size() == 1) {
        if (changes.stream().anyMatch(change -> ASSIGNEE_FIELDS.contains(change.getField().toLowerCase()))) {
          handleAssigneeUpdate(changeEvent);
          return;
        }
        if (changes.stream().anyMatch(change -> SLA_REMINDER_SENT_AT.equalsIgnoreCase(change.getField()))) {
          return;
        }
      }

      long ticketId = Long.parseLong(changeEvent.getChangeLog().getId());
      TicketEntry ticketEntry = ticketService.findOneById(ticketId);
      formatChanges(changes, ticketEntry.isConfidential());

      Map<String, Object> tags = new HashMap<>();
      populateCommonTags(tags, ticketEntry, changeEvent);

      tags.put(ACTION_TEXT, UPDATED);
      tags.put(CHANGES, changes);

      Optional<Change> optionalDescriptionChange = changes.stream().filter(change -> change.getField().equalsIgnoreCase(DESCRIPTION)).findFirst();
      List<String> oldMentions = null;
      List<String> newMentions = null;

      if (optionalDescriptionChange.isPresent()) {
        Change change = optionalDescriptionChange.get();
        if (change.getFrom() != null) {
          oldMentions = UserIdParser.getUserIds(change.getFrom());
          change.setFrom(replaceUserMentionWithName(change.getFrom(), oldMentions));
        }
        if (change.getTo() != null) {
          newMentions = UserIdParser.getUserIds(change.getTo());
          change.setTo(replaceUserMentionWithName(change.getTo(), newMentions));
        }
      }

      List<String> emails = fetchNotifyEmails(ticketEntry.getId(), changeEvent.getUserId());
      sendNotificationToMentionedUser(TICKET_UPDATE, emails, changeEvent.getUserId(), tags ,oldMentions, newMentions);

      tags.put(IS_USER_MENTION, false);
      notificationHelper.sendNotification(TICKET_UPDATE, emails, tags, DATE_KEYS);

    } catch (Exception e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
  }

  public void handleAssigneeUpdate(ChangeEvent changeEvent) {
    try {
      long ticketId = Long.parseLong(changeEvent.getChangeLog().getId());
      TicketEntry ticketEntry = ticketService.findOneById(ticketId);

      Optional<Change> changeOptional = changeEvent.getChangeLog().getChanges().stream()
              .filter(change -> ASSIGNEE_FIELDS.contains(change.getField().toLowerCase()))
              .findFirst();

      if (!changeOptional.isPresent()) {
        return;
      }

      List<Change> changes = Collections.singletonList(changeOptional.get());
      formatChanges(changes, ticketEntry.isConfidential());

      Map<String, Object> tags = new HashMap<>();
      populateCommonTags(tags, ticketEntry, changeEvent);

      tags.put(ACTION_TEXT, ASSIGNED);
      tags.put(OLD_ASSIGNEE, changes.get(0).getFrom());
      tags.put(NEW_ASSIGNEE, changes.get(0).getTo());

      List<String> notifyEmails = fetchNotifyEmails(ticketEntry.getId(), changeEvent.getUserId());
      if (ticketEntry.getSource() == TicketSource.EMAIL) {
        if (!notifyEmails.contains(ticketEntry.getCreatedBy())) {
          notifyEmails.add(ticketEntry.getCreatedBy());
        }
      }
      notificationHelper.sendNotification(TICKET_ASSIGN, notifyEmails, tags, DATE_KEYS);

    } catch (Exception e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
  }

  public void handleTicketComment(ChangeEvent changeEvent) {
    try {
      long commentId = Long.parseLong(changeEvent.getChangeLog().getId());
      Comment comment = commentService.fetchEntityById(commentId);
      TicketEntry ticketEntry = ticketService.convertToEntry(comment.getTicket());
      String reporterEmailId = ticketEntry.getReporter().getEmailId();
      EventType eventType = changeEvent.getChangeLog().getEventType();

      Optional<Change> optionalCommentFieldChange = changeEvent.getChangeLog()
              .getChanges()
              .stream()
              .filter(c -> c.getField().equalsIgnoreCase(COMMENT))
              .findFirst();

      Optional<Change> optionalActiveFieldChange = changeEvent.getChangeLog()
              .getChanges()
              .stream()
              .filter(c -> c.getField().equalsIgnoreCase(ACTIVE))
              .findFirst();

      String newComment = null;
      String oldComment = null;
      String actionText = EMPTY_STRING;
      if (optionalCommentFieldChange.isPresent()) {
        oldComment = optionalCommentFieldChange.get().getFrom();
        newComment = optionalCommentFieldChange.get().getTo();
        actionText = eventType == CREATE ? ADDED : UPDATED;
      }
      if (eventType == UPDATE) {
        if (optionalActiveFieldChange.isPresent() && optionalActiveFieldChange.get().getTo().equalsIgnoreCase(FALSE)) {
          oldComment = comment.getComment();
          actionText = DELETED;
        }
      }

      List<String> oldMentions = null;
      if (oldComment != null) {
        oldMentions = UserIdParser.getUserIds(oldComment);
        oldComment = replaceUserMentionWithName(oldComment, oldMentions);
      }

      List<String> newMentions = null;
      if (newComment != null) {
        newMentions = UserIdParser.getUserIds(newComment);
        newComment = replaceUserMentionWithName(newComment, newMentions);
      }

      Map<String, Object> tags = new HashMap<>();
      populateCommonTags(tags, ticketEntry, changeEvent);

      tags.put(ACTION_TEXT, actionText);
      tags.put(OLD_COMMENT, decryptIfEncrypted("comment", oldComment, ticketEntry.isConfidential()));
      tags.put(NEW_COMMENT, decryptIfEncrypted("comment", newComment, ticketEntry.isConfidential()));

      List<String> emails = fetchNotifyEmails(ticketEntry.getId(), changeEvent.getUserId());
      if (comment.getIsInternal()) {
        emails.remove(reporterEmailId);
      }
      sendNotificationToMentionedUser(TICKET_COMMENT, emails, changeEvent.getUserId(), tags, oldMentions, newMentions);

      tags.put(IS_USER_MENTION, false);
      notificationHelper.sendNotification(TICKET_COMMENT, emails, tags, DATE_KEYS);

    } catch (Exception e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
  }

  private void sendNotificationToMentionedUser(NotificationFormat notificationFormat, List<String> emails, String modifiedBy, Map<String, Object> tags, List<String> oldMentions, List<String> newMentions) {
    boolean isMentionUserPresent = false;
    if (newMentions != null) {
      if (oldMentions != null) {
        newMentions.removeAll(oldMentions);
      }
      if (newMentions.size() > 0) {
        newMentions.remove(modifiedBy);
        if (newMentions.size() > 0) {
          isMentionUserPresent = true;
        }
      }
    }
    if (isMentionUserPresent) {
      emails.removeAll(newMentions);
      tags.put(IS_USER_MENTION, true);
      notificationHelper.sendNotification(notificationFormat, newMentions, tags, DATE_KEYS);
    }
  }

  private String replaceUserMentionWithName(String text, List<String> mentions) {
    for (String mention : mentions) {
      text = text.replace("[user:" + mention + "]", userService.getUserNameFromMailId(mention));
    }
    return text;
  }

  public void handleAttachment(ChangeEvent changeEvent) {
    try {
      long attachmentId = Long.parseLong(changeEvent.getChangeLog().getId());
      Attachment attachment = attachmentService.fetchEntityById(attachmentId);

      Ticket ticket = attachment.getTicket();
      Comment comment = attachment.getComment();

      Optional<Change> optionalDescriptionFieldChange = changeEvent.getChangeLog()
              .getChanges()
              .stream()
              .filter(c -> c.getField().equalsIgnoreCase(DESCRIPTION))
              .findFirst();

      Optional<Change> optionalActiveFieldChange = changeEvent.getChangeLog()
              .getChanges()
              .stream()
              .filter(c -> c.getField().equalsIgnoreCase(ACTIVE))
              .findFirst();

      String actionText;
      String attachmentName;
      if (optionalActiveFieldChange.isPresent() && optionalActiveFieldChange.get().getTo().equalsIgnoreCase(FALSE)) {
        attachmentName = attachment.getDescription();
        actionText = DELETED;
      } else if (optionalDescriptionFieldChange.isPresent()) {
        attachmentName = optionalDescriptionFieldChange.get().getTo();
        actionText = ADDED;
      } else {
        return;
      }

      if (ticket == null) {
        ticket = comment.getTicket();
      }

      TicketEntry ticketEntry = ticketService.convertToEntry(ticket);

      Map<String, Object> tags = new HashMap<>();
      populateCommonTags(tags, ticketEntry, changeEvent);

      if (comment != null) {
        tags.put(COMMENT, comment.getComment());
      }
      tags.put(ACTION_TEXT, actionText);
      tags.put(ATTACHMENT_NAME, attachmentName);

      notificationHelper.sendNotification(TICKET_ATTACHMENT, fetchNotifyEmails(ticketEntry.getId(), changeEvent.getUserId()), tags, DATE_KEYS);

    } catch (Exception e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
  }

  public void handleTicketEscalation(TicketEntry ticketEntry, EscalationLevel escalationLevel, Set<String> escalationUserIds) {
    Map<String, Object> tags = new HashMap<>();
    tags.put(TICKET_TITLE, getTitle(ticketEntry));
    tags.put(TICKET_CATEGORY, ticketEntry.getCategoryName());
    tags.put(TICKET_SUB_CATEGORY, ticketEntry.getSubCategoryName());
    tags.put(LOCATION, ticketEntry.getLocationEntry() != null ? ticketEntry.getLocationEntry().getCenterName() : null);
    tags.put(TICKET_ID, ticketEntry.getId());
    tags.put(TENANT, ticketEntry.getTenantName().toUpperCase());
    String assignees = ticketEntry.getAssigneeQueueUsers() != null ? ticketEntry.getAssigneeQueueUsers().stream()
            .map(UserEntry::getName)
            .collect(Collectors.joining(",")) : UNASSIGNED;
    tags.put(ASSIGNEES, assignees);
    tags.put(STATUS, ticketEntry.getStatus().getName());
    tags.put(DUE_DATE, String.valueOf(ticketEntry.getDueDate()));
    tags.put(ESCALATION_LEVEL, escalationLevel);

    if(!enableEmail) {
      escalationUserIds.clear();
    }
    escalationUserIds.addAll(fetchNotifyEmails(ticketEntry.getId(), null));
    notificationHelper.sendNotification(TICKET_ESCALATION, new ArrayList<>(escalationUserIds), tags, DATE_KEYS);
  }

  private List<String> fetchNotifyEmails(long ticketId, String modifiedBy) {
    Set<String> watcherEmails = ticketWatcherService.findAllUsersToUpdate(ticketId);
    watcherEmails.remove(modifiedBy);
    if(!enableEmail) {
      watcherEmails.clear();
    }
    if (defaultNotificationEmails != null) {
      watcherEmails.addAll(defaultNotificationEmails.stream()
              .filter(email -> !StringUtils.isBlank(email))
              .map(String::trim)
              .collect(Collectors.toList()));
    }
    if (ignoredNotificationEmails != null) {
      watcherEmails.removeAll(ignoredNotificationEmails.stream()
              .filter(email -> !StringUtils.isBlank(email))
              .map(String::trim)
              .collect(Collectors.toList()));
    }
    return new ArrayList<>(watcherEmails);
  }

  public void handleTicketSLAReminder(TicketEntry ticketEntry) {
    if (!enableEmail) {
      return;
    }
    List<UserEntry> assigneeEntries = ticketEntry.getAssigneeQueueUsers();
    if (assigneeEntries == null || assigneeEntries.isEmpty()) {
      log.info("SLA Reminder Notification not sent for Ticket {} : Assignee list is empty.", ticketEntry.getId());
      return;
    }
    Map<String, Object> tags = new HashMap<>();
    tags.put(TICKET_TITLE, getTitle(ticketEntry));
    tags.put(TICKET_CATEGORY, ticketEntry.getCategoryName());
    tags.put(TICKET_SUB_CATEGORY, ticketEntry.getSubCategoryName());
    tags.put(LOCATION, ticketEntry.getLocationEntry() != null ? ticketEntry.getLocationEntry().getCenterName() : null);
    tags.put(TICKET_ID, ticketEntry.getId());
    tags.put(TENANT, ticketEntry.getTenantName().toUpperCase());
    String assigneeNames = assigneeEntries != null ? assigneeEntries.stream()
        .map(UserEntry::getName)
        .collect(Collectors.joining(", ")) : UNASSIGNED;
    tags.put(ASSIGNEES, assigneeNames);
    tags.put(STATUS, ticketEntry.getStatus().getName());
    tags.put(DUE_DATE, String.valueOf(ticketEntry.getDueDate()));
    List<String> asigneeEmails = assigneeEntries != null ? assigneeEntries.stream()
        .map(UserEntry::getEmailId)
        .collect(Collectors.toList()) : null;
    notificationHelper.sendNotification(TICKET_SLA_REMINDER, asigneeEmails, tags, DATE_KEYS);
    log.info("Sent SLA Reminder for Ticket {}", ticketEntry.getId());
  }

  public void handleTicketSLAReminderToWatchers(TicketEntry ticketEntry) {
    if (!enableEmail) {
      return;
    }
    List<UserEntry> ticketEntryWatchers = ticketEntry.getWatchers();
    if (ticketEntryWatchers == null || ticketEntryWatchers.isEmpty()) {
      log.info("SLA Reminder Notification not sent for Ticket {} : No-one is watching the ticket.", ticketEntry.getId());
      return;
    }
    Map<String, Object> tags = new HashMap<>();
    tags.put(TICKET_TITLE, getTitle(ticketEntry));
    tags.put(TICKET_CATEGORY, ticketEntry.getCategoryName());
    tags.put(TICKET_SUB_CATEGORY, ticketEntry.getSubCategoryName());
    tags.put(LOCATION, ticketEntry.getLocationEntry() != null ? ticketEntry.getLocationEntry().getCenterName() : null);
    tags.put(TICKET_ID, ticketEntry.getId());
    tags.put(TENANT, ticketEntry.getTenantName().toUpperCase());
    String watcherNames = ticketEntryWatchers.stream()
            .map(UserEntry::getName)
            .collect(Collectors.joining(", "));
    tags.put(WATCHERS, watcherNames);
    tags.put(STATUS, ticketEntry.getStatus().getName());
    tags.put(DUE_DATE, String.valueOf(ticketEntry.getDueDate()));
    notificationHelper.sendNotification(TICKET_SLA_TO_BE_BREACHED_TOMORROW_SLA_REMINDER, ticketEntryWatchers.stream()
            .map(UserEntry::getEmailId)
            .collect(Collectors.toList()), tags, DATE_KEYS);
    log.info("Sent SLA Reminder for Ticket {}", ticketEntry.getId());
  }

  private void formatChanges(List<Change> changes, boolean isConfidential) {
    changes.forEach(change -> {
      decryptChangeIfConfidential(change, isConfidential);
      if (ASSIGNEE.equalsIgnoreCase(change.getField())) {
        change.setFrom(StringUtils.isEmpty(change.getFrom()) ? UNASSIGNED : userService.getUserNameFromMailId(change.getFrom()));
        change.setTo(StringUtils.isEmpty(change.getTo()) ? UNASSIGNED : userService.getUserNameFromMailId(change.getTo()));
      } else if (ASSIGNEES.equalsIgnoreCase(change.getField())) {
        change.setField(ASSIGNEE);
        change.setFrom(StringUtils.isEmpty(change.getFrom()) ? UNASSIGNED : getListFromString(change.getFrom())
                .stream().map(from -> userService.getUserNameFromMailId(from))
                .collect(Collectors.toList()).toString());
        change.setTo(StringUtils.isEmpty(change.getTo()) ? UNASSIGNED : getListFromString(change.getTo())
                .stream().map(to -> userService.getUserNameFromMailId(to))
                .collect(Collectors.toList()).toString());

      } else {
        change.setFrom(change.getFrom() == null ? EMPTY_STRING : change.getFrom());
        change.setTo(change.getTo() == null ? EMPTY_STRING : change.getTo());
      }

      if (DATE_KEYS.contains(change.getField())) {
        try {
          change.setFrom(DateUtils.changeToIST(change.getFrom(), UTC_DATE_STRING_PATTERN));
          change.setTo(DateUtils.changeToIST(change.getTo(), UTC_DATE_STRING_PATTERN));
        } catch (ParseException e) {
          log.error(e.getMessage(), e);
        }
      }
      change.setField(StringUtils.capitalize(change.getField()));
    });
  }

  private List<String> getListFromString(String s) {
    return Arrays.asList(s.replace("[", "").replace("]", "").split(","));
  }

  private void populateCommonTags(Map<String, Object> tags, TicketEntry ticketEntry, ChangeEvent changeEvent) {
    tags.put(TICKET_TITLE, getTitle(ticketEntry));
    tags.put(TICKET_CATEGORY, ticketEntry.getCategoryName());
    tags.put(TICKET_SUB_CATEGORY, ticketEntry.getSubCategoryName());
    tags.put(LOCATION, ticketEntry.getLocationEntry() != null ? ticketEntry.getLocationEntry().getCenterName() : null);
    tags.put(TICKET_ID, ticketEntry.getId());
    tags.put(TENANT, ticketEntry.getTenantName().toUpperCase());
    tags.put(MODIFIED_ON, new Date(changeEvent.getChangeLog().getModifiedOn()).toString());
    tags.put(MODIFIED_BY, userService.getUserNameFromMailId(changeEvent.getUserId()));

    tags.put(NAMESPACE_CONFIG, namespaceTenantsMappingService
            .getNamespacesByTenant(ticketEntry.getTenantId())
            .stream()
            .map(namespaceTenant -> namespaceConfigService.findByNamespace(namespaceTenant.getName()))
            .filter(Objects::nonNull)
            .collect(Collectors.toList()));
  }

  private String getTitle(TicketEntry ticketEntry) {
    String title;
    if (!StringUtils.isEmpty(ticketEntry.getTitle())) {
      title =  ticketEntry.getTitle();
    } else {
      if (!StringUtils.isEmpty(ticketEntry.getCategoryName())) {
        title = ticketEntry.getCategoryName();
        if (!StringUtils.isEmpty(ticketEntry.getSubCategoryName())) {
          title = title + " - " + ticketEntry.getSubCategoryName();
        }
      } else {
        title = "";
      }
    }
    if (ticketEntry.getLocationEntry() != null && ticketEntry.getLocationEntry().getCenterName() != null) {
      title = title + " (" + ticketEntry.getLocationEntry().getCenterName() + ")";
    }
    return title;
  }

  private void decryptChangeIfConfidential(Change change, boolean isConfidential) {
    if (isConfidential) {
      try {
        if ("comment".equals(change.getField()) || "description".equals(change.getField()) || "title".equals(change.getField())) {
          if (StringUtils.isNotBlank(change.getFrom())) {
            change.setFrom((String) decryptionUtils.decrypt(change.getFrom()));
          }
          if (StringUtils.isNotBlank(change.getTo())) {
            change.setTo((String) decryptionUtils.decrypt(change.getTo()));
          }
        }
      } catch (GeneralSecurityException e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
      }
    }
  }

  private String decryptIfEncrypted(String fieldName, String fieldValue, boolean isConfidential) {
    if (isConfidential) {
      if ("comment".equals(fieldName) || "description".equals(fieldName) || "title".equals(fieldName)) {
        try {
          if (StringUtils.isBlank(fieldValue)) {
            return fieldValue;
          }
          return (String) decryptionUtils.decrypt(fieldValue);
        } catch (GeneralSecurityException e) {
          log.error(e.getMessage(), e);
          rollbarService.error(e);
        }
      }
    }
    return fieldValue;
  }
}
