package com.curefit.odin.notification;

import com.curefit.base.enums.AppTenant;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.iris.models.SendCampaignNotificationsRequest;
import com.curefit.iris.models.SendCampaignNotificationsResponse;
import com.curefit.iris.models.SendCampaignNotificationsResponseStatus;
import com.curefit.iris.models.UserContext;
import com.curefit.iris.services.spi.CampaignService;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Slf4j
@Service
@FieldDefaults(level = AccessLevel.PRIVATE)
public class NotificationService {

  @Autowired
  CampaignService campaignService;

  @Autowired
  RollbarService rollbarService;

  public SendCampaignNotificationsResponseStatus sendNotification(String campaignId, String creativeId, List<UserContext> userContexts, Map<String, Object> tags) {
    SendCampaignNotificationsRequest sendCampaignNotificationsRequest = new SendCampaignNotificationsRequest();
    sendCampaignNotificationsRequest.setCampaignId(campaignId);
    sendCampaignNotificationsRequest.setCreativeIds(Collections.singletonList(creativeId));

    sendCampaignNotificationsRequest.setUserContexts(userContexts);
    sendCampaignNotificationsRequest.setGlobalTags(tags);

    log.info("SOS: Send notification request: {}", sendCampaignNotificationsRequest);
    try {
      SendCampaignNotificationsResponse sendCampaignNotificationsResponse = campaignService.sendCampaignMessages(sendCampaignNotificationsRequest, AppTenant.CUREFIT);
      log.info("send notification response {}", sendCampaignNotificationsResponse);
      try {
        return sendCampaignNotificationsResponse.getResponse().entrySet().iterator().next().getValue().get(0);
      } catch (Exception e) {
        return null;
      }
    } catch (BaseException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    return null;
  }
}
