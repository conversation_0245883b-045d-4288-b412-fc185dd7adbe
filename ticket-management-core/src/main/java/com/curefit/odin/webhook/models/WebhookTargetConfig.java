package com.curefit.odin.webhook.models;

import com.curefit.odin.admin.models.BaseMySQLModel;
import com.curefit.odin.enums.EndpointType;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import javax.persistence.*;

/**
 * <AUTHOR>
 */

@Getter
@Setter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "webhook_target_config")
public class WebhookTargetConfig extends BaseMySQLModel {

  @Column(name = "type")
  @Enumerated(EnumType.STRING)
  EndpointType endpointType;

  String config;
}