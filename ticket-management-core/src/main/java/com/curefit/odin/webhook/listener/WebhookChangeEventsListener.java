package com.curefit.odin.webhook.listener;

import com.amazonaws.regions.Regions;
import com.amazonaws.services.sqs.model.Message;
import com.curefit.cf.commons.pojo.audit.ChangeEvent;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.consumer.BaseSQSConsumer;
import com.curefit.odin.ChangeEventConfiguration;
import com.curefit.odin.webhook.handler.WebhookChangeEventsHandler;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
@Profile("!local & !alpha")
public class WebhookChangeEventsListener extends BaseSQSConsumer {

  @Qualifier(value = "odinObjectMapper")
  @Autowired
  ObjectMapper objectMapper;

  @Autowired
  WebhookChangeEventsHandler webhookChangeEventsHandler;

  @Autowired
  RollbarService rollbarService;

  public WebhookChangeEventsListener(ChangeEventConfiguration changeEventConfiguration) {
    super(changeEventConfiguration.getWebhookEventsQueue(), Regions.AP_SOUTH_1,
            changeEventConfiguration.getWebhookEventsQueueWaitTimeInSec(), changeEventConfiguration.getWebhookEventsQueueBatchSize());
  }

  @Override
  public List<Boolean> process(List<Message> messages) {
    return messages.stream().map(message -> {
      log.info("message for webhook : {} ", message.getBody());
      try {
        ChangeEvent changeEvent = objectMapper.readValue(message.getBody(), ChangeEvent.class);
        webhookChangeEventsHandler.handle(changeEvent);
        return true;
      } catch (Exception e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
      }
      return false;
    }).collect(Collectors.toList());
  }
}
