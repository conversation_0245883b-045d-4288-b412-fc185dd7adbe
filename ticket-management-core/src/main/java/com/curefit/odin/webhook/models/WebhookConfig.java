package com.curefit.odin.webhook.models;

import com.curefit.odin.admin.models.BaseMySQLModel;
import com.vladmihalcea.hibernate.type.json.JsonStringType;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;
import org.json.simple.JSONObject;

import javax.persistence.*;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Getter
@Setter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "webhook_config")
@TypeDefs({@TypeDef(name = "json", typeClass = JsonStringType.class),})
public class WebhookConfig extends BaseMySQLModel {

  String name;

  String description;

  String filter;

  @Column(name = "events_meta")
  String eventsMeta;

  @Column(name = "exclude_body")
  Boolean excludeBody;

  @ManyToOne
  @JoinColumn(name = "webhook_target_config_id", referencedColumnName = "id")
  WebhookTargetConfig webhookTargetConfig;

  @Type(type = "json")
  @Column(name = "custom_payload", columnDefinition = "json")
  Map<String, Object> customPayload;

}
