package com.curefit.odin.webhook.controllers;

import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.sf.controller.BaseController;
import com.curefit.odin.enums.EntityEvent;
import com.curefit.odin.enums.WebhookEntity;
import com.curefit.odin.webhook.models.WebhookConfig;
import com.curefit.odin.webhook.pojo.WebhookConfigEntry;
import com.curefit.odin.webhook.service.WebhookConfigService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@RestController
@RequestMapping("/webhook")
public class WebhookConfigController extends BaseController<WebhookConfig, WebhookConfigEntry> {

  public WebhookConfigController(WebhookConfigService webhookConfigService) {
    super(webhookConfigService);
  }

  @RequestMapping(method = RequestMethod.GET, value = "{id}/detail")
  public ResponseEntity<WebhookConfigEntry> fetchDetail(@PathVariable Long id) throws BaseException {
    return new ResponseEntity<>(((WebhookConfigService) baseMySQLService).fetchDetail(id), HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET, value = "/events_meta")
  public ResponseEntity<Map<WebhookEntity, Set<EntityEvent>>> fetch() {
    Map<WebhookEntity, Set<EntityEvent>> eventsMeta = Arrays.stream(WebhookEntity.values())
            .collect(Collectors.toMap(w -> w, WebhookEntity::getEntityEvents));
    return new ResponseEntity<>(eventsMeta, HttpStatus.OK);
  }
}
