package com.curefit.odin.webhook.service;

import com.curefit.common.rest.client.CommonHttpHelper;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.odin.webhook.configs.HTTPConfig;
import com.curefit.odin.webhook.pojo.WebhookEvent;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class WebhookHttpEndpointEventProcessor {

  @Autowired
  CommonHttpHelper httpHelper;

  @Autowired
  RollbarService rollbarService;

  static final Map<String, String> defaultHeaders = new HashMap<String, String>() {{
    put("Content-Type", "application/json");
  }};

  public <T> void process(T webhookEvent, HTTPConfig config, Boolean excludeBody) {
    try {
      config.getHeaders().putAll(defaultHeaders);

      T requestBody = webhookEvent;
      if (excludeBody != null && excludeBody) {
        requestBody = null;
      }
      ResponseEntity<Object> response = httpHelper.request(config.getEndPoint(), HttpMethod.POST, requestBody, config.getHeaders(), Object.class);
      if (response != null && response.getBody() != null) {
        log.debug("Received response {} from endPoint {}" , response.getBody(), config.getEndPoint());
      }
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
  }
}
