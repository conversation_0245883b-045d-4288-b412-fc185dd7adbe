package com.curefit.odin.webhook;

import com.curefit.cf.commons.pojo.audit.Change;
import com.curefit.odin.admin.pojo.UserEntry;
import com.curefit.odin.webhook.pojo.WebhookEventPayload;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class WebhookFieldMapper {

    static final Pattern pattern = Pattern.compile("\\{\\{[a-zA-Z]+\\.+[a-zA-Z]+}}");

    static final Map<String, Function<WebhookEventPayload, Object>> fieldMap = new HashMap<>();

    public WebhookFieldMapper(ObjectMapper objectMapper) {
        fieldMap.put("ticket.id", event -> event.getTicket().getId());
        fieldMap.put("ticket.title", event -> event.getTicket().getTitle());
        fieldMap.put("ticket.description", event -> event.getTicket().getDescription());
        fieldMap.put("ticket.priority", event -> event.getTicket().getPriority());
        fieldMap.put("ticket.status", event -> event.getTicket().getStatus());
        fieldMap.put("ticket.tenant", event -> event.getTicket().getTenantName());
        fieldMap.put("ticket.category", event -> event.getTicket().getCategoryName());
        fieldMap.put("ticket.subCategory", event -> event.getTicket().getSubCategoryName());
        fieldMap.put("ticket.reporter", event -> event.getTicket().getReporter().getEmailId());
        fieldMap.put("ticket.location", event -> {
            if (event.getTicket().getLocationEntry() != null) {
                return event.getTicket().getLocationEntry().getCenterName();
            }
            return null;
        });
        fieldMap.put("ticket.watchers", event -> {
            List<UserEntry> watchers = event.getTicket().getWatchers();
            if (watchers != null) {
                return watchers.stream().map(UserEntry::getEmailId).collect(Collectors.toList());
            }
            return Collections.emptyList();
        });

        fieldMap.put("ticket.assignees", event -> {
            List<UserEntry> assigneeQueueUsers = event.getTicket().getAssigneeQueueUsers();
            if (assigneeQueueUsers != null) {
                return assigneeQueueUsers.stream().map(UserEntry::getEmailId).collect(Collectors.toList());
            }
            return Collections.emptyList();
        });

        fieldMap.put("ticket.currentAssignee", event -> {
            List<Change> changes = event.getChanges();
            return changes.stream().filter(change -> change.getField().equals("assignees"))
                    .findFirst()
                    .map(change -> {
                        List<String> oldAssignees = new ArrayList<>();
                        List<String> newAssignees = new ArrayList<>();
                        if (change.getFrom() != null) {
                            try {
                                oldAssignees = objectMapper.readValue(change.getFrom(), List.class);
                            } catch (IOException e) {
                                log.error(e.getMessage(), e);
                            }
                        }

                        if (change.getTo() != null) {
                            try {
                                newAssignees = objectMapper.readValue(change.getTo(), List.class);
                            } catch (IOException e) {
                                log.error(e.getMessage(), e);
                            }
                        }
                        newAssignees.removeAll(oldAssignees);
                        return newAssignees;
                    })
                    .orElse(Collections.emptyList());
        });

        fieldMap.put("ticket.currentWatcher", event -> {
            List<Change> changes = event.getChanges();
            return changes.stream().filter(change -> change.getField().equals("userId"))
                    .findFirst()
                    .map(Change::getTo)
                    .orElse(null);
        });

        fieldMap.put("comment.name", event -> event.getComment().getComment());
        fieldMap.put("comment.createdBy", event -> event.getComment().getUser().getEmailId());
    }

    public Object getValue(String key, WebhookEventPayload eventPayload) {
        Function<WebhookEventPayload, Object> func = fieldMap.get(key);
        if (func == null) {
            return null;
        }
        return func.apply(eventPayload);
    }

    public boolean isFieldPresent(String key) {
        return fieldMap.containsKey(key);
    }

    public Object replaceDynamicField(Object value, WebhookEventPayload webhookEventPayload) {

        boolean replaced = false;
        String strValue = value.toString();
        String output = strValue;

        Matcher matcher = pattern.matcher(strValue);

        while (matcher.find()) {
            int start = matcher.start();
            int end = matcher.end();
            String key = strValue.substring(start + 2, end - 2);

            if (isFieldPresent(key)) {
                if (strValue.length() == end - start) {
                    return getValue(key, webhookEventPayload);
                } else {
                    output = output.replace(matcher.group(), getValue(key, webhookEventPayload).toString());
                    replaced = true;
                }
            }
        }
        if (replaced) {
            return output;
        }
        return value;
    }
}
