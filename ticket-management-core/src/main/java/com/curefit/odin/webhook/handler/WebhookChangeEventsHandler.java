package com.curefit.odin.webhook.handler;

import com.curefit.cf.commons.pojo.audit.Change;
import com.curefit.cf.commons.pojo.audit.ChangeEvent;
import com.curefit.cf.commons.pojo.audit.EventType;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.odin.admin.pojo.UserEntry;
import com.curefit.odin.admin.service.UserService;
import com.curefit.odin.enums.EntityEvent;
import com.curefit.odin.enums.WebhookEntity;
import com.curefit.odin.user.models.Attachment;
import com.curefit.odin.user.models.Comment;
import com.curefit.odin.user.models.TicketWatcher;
import com.curefit.odin.user.pojo.AttachmentEntry;
import com.curefit.odin.user.pojo.CommentEntry;
import com.curefit.odin.user.pojo.TicketEntry;
import com.curefit.odin.user.service.AttachmentService;
import com.curefit.odin.user.service.CommentService;
import com.curefit.odin.user.service.TicketService;
import com.curefit.odin.user.service.TicketWatcherService;
import com.curefit.odin.webhook.WebhookFieldMapper;
import com.curefit.odin.webhook.pojo.*;
import com.curefit.odin.webhook.service.WebhookConfigService;
import com.curefit.odin.webhook.service.WebhookEventsProcessor;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.curefit.odin.commons.Constants.*;

/**
 * <AUTHOR>
 */

@Slf4j
@Component
@FieldDefaults(level = AccessLevel.PRIVATE)
public class WebhookChangeEventsHandler {

  @Autowired
  WebhookConfigService webhookConfigService;

  @Autowired
  TicketService ticketService;

  @Autowired
  CommentService commentService;

  @Autowired
  AttachmentService attachmentService;

  @Autowired
  TicketWatcherService ticketWatcherService;

  @Autowired
  UserService userService;

  @Autowired
  WebhookEventsProcessor webhookEventsProcessor;

  @Autowired
  RollbarService rollbarService;

  @Autowired
  WebhookFieldMapper webhookFieldMapper;

  public void handle(ChangeEvent changeEvent) throws BaseException {

    List<WebhookConfigEntry> webhooks = webhookConfigService.fetchAllWebhooks();

    String changeEntity = changeEvent.getChangeLog().getEntity().toUpperCase();
    WebhookEntity entity = WebhookEntity.valueOf(changeEntity);

    log.debug("received webhook event for entity {}", entity);

    List<Change> changes = changeEvent.getChangeLog().getChanges();
    EntityEvent entityEvent = getEntityEventAndCleanChanges(changeEvent.getChangeLog().getEventType(), changes);

    Map<String, Change> fieldChangesMap = changes.stream()
            .collect(Collectors.toMap(Change::getField, value -> value));

    webhooks = webhooks.stream()
            .filter(webhookConfigEntry -> {
              List<WebhookMeta> webhookMetas = webhookConfigEntry.getWebhookEventsMeta();
              return webhookMetas.stream()
                      .filter(webhookMeta -> webhookMeta.getEntity().equals(entity))
                      .filter(webhookMeta -> webhookMeta.getEvents().containsKey(entityEvent))
                      .anyMatch(webhookMeta -> {
                        List<WebhookMeta.EventMeta> eventMetas = webhookMeta.getEvents().get(entityEvent);
                        long matchCount = eventMetas.stream()
                                .filter(eventMeta -> fieldChangesMap.containsKey(eventMeta.getKey())
                                && (eventMeta.getNewValue().equals("any")
                                    || Objects.equals(fieldChangesMap.get(eventMeta.getKey()).getTo(), eventMeta.getNewValue())))
                                .count();
                        return matchCount == eventMetas.size();
                      });
            })
            .collect(Collectors.toList());

    if (webhooks.isEmpty()) {
      return;
    }

    WebhookEventPayload webhookEventPayload = getWebhookEventPayload(entity, Long.parseLong(changeEvent.getChangeLog().getId()));
    webhookEventPayload.setChanges(changes);

    webhooks = webhooks.stream()
            .filter(webhookConfigEntry -> isFilterPassed(webhookConfigEntry.getWebhookFilter(), webhookEventPayload))
            .collect(Collectors.toList());

    enhanceChanges(entity, entityEvent, webhookEventPayload, changes);

    List<WebhookConfigEntry> hooksWithoutCustomPayload = webhooks.stream()
            .filter(webhook -> webhook.getCustomPayload() == null)
            .collect(Collectors.toList());

    if (!hooksWithoutCustomPayload.isEmpty()) {
      WebhookEvent webhookEvent = WebhookEvent.builder()
              .entity(entity)
              .entityEvent(entityEvent)
              .payload(webhookEventPayload)
              .user(fetchUser(changeEvent.getUserId()))
              .modifiedOn(changeEvent.getChangeLog().getModifiedOn())
              .changeList(mapChanges(changes))
              .build();
      webhookEventsProcessor.process(hooksWithoutCustomPayload, webhookEvent);
  }

    webhooks.stream()
            .filter(webhook -> webhook.getCustomPayload() != null)
            .forEach(webhook -> {
              Map<String, Object> customPayload = webhook.getCustomPayload();
              updateDynamicValues(customPayload, webhookEventPayload);
              webhookEventsProcessor.process(Collections.singletonList(webhook), customPayload);
            });
  }

  private void updateDynamicValues(Map<String, Object> customPayload, WebhookEventPayload webhookEventPayload) {
    for (Object key : customPayload.keySet()) {
      String keyStr = (String)key;
      Object keyValue = customPayload.get(keyStr);

      if (keyValue instanceof List) {
        List<Object> list = (List<Object>) keyValue;
        List<Object> updatedList = new ArrayList<>();
        for (Object element : list) {
          if (element instanceof Map) {
            updateDynamicValues((Map)keyValue, webhookEventPayload);
          } else {
            updatedList.add(webhookFieldMapper.replaceDynamicField(element.toString(), webhookEventPayload));
          }
        }
        customPayload.put(keyStr, updatedList);
      }
      else if (keyValue instanceof Map) {
        updateDynamicValues((Map) keyValue, webhookEventPayload);
      } else {
        customPayload.put(keyStr, webhookFieldMapper.replaceDynamicField(keyValue, webhookEventPayload));
      }
    }
  }

  private List<FieldChange> mapChanges(List<Change> changes) {
    return changes.stream().map(change -> FieldChange.builder()
            .field(change.getField())
            .from(change.getFrom())
            .to(change.getTo())
            .build())
            .collect(Collectors.toList());
  }

  private void enhanceChanges(WebhookEntity entity, EntityEvent entityEvent, WebhookEventPayload webhookEventPayload, List<Change> changes) {
    if (entity == WebhookEntity.TICKET) {
      if (entityEvent == EntityEvent.CREATE) {
        changes.clear();
      }
    } else if (entityEvent == EntityEvent.DELETE && entity == WebhookEntity.COMMENT) {
      changes.add(Change.builder().from(webhookEventPayload.getComment().getComment())
              .to("")
              .field(COMMENT).build());

    } else if (entityEvent == EntityEvent.DELETE && entity == WebhookEntity.ATTACHMENT) {
      changes.add(Change.builder().from(webhookEventPayload.getAttachment().getDescription())
              .to("")
              .field(DESCRIPTION).build());
    }
  }

  private EntityEvent getEntityEventAndCleanChanges(EventType eventType, List<Change> changes) {

    EntityEvent entityEvent = eventType == EventType.CREATE ? EntityEvent.CREATE :
            eventType == EventType.UPDATE ? EntityEvent.UPDATE : EntityEvent.DELETE;
    Optional<Change> optionalActiveFieldChange = changes.stream()
            .filter(c -> c.getField().equalsIgnoreCase(ACTIVE))
            .findFirst();

    if (optionalActiveFieldChange.isPresent()) {
      if (optionalActiveFieldChange.get().getTo().equalsIgnoreCase(FALSE)) {
        entityEvent = EntityEvent.DELETE;
        changes.clear();
      } else {
        entityEvent = EntityEvent.CREATE;
        changes.remove(optionalActiveFieldChange.get());
      }
    }
    return entityEvent;
  }

  private WebhookEventPayload getWebhookEventPayload(WebhookEntity entity, long changeId) throws BaseException {
    TicketEntry ticketEntry = null;
    CommentEntry commentEntry = null;
    AttachmentEntry attachmentEntry = null;
    Comment comment;
    Attachment attachment;

    switch (entity) {
      case TICKET:
        ticketEntry = ticketService.fetchDetail(changeId);
        break;
      case COMMENT:
        comment = commentService.fetchEntityById(changeId);
        ticketEntry = ticketService.convertToEntry(comment.getTicket());
        ticketService.populateDetails(ticketEntry);
        commentEntry = commentService.convertToEntry(comment);
        break;
      case ATTACHMENT:
        attachment = attachmentService.fetchEntityById(changeId);
        if (attachment.getTicket() != null) {
          ticketEntry = ticketService.convertToEntry(attachment.getTicket());
          ticketService.populateDetails(ticketEntry);
        } else {
          comment = attachment.getComment();
          ticketEntry = ticketService.convertToEntry(comment.getTicket());
          ticketService.populateDetails(ticketEntry);
          commentEntry = commentService.convertToEntry(comment);
        }
        attachmentEntry = attachmentService.convertToEntry(attachment);
        break;

      case TICKETWATCHER:
        TicketWatcher ticketWatcher = ticketWatcherService.fetchEntityById(changeId);
        ticketEntry = ticketService.convertToEntry(ticketWatcher.getTicket());
        ticketService.populateDetails(ticketEntry);
        break;
      default:
        break;
    }
    if (ticketEntry != null && StringUtils.isEmpty(ticketEntry.getTitle())) {
      ticketEntry.setTitle(String.format("Odin Ticket #%s", ticketEntry.getId()));
    }
    return WebhookEventPayload.builder()
            .ticket(ticketEntry)
            .comment(commentEntry)
            .attachment(attachmentEntry).build();
  }

  private boolean isFilterPassed(WebhookFilter filter, WebhookEventPayload webhookEventPayload) {
    if (filter == null) {
      return true;
    }

    TicketEntry ticketEntry = webhookEventPayload.getTicket();

    return isPresentIfNotEmpty(filter.getTenantIds(), ticketEntry.getTenantId())
            && isPresentIfNotEmpty(filter.getCategoryIds(), ticketEntry.getCategoryId())
            && isPresentIfNotEmpty(filter.getSubCategoryIds(), ticketEntry.getSubCategoryId())
            && isPresentIfNotEmpty(filter.getStatus(), ticketEntry.getStatus())
            && isPresentIfNotEmpty(filter.getAssignedQueueIds(), ticketEntry.getAssignedQueueId())
            && checkAssigneeOrWatchers(filter.getAssignedUserIds(), ticketEntry.getAssigneeQueueUsers(), webhookEventPayload)
            && isPresentIfNotEmpty(filter.getLocations(), ticketEntry.getLocationEntry() != null ? ticketEntry.getLocationEntry().getNeoCode() : null)
            && isPresentIfNotEmpty(filter.getCreatedBy(), ticketEntry.getReporter() != null ? ticketEntry.getReporter().getEmailId() : null)
            && isPresentIfNotEmpty(filter.getPriorities(), ticketEntry.getPriority())
            && isPresentIfNotEmpty(filter.getSourceRefIds(), ticketEntry.getSourceRefId())
            && isPresentIfNotEmpty(filter.getSource(), ticketEntry.getSource())
            && checkDateInBetweenIfPresent(filter.getCreatedOnFrom(), filter.getCreatedOnTo(), ticketEntry.getCreatedOn())
            && checkDateInBetweenIfPresent(filter.getDueDateFrom(), filter.getDueDateTo(), ticketEntry.getDueDate())
            && checkAssigneeOrWatchers(filter.getWatcherUserIds(), ticketEntry.getWatchers(), webhookEventPayload);

  }

  private <E> boolean isPresentIfNotEmpty(List<E> list, E element) {
    if (CollectionUtils.isEmpty(list)) {
      return true;
    }
    if (element != null) {
      return list.contains(element);
    }
    return false;
  }

  private boolean checkAssigneeOrWatchers(List<String> filterUsers, List<UserEntry> userEntries, WebhookEventPayload webhookEventPayload) {
    if (CollectionUtils.isEmpty(filterUsers)) {
      return true;
    }
    if (CollectionUtils.isNotEmpty(userEntries)) {
      return userEntries.stream().map(UserEntry::getEmailId)
              .anyMatch(filterUsers::contains);
    }
    return false;
  }

  private <E> boolean isPresentIfNotEmpty(E filterElement, E element) {
    if (filterElement == null) {
      return true;
    }
    if (element != null) {
      return filterElement.equals(element);
    }
    return false;
  }

  private boolean checkDateInBetweenIfPresent(Date fromDate, Date toDate, Date dateToCheck) {
    if (fromDate == null && toDate == null) {
      return true;
    }
    if (dateToCheck == null) {
      return false;
    }
    if (fromDate != null && toDate != null) {
      return dateToCheck.after(fromDate) && dateToCheck.before(toDate);
    }
    if (fromDate != null) {
      return dateToCheck.after(fromDate);
    }
    return dateToCheck.before(toDate);
  }

  private UserEntry fetchUser(String userId) {
    UserEntry userEntry;
    try {
      userEntry = userService.findUserByMailId(userId);
    } catch (Exception e) {
      rollbarService.error(e);
      userEntry = new UserEntry(userId, userId);
    }
    return userEntry;
  }
}
