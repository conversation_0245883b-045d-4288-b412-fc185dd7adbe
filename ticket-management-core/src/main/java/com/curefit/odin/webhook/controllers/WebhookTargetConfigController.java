package com.curefit.odin.webhook.controllers;

import com.curefit.odin.enums.EndpointType;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */

@RestController
@RequestMapping("/webhook_target")
public class WebhookTargetConfigController {

  @RequestMapping(method = RequestMethod.GET, value = "/endpoints")
  public ResponseEntity<List<EndpointType>> fetchAll() {
    return new ResponseEntity<>(Arrays.asList(EndpointType.values()), HttpStatus.OK);
  }
}

