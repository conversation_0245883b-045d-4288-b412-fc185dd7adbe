package com.curefit.odin.webhook.service;

import com.curefit.odin.enums.EndpointType;
import com.curefit.odin.webhook.configs.HTTPConfig;
import com.curefit.odin.webhook.pojo.WebhookTargetConfigEntry;
import com.curefit.odin.webhook.pojo.WebhookConfigEntry;
import com.curefit.odin.webhook.pojo.WebhookEvent;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */

@Slf4j
@Component
@FieldDefaults(level = AccessLevel.PRIVATE)
public class WebhookEventsProcessor {

  @Autowired
  WebhookHttpEndpointEventProcessor webhookHttpEndpointEventProcessor;

  public <T> void process(List<WebhookConfigEntry> webhooks, T webhookEvent) {
    webhooks.forEach(webhookConfigEntry -> {
      WebhookTargetConfigEntry webhookTargetConfigEntry = webhookConfigEntry.getWebhookTargetConfigEntry();
      EndpointType endpointType = webhookTargetConfigEntry.getEndpointType();
      //only enabled for HTTP for now, need to configure for other type as well
      if (endpointType == EndpointType.HTTP) {
        log.info("processing webhook message {} for webhookEndPoint {}", webhookEvent, webhooks);
        webhookHttpEndpointEventProcessor.process(webhookEvent, (HTTPConfig) webhookTargetConfigEntry.getConfig(), webhookConfigEntry.getExcludeBody());
      }
    });
  }
}