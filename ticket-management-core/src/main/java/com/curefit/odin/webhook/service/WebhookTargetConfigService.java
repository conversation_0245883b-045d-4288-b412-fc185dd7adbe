package com.curefit.odin.webhook.service;

import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.odin.enums.EndpointType;
import com.curefit.odin.webhook.models.WebhookTargetConfig;
import com.curefit.odin.webhook.pojo.WebhookTargetConfigEntry;
import com.curefit.odin.webhook.repositories.WebhookTargetConfigRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class WebhookTargetConfigService extends BaseMySQLService<WebhookTargetConfig, WebhookTargetConfigEntry> {

  @Autowired
  RollbarService rollbarService;

  public WebhookTargetConfigService(WebhookTargetConfigRepository webhookTargetConfigRepository) {
    super(webhookTargetConfigRepository);
  }

  @Override
  public WebhookTargetConfigEntry convertToEntry(WebhookTargetConfig entity) {
    WebhookTargetConfigEntry webhookTargetConfigEntry = super.convertToEntry(entity);
    try {
      // TODO: need to add support for QUEUE and SLACK later
      webhookTargetConfigEntry.setConfig(objectMapper.readValue(entity.getConfig(), EndpointType.HTTP.getEndPointConfigClass()));
    } catch (IOException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    return webhookTargetConfigEntry;
  }

  @Override
  public WebhookTargetConfig convertToEntity(WebhookTargetConfigEntry entry) {

    WebhookTargetConfig webhookTargetConfig = super.convertToEntity(entry);
    try {
      webhookTargetConfig.setConfig(objectMapper.writeValueAsString(entry.getConfig()));
    } catch (JsonProcessingException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    return webhookTargetConfig;
  }
}
