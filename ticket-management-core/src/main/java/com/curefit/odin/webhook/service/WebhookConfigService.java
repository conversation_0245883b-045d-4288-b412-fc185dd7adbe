package com.curefit.odin.webhook.service;

import com.curefit.cf.commons.pojo.BaseEntry;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.model.BaseMySQLEntity;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.pojo.UserEntry;
import com.curefit.odin.admin.service.*;
import com.curefit.odin.enums.EntityEvent;
import com.curefit.odin.enums.WebhookEntity;
import com.curefit.odin.webhook.models.WebhookConfig;
import com.curefit.odin.webhook.pojo.WebhookConfigEntry;
import com.curefit.odin.webhook.pojo.WebhookMeta;
import com.curefit.odin.webhook.pojo.WebhookFilter;
import com.curefit.odin.webhook.pojo.WebhookFilterDetail;
import com.curefit.odin.webhook.repositories.WebhookConfigRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class WebhookConfigService extends BaseMySQLService<WebhookConfig, WebhookConfigEntry> {

  @Autowired
  WebhookTargetConfigService webhookTargetConfigService;

  @Autowired
  CategoryService categoryService;

  @Autowired
  SubCategoryService subCategoryService;

  @Autowired
  UserService userService;

  @Autowired
  TenantService tenantService;

  @Autowired
  RollbarService rollbarService;

  public WebhookConfigService(WebhookConfigRepository webhookConfigRepository) {
    super(webhookConfigRepository);
  }

  @Override
  public WebhookConfig convertToEntity(WebhookConfigEntry entry) {
    WebhookConfig webhookConfig = super.convertToEntity(entry);
    try {
      if (entry.getWebhookEventsMeta() == null) {
        entry.setWebhookEventsMeta(entry.getEvents().entrySet().stream().map(eventEntry -> {
          WebhookMeta webhookMeta = new WebhookMeta();
          webhookMeta.setEntity(eventEntry.getKey());
          webhookMeta.setEvents(eventEntry.getValue().stream().collect(Collectors.toMap(key -> key, value -> new ArrayList<>())));
          return webhookMeta;
        }).collect(Collectors.toList()));
      }
      webhookConfig.setEventsMeta(objectMapper.writeValueAsString(entry.getWebhookEventsMeta()));
      webhookConfig.setFilter(objectMapper.writeValueAsString(entry.getWebhookFilter()));
    } catch (JsonProcessingException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    if (entry.getWebhookTargetConfigEntry() != null) {
      webhookConfig.setWebhookTargetConfig(webhookTargetConfigService.convertToEntity(entry.getWebhookTargetConfigEntry()));
    }
    return webhookConfig;
  }

  @Override
  public WebhookConfigEntry convertToEntry(WebhookConfig entity) {
    WebhookConfigEntry entry = super.convertToEntry(entity);
    try {
      entry.setWebhookEventsMeta(objectMapper.readValue(entity.getEventsMeta(), new TypeReference<List<WebhookMeta>>() {
      }));
      entry.setEvents(entry.getWebhookEventsMeta().stream().collect(Collectors.toMap(WebhookMeta::getEntity, value -> value.getEvents().keySet())));
      entry.setWebhookFilter(objectMapper.readValue(entity.getFilter(), WebhookFilter.class));
    } catch (IOException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    entry.setWebhookTargetConfigEntry(webhookTargetConfigService.convertToEntry(entity.getWebhookTargetConfig()));
    return entry;
  }

  @CacheEvict(value = "allWebhooks", allEntries = true)
  @Override
  @Transactional(rollbackFor = Exception.class)
  public WebhookConfigEntry create(WebhookConfigEntry entry) throws BaseException {
    entry.setWebhookTargetConfigEntry(webhookTargetConfigService.create(entry.getWebhookTargetConfigEntry()));
    return super.create(entry);
  }

  @CacheEvict(value = "allWebhooks", allEntries = true)
  @Override
  @Transactional(rollbackFor = Exception.class)
  public WebhookConfigEntry patchUpdate(Long id, WebhookConfigEntry entry) throws BaseException {
    if (entry.getWebhookTargetConfigEntry() != null) {
      entry.setWebhookTargetConfigEntry(webhookTargetConfigService.patchUpdate(entry.getWebhookTargetConfigEntry().getId(), entry.getWebhookTargetConfigEntry()));
    }
    return super.patchUpdate(id, entry);
  }

  public WebhookConfigEntry fetchDetail(Long id) throws BaseException {
    WebhookConfigEntry webhookConfigEntry = findOneById(id);
    WebhookFilter filterRequest = webhookConfigEntry.getWebhookFilter();
    if (filterRequest != null) {
      webhookConfigEntry.setWebhookFilterDetail(WebhookFilterDetail.builder()
              .tenants(findAllByIds(filterRequest.getTenantIds(), tenantService))
              .categories(findAllByIds(filterRequest.getCategoryIds(), categoryService))
              .subCategories(findAllByIds(filterRequest.getSubCategoryIds(), subCategoryService))
              .assignedUsers(findAllUsersByIds(filterRequest.getAssignedUserIds()))
              .createdBy(findAllUsersByIds(filterRequest.getCreatedBy()))
              .status(filterRequest.getStatus())
              .cities(filterRequest.getCities())
              .fields(filterRequest.getFields())
              .locations(filterRequest.getLocations())
              .priorities(filterRequest.getPriorities())
              .source(filterRequest.getSource())
              .sourceRefIds(filterRequest.getSourceRefIds())
              .createdOnFrom(filterRequest.getCreatedOnFrom())
              .createdOnTo(filterRequest.getCreatedOnTo())
              .dueDateTo(filterRequest.getDueDateTo())
              .dueDateFrom(filterRequest.getDueDateFrom())
              .build());
    }

    return webhookConfigEntry;
  }

  private <T extends BaseMySQLEntity, E extends BaseEntry> List<E> findAllByIds(List<Long> ids, BaseMySQLService<T, E> baseMySQLService) {
    if (ids == null) {
      return null;
    }
    return ids.stream().map(id -> {
      try {
        return baseMySQLService.findOneById(id);
      } catch (BaseException e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
        return null;
      }
    }).filter(Objects::nonNull).collect(Collectors.toList());
  }

  private List<UserEntry> findAllUsersByIds(List<String> ids) {
    if (ids == null) {
      return null;
    }
    return ids.stream().map(id -> {
      try {
        return userService.findUserByMailId(id);
      } catch (BaseException e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
        return null;
      }
    }).filter(Objects::nonNull).collect(Collectors.toList());
  }

  // TODO : fix serialization issue in caching
  //@Cacheable(value = "allWebhooks", unless = "#result == null")
  public List<WebhookConfigEntry> fetchAllWebhooks() {
    try {
      return search(0, -1, null, null, "active.eq:true").getElements();
    } catch (InvalidSeachQueryException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    return Collections.emptyList();
  }
}
