package com.curefit.odin.commons;

import java.util.List;
import java.util.Map;

public class SprinklrConstants {

    public static final String CULT_AUTOMATION_FLAG_FIELD = "cult_automation_flag";
    public static final String CULT_LIVE_CHAT_L1_FIELD = "live_chat_L1";
    public static final String CULT_LIVE_CHAT_SUB_L1_FIELD = "live_chat_sub_L1";
    public static final String CULT_LIVE_CHAT_L2_FIELD = "live_chat_L2";
    public static final String CULT_SESSION_STATUS_FIELD = "session_status";
    public static final String CULT_MEMBERSHIP_STATUS_FIELD = "membership_status";

    public static final String CULT_SESSION = "Session";
    public static final String CULT_MEMBERSHIP = "Membership";
    public static final String CULT_ACCOUNT_SUB_L1_VALUE = "All";
    //sprinklr not sending subL1 and status for account value so setting this internally. 
    public static final String CULT_ACCOUNT_STATUS_VALUE = "User";


    public static final String CULT_AUTOMATION_DEEP_LINK = "cult_automation_deep_link";
    public static final String CULT_AUTOMATION_DEEP_LINK_PREVIEW_IMAGE = "cult_automation_deep_link_preview_image";
    public static final String CULT_AUTOMATION_NO_SHOW_TEMPLATE_ID = "cult_automation_no_show_template_id";
    public static final String SCHEDULING_FLAG = "scheduling_flag";
    public static final String CUREFIT_SCHEDULING_TIME_SLOT_ID="curefit_scheduling_time_slot_id";
    public static final String CUREFIT_SCHEDULING_WORKOUT_ID="curefit_scheduling_workout_id";
    public static final String CUREFIT_SCHEDULING_CENTER_ID="curefit_scheduling_center_id";

    public static final String ODIN_TICKET_ID = "odin_ticket_id";
    public static final String ODIN_STATUS = "odin_status";
    public static final String EXTERNAL_SYSTEM_STATUS = "external_system_status";
    public static final String ODIN_ASSIGNED_TO = "odin_assigned_to";
    public static final String ODIN_SLA = "odin_sla";
    public static final String ODIN_TICKET_DUE_DATE = "odin_ticket_due_date";
    public static final String ODIN_TICKET_LOCATION_ID = "odin_ticket_location_id";
    public static final String ODIN_TICKET_LOCATION_NAME = "odin_ticket_location_name";
    public static final String ODIN_TICKET_RESOLUTION_DATE = "odin_ticket_resolution_date";


    public static final List<String> SPRINKLR_UPDATE_TICKET_ACTIONS = List.of("SYNC_SELECTED_PROPERTIES");

    public static final String CUREFIT_USER_ID = "curefit_user_id";
    public static final String IS_TICKET_FLAG = "is_ticket_flag";

    public static final String YES = "Yes";
    public static final String NO = "No";

    public static final String ODIN_TICKET_CLOSE_FLAG = "odin_ticket_close_flag";
    public static final String ODIN_TICKET_REOPEN_FLAG = "odin_ticket_reopen_flag";

    public static final String AGENT_L2 = "agent_L2";
    public static final String EXTERNAL_SYSTEM_STATUS_PICK_LIST = "external_system_status_pick_list";
    public static final String DESCRIPTION = "description";

    public static final String PENDING_ON_OPS = "Pending on Ops";
    public static final String OPS_RESPONDED = "Ops Responded";
    public static final String OPS_RESOLVED = "Ops Resolved";

    public static final String CULT_CSAT_DEEPLINK = "curefit://ticketcsat?questionId=6&isSprinklrTicket=true&ticketId=";
    public static final String CSAT_DEEPLINK_FIELD = "csat_email_deeplink";


    public static final String TEAM_TO_ASSIGN_ODIN_CASE_FIELD = "team_to_assign_odin_case";
    public static final String AGENT_SKILL_SET_FIELD = "agent_skill_set";
    public static final String CASE_CREATED_VIA_API_FLAG = "case_created_via_api_flag";
    public static final String CULT_USERNAME_FIELD = "curefit_username";
    public static final String CULT_USER_PHONE_NUMBER_FIELD = "curefit_user_phone_number";
    public static final String CULT_KNOWN_CUSTOMER_FLAG = "known_customer_flag";
    public static final String CULT_SUBJECT_FIELD = "subject";
    public static final String WIDGET_VISIBILITY_ON_SPRINKLR_FLAG = "widget_visibility_on_sprinklr_flag";
    public static final String ODIN_L2_VALUE = "odin_l2_value";
    public static final String CUREFIT_ODIN_API_CASE_PICKLIST = "curefit_odin_api_case_picklist";

    public static final String CREATE_MESSAGE_A_ID_EMAIL = "<EMAIL>";

    public static final Map<String, String> SUPPORT_EMAIL_MAP = Map.of(
            "<EMAIL>", "<EMAIL>",
            "<EMAIL>", "<EMAIL>",
            "<EMAIL>", "<EMAIL>",
            "<EMAIL>", "<EMAIL>",
            "<EMAIL>", "<EMAIL>",
            "<EMAIL>", "<EMAIL>",
            "<EMAIL>", "<EMAIL>",
            "<EMAIL>", "<EMAIL>"
    );
    public static final int SCHEDULING_COUNTER_REFRESH_DAYS = 30;
    public static final Long SCHEDULING_TICKET_CREATE_THRESHOLD = 5L;
}
