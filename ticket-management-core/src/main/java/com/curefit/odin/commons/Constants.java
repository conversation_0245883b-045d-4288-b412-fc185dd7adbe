package com.curefit.odin.commons;

import com.curefit.odin.enums.Status;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 */

public class Constants {

  public static final String UPDATED = "updated";
  public static final String ASSIGNEE = "assignee";
  public static final String ASSIGNEES = "assignees";
  public static final String WATCHERS = "watchers";
  public static final String ASSIGNED = "assigned";
  public static final String ACTIVE = "active";
  public static final String ADDED = "added";
  public static final String DELETED = "deleted";
  public static final String UNASSIGNED = "Unassigned";
  public static final String DESCRIPTION = "description";
  public static final String EMPTY_STRING = "";
  public static final String FALSE = "false";
  public static final String COMMENT = "comment";
  public static final String SLA_REMINDER_SENT_AT = "slaReminderSentAt";

  public static final String DEFAULT_COUNTRY_ID = "1";
  public static final String DEFAULT_COUNTRY_NAME = "INDIA";

  public static final String SOURCE = "SOURCE:ODIN";

  public static final String AGENT_NUMBER = "agentNumber";
  public static final String TICKET_ID = "odinTicketId";
  public static final String FRESHDESK_TICKET_ID = "freshdeskTicketId";

  public static final String ODIN_NAMESPACE = "odin";

  public static final String DISABLED_USER = "Disabled User";
  public static final String CUSTOMER_SUPPORT_NAMESPACE = "CUSTOMER_SUPPORT";

  public static final String USER_ID_HEADER = "X_USER_ID";
  public static final String NAMESPACE_HEADER = "X_NAMESPACE";

  public static final String FROM = "from";
  public static final String TO = "to";
  public static final String SUBJECT = "subject";
  public static final String AUTO_SUBMITTED = "auto-submitted";
  public static final String X_AUTOREPLY = "x-autoreply";
  public static final String PRECEDENCE = "precedence";
  public static final List<String> PRECEDENCE_VALUES = Arrays.asList("bulk", "auto_reply", "list");
  public static final String X_AUTO_RESPONSE_SUPRESS = "x-auto-response-suppress";
  public static final List<String> X_AUTO_RESPONSE_SUPRESS_VALUES = Arrays.asList("dr", "autoreply", "all");

  public static final String ODIN_TAG = "[ODIN] ";
  public static final String ODIN_GMAIL_ACCOUNT_NAME = "odin";
  public static final List<String> ODIN_NOTIFICATION_EMAIL_IDS = Arrays.asList("<EMAIL>", "<EMAIL>");

  public static final List<Status> TICKET_COMPLETE_STATUSES = Arrays.asList(Status.REJECTED, Status.RESOLVED);
  public static final List<Status> TICKET_DONE_STATUSES = Collections.singletonList(Status.RESOLVED);

  public static final String EMPLOYEE_DETAILS = "employeeDetails";
  public static final String CALL_SENT = "callSent";

  public static final Long MILLI_SEC_IN_HOUR = 60 * 60 * 1000L;
  public static final Long MILLIS_IN_MINUTE = 60 * 1000L;
  public static final int MINUTES_IN_HOUR = 60;
  public static final int MINUTES_IN_DAY = 24 * 60;

  public static final String METHOD_NOT_SUPPORTED = "Method Not supported";

  public static final String TENANT = "CUREFIT";
  public static final String WATCHMEN_CONTEXT = "All";
  public static final String AUTH_NAMESPACE = "odin";
  public static final String WATCHMEN_ROLE = "USER";
  public static final String WATCHMEN_ALL_CONTEXT = "All";

  public static final Integer SLA_REMINDER_WINDOW_IN_MILLISEC = 60 * 60 * 1000;

  public static final SimpleDateFormat INPUT_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss"); // Ignoring time-zone info
  public static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
  public static final SimpleDateFormat TIME_FORMAT = new SimpleDateFormat("HH:mm:00");
  public static final SimpleDateFormat DATE_TIME_FORMAT = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:00");
  public static final long S3_BUCKET_EXPIRATION_MILLIS = 1000 * 60 * 60;
  public static final int S3_GET_PRE_SIGNED_URL_RETRY = 3;
  public static final Integer TICKET_FILTER_OFFSET = 0;
  public static final Integer TICKET_FILTER_BATCH_SIZE_LIMIT = 100;
  public static final Long INJURY_CATEGORY_ID = 383L;
}
