package com.curefit.odin;

import com.curefit.odin.utils.RequestInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 */

@Configuration
public class WebConfig implements WebMvcConfigurer {

  @Override
  public void addInterceptors(InterceptorRegistry registry) {
    registry.addInterceptor(new RequestInterceptor()).excludePathPatterns("/error/**", "/swagger-*/**", "/webjars/**", "/csrf/**", "/v2/api-docs", "/user/searchV2", "/google/oauth/callback", "/actuator/**");
  }
}
