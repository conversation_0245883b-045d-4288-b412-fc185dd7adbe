package com.curefit.odin.audit.models;

import com.curefit.cf.commons.pojo.audit.EventType;
import com.curefit.commons.sf.model.BaseMySQLEntity;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 */

@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "audit_event")
public class AuditEvent extends BaseMySQLEntity {

  @Column(name = "parent_id")
  String parentId;

  @Column(name = "parent_entity")
  String parentEntity;

  @Column(name = "entity_id")
  String entityId;

  String entity;

  @Column(name = "event_type")
  @Enumerated(value = EnumType.STRING)
  EventType eventType;

  String changes;

  @Column(name = "modified_by")
  String modifiedBy;

  @Column(name = "modified_on")
  Date modifiedOn;
}
