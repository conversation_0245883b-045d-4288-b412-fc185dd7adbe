package com.curefit.odin.audit;

import com.curefit.cf.commons.pojo.audit.Change;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.odin.audit.models.AuditEvent;
import com.curefit.odin.audit.pojo.AuditEventEntry;
import com.curefit.odin.audit.repositories.AuditEventsRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AuditEventsService extends BaseMySQLService<AuditEvent, AuditEventEntry> {

  @Autowired
  RollbarService rollbarService;

  public AuditEventsService(AuditEventsRepository auditEventsRepository) {
    super(auditEventsRepository);
  }

  @Override
  public AuditEvent convertToEntity(AuditEventEntry entry) {
    AuditEvent auditEvent = super.convertToEntity(entry);
    try {
      auditEvent.setChanges(objectMapper.writeValueAsString(entry.getChangeList()));
    } catch (JsonProcessingException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    return auditEvent;
  }

  @Override
  public AuditEventEntry convertToEntry(AuditEvent entity) {
    AuditEventEntry auditEventEntry = super.convertToEntry(entity);
    try {
      auditEventEntry.setChangeList(objectMapper.readValue(entity.getChanges(), new TypeReference<List<Change>>() {
      }));
    } catch (IOException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    return auditEventEntry;
  }
}
