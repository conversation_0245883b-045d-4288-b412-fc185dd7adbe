package com.curefit.odin;

import com.curefit.freshdesk.commons.FreshdeskConfig;
import com.curefit.freshdesk.service.FreshdeskService;
import com.curefit.freshdesk.service.FreshdeskServiceImpl;
import com.curefit.odin.config.OdinConfigurations;
import com.curefit.odin.config.SOSConfigurations;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Configuration
@Component
@Slf4j
@Setter
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class FreshdeskConfiguration {

  @Autowired
  OdinConfigurations odinConfigurations;

  @Autowired
  SOSConfigurations sosConfigurations;

  @Bean(name = "freshdeskService")
  public FreshdeskService freshdeskService() {
    FreshdeskConfig freshdeskConfig = new FreshdeskConfig();
    freshdeskConfig.setBaseUrl(odinConfigurations.getFreshdeskBaseUrl());
    freshdeskConfig.setApiKey(odinConfigurations.getFreshdeskAPIKey());
    return new FreshdeskServiceImpl(freshdeskConfig);
  }


  @Bean(name = "sosFreshdeskService")
  public FreshdeskService sosFreshdeskService() {
    FreshdeskConfig freshdeskConfig = new FreshdeskConfig();
    freshdeskConfig.setBaseUrl(sosConfigurations.getFreshdeskBaseUrl());
    freshdeskConfig.setApiKey(sosConfigurations.getFreshdeskAPIKey());

    return new FreshdeskServiceImpl(freshdeskConfig);
  }

  @Bean(name = "hrFreshdeskService")
  public FreshdeskService hrFreshdeskService() {
    FreshdeskConfig freshdeskConfig = new FreshdeskConfig();
    freshdeskConfig.setBaseUrl(odinConfigurations.getHrFreshdeskBaseUrl());
    freshdeskConfig.setApiKey(odinConfigurations.getHrFreshdeskAPIKey());

    return new FreshdeskServiceImpl(freshdeskConfig);
  }

  @Bean(name = "partnerFreshdeskService")
  public FreshdeskService partnerFreshdeskService() {
    FreshdeskConfig freshdeskConfig = new FreshdeskConfig();
    freshdeskConfig.setBaseUrl(odinConfigurations.getPartnerFreshdeskBaseUrl());
    freshdeskConfig.setApiKey(odinConfigurations.getPartnerFreshdeskAPIKey());

    return new FreshdeskServiceImpl(freshdeskConfig);
  }

  @Bean(name = "sugarfitFreshdeskService")
  public FreshdeskService sugarfitFreshdeskService() {
    FreshdeskConfig freshdeskConfig = new FreshdeskConfig();
    freshdeskConfig.setBaseUrl(odinConfigurations.getSugarfitFreshdeskBaseUrl());
    freshdeskConfig.setApiKey(odinConfigurations.getSugarfitFreshdeskAPIKey());
    return new FreshdeskServiceImpl(freshdeskConfig);
  }
}
