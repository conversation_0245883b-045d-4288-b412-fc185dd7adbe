package com.curefit.odin;

import com.curefit.commons.sf.crypto.ConfidentialityCheckService;
import com.curefit.commons.sf.crypto.ConfidentialityCheckServiceProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 */

@Component
public class EncryptionConfiguration {

  @Autowired
  ConfidentialityCheckService confidentialCheckService;

  @Autowired
  ConfidentialityCheckServiceProvider confidentialCheckServiceProvider;

  @PostConstruct
  public void register() {
    confidentialCheckServiceProvider.setConfidentialityCheckService(confidentialCheckService);
  }
}
