package com.curefit.odin;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import dev.shortloop.agent.ShortloopAutoConfiguration;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.*;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;


@EnableCaching
@SpringBootApplication
@EnableScheduling
@EnableRetry
@EnableSchedulerLock(defaultLockAtMostFor = "PT5M")
@PropertySource("classpath:application-${spring.profiles.active}.properties")
@ComponentScan(basePackages = {"com.curefit.common.rest.client",
        "com.curefit.commons.integrations.rollbar", "com.curefit.commons.sf", "com.curefit.commons.sf.util",
        "com.curefit.commons.server.controller", "com.curefit.iris", "com.curefit.odin",
        "com.curefit.mozart", "com.curefit.freshdesk", "com.curefit.mozart",
        "com.curefit.commons.sf.auth", "com.curefit.gymfit", "com.curefit.reportissues","com.curefit.userservice", "com.curefit.falcon"})
@Slf4j
@Import(ShortloopAutoConfiguration.class)
public class Application {
    public static void main(String[] args) {
        log.info("Starting app");
        SpringApplication.run(Application.class, args);
    }

    @Primary
    @Bean(name = "odinObjectMapper")
    public ObjectMapper odinObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper;
    }
}
  