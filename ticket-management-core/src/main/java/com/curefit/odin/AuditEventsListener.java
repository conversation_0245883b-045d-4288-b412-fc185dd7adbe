package com.curefit.odin;

import com.amazonaws.regions.Regions;
import com.amazonaws.services.sqs.model.Message;
import com.curefit.cf.commons.pojo.audit.ChangeEvent;
import com.curefit.cf.commons.pojo.audit.ChangeLog;
import com.curefit.cf.commons.pojo.audit.Parent;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.consumer.BaseSQSConsumer;
import com.curefit.odin.audit.AuditEventsService;
import com.curefit.odin.audit.pojo.AuditEventEntry;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
@Profile("!local & !alpha")
public class AuditEventsListener extends BaseSQSConsumer {

  @Qualifier(value = "odinObjectMapper")
  @Autowired
  ObjectMapper objectMapper;

  @Autowired
  AuditEventsService auditEventsService;

  @Autowired
  RollbarService rollbarService;

  public AuditEventsListener(ChangeEventConfiguration changeEventConfiguration) {
    super(changeEventConfiguration.getAuditEventsQueue(), Regions.AP_SOUTH_1,
            changeEventConfiguration.getAuditEventsQueueWaitTimeInSec(), changeEventConfiguration.getAuditEventsQueueBatchSize());
  }

  @Override
  public List<Boolean> process(List<Message> messages) {
    return messages.stream().map(message -> {
      log.info("message for audit : {} ", message.getBody());
      try {
        ChangeEvent changeEvent = objectMapper.readValue(message.getBody(), ChangeEvent.class);
        auditEventsService.create(convertToAuditEventEntry(changeEvent));
        return true;
      } catch (Exception e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
      }
      return false;
    }).collect(Collectors.toList());
  }

  private AuditEventEntry convertToAuditEventEntry(ChangeEvent changeEvent) {
    ChangeLog changeLog = changeEvent.getChangeLog();

    AuditEventEntry.AuditEventEntryBuilder auditEventEntryBuilder = AuditEventEntry.builder()
            .entityId(changeLog.getId())
            .entity(changeLog.getEntity())
            .eventType(changeLog.getEventType())
            .changeList(changeLog.getChanges())
            .modifiedOn(new Date(changeLog.getModifiedOn()))
            .modifiedBy(changeEvent.getUserId());

    Parent parent = changeEvent.getParent();
    if (parent != null) {
      auditEventEntryBuilder.parentId(parent.getId())
              .parentEntity(parent.getEntity());
    }

    return auditEventEntryBuilder.build();
  }
}
