package com.curefit.odin.scheduledTasks;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import com.curefit.common.data.exception.BaseException;
import com.curefit.odin.admin.models.DataSourceValue;
import com.curefit.odin.admin.pojo.DataSourceEntry;
import com.curefit.odin.admin.pojo.DataSourceValueEntry;
import com.curefit.odin.admin.service.DataSourceService;
import com.curefit.odin.admin.service.DataSourceValueService;
import com.curefit.odin.utils.service.UpdateHelper;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.core.SchedulerLock;

@Slf4j
@Component
public class DataSourceValueUpdater extends UpdateHelper<DataSourceValue, DataSourceValueEntry> {

  @Autowired
  private DataSourceService datasourceService;

  @Autowired
  private DataSourceValueService dataSourceValueService;


  public DataSourceValueUpdater(DataSourceValueService dataSourceValueService) {
    super(dataSourceValueService);
  }

  @Profile("!local")
  @Scheduled(cron = "0 0 1 ? * * ")
  @SchedulerLock(name = "datasourceUpdation")
  public void refreshData() throws BaseException {
    List<DataSourceEntry> dynamicDataSources = datasourceService.getActiveDynamicDataSources();
    for (DataSourceEntry dataSource : dynamicDataSources) {
      log.info("Updating values for the data source {}", dataSource.getName());
      datasourceService.updateDataSourceValues(dataSource, dataSource.getId());
      List<DataSourceValueEntry> dataSourceValuesInDb =
          dataSourceValueService.getByDataSourceId(dataSource.getId());
      List<DataSourceValueEntry> dataSourceValues = dataSource.getDataSourceValues();
      updateValues(dataSourceValuesInDb, dataSourceValues);
    }
  }
}
