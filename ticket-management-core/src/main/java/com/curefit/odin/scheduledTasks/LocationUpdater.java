package com.curefit.odin.scheduledTasks;

import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.utils.models.Location;
import com.curefit.odin.utils.pojo.LocationEntry;
import com.curefit.odin.utils.pojo.LocationSourceEntry;
import com.curefit.odin.utils.service.LocationService;
import com.curefit.odin.utils.service.LocationSourceService;
import com.curefit.odin.utils.service.UpdateHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class LocationUpdater extends UpdateHelper<Location, LocationEntry> {

    @Autowired
    private LocationSourceService locationSourceService;

    @Autowired
    RollbarService rollbarService;

    public LocationUpdater(LocationService locationService) {
        super(locationService);
    }

    public void refreshLocations() throws BaseException, InvalidSeachQueryException {
        List<LocationSourceEntry> locationSources =
                locationSourceService.search(0, -1, null, null, "syncDurationInHours.gt:0").getElements();
        for (LocationSourceEntry locationSource : locationSources) {
            log.info("Updating values for the location source {}", locationSource.getUrl());
            try {
                List<LocationEntry> dataSourceValuesInDb =
                        ((LocationService) baseMySqlService).getAllByDataSourceId(locationSource.getId());
                List<LocationEntry> dataSourceValues = locationSourceService.getValuesFromUrl(
                        locationSource.getUrl(), locationSource.getHeadersObject(),
                        locationSource.getFieldMapping(), locationSource.getRootPath(), locationSource.getId());
                updateValues(dataSourceValuesInDb, dataSourceValues);
            } catch (Exception e) {
                String message = String.format("Error while refreshLocations from source: - sourceId: %d.  Error message: %s", locationSource.getId(), e.getMessage());
                log.error(message, e);
                rollbarService.error(new Exception(message, e));
            }
        }
    }
}
