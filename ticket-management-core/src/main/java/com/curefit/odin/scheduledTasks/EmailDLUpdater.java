package com.curefit.odin.scheduledTasks;

import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.odin.admin.service.UserService;
import com.curefit.odin.utils.models.EmailDL;
import com.curefit.odin.utils.pojo.EmailDLEntry;
import com.curefit.odin.utils.pojo.GoogleGroup;
import com.curefit.odin.utils.service.BlackListDLService;
import com.curefit.odin.utils.service.DomainService;
import com.curefit.odin.utils.service.EmailDLService;
import com.curefit.odin.utils.service.GoogleGroupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Slf4j
@Component
public class EmailDLUpdater {

  @Autowired
  EmailDLService emailDLService;

  @Autowired
  GoogleGroupService googleGroupService;

  @Autowired
  RollbarService rollbarService;

  @Autowired
  UserService userService;

  @Autowired
  DomainService domainService;

  @Autowired
  BlackListDLService blackListDLService;

  @Value("${email.dl.refresh.enable:false}")
  boolean isDLRefreshEnabled;

  public void refreshEmailDLs() {
    log.info("Email DL refresh job started - {}", isDLRefreshEnabled);
    if (!isDLRefreshEnabled) {
      return;
    }
    Set<String> blackListedDls = blackListDLService.fetchAllBlackListDLs();
    domainService.fetchAll().forEach(domain -> {
      log.info("Email DL refresh started for domain {} ", domain.getName());
      try {
        List<GoogleGroup> groups = googleGroupService.fetchGroups(domain);
        log.info("Fetched {} groups from domain {}", groups.size(), domain.getName());
        groups.stream().filter(group -> !blackListedDls.contains(group.getDl())).forEach(group -> {
          log.info("Processing domain {} group {}", domain.getName(), group.getDl());
          try {
            googleGroupService.updateGroupMembers(domain, group);
            userService.createOrUpdate(group.getDl(), group.getMembers().size());
            List<EmailDL> emailDLMemberEntries = emailDLService.fetchByDL(group.getDl());
            Set<String> membersInDB = emailDLMemberEntries.stream()
                    .map(EmailDL::getEmail)
                    .collect(Collectors.toSet());

            emailDLService.deleteAll(emailDLMemberEntries.stream()
                    .filter(memberEntry -> !group.getMembers().contains(memberEntry.getEmail()))
                    .collect(Collectors.toList()));

            List<EmailDLEntry> emailDLEntries = group.getMembers().stream().filter(member -> !membersInDB.contains(member))
                    .map(member -> new EmailDLEntry(group.getDl(), member))
                    .collect(Collectors.toList());

            if (!emailDLEntries.isEmpty()) {
              emailDLService.bulkCreate(emailDLEntries);
            }

          } catch (Exception e) {
            log.error(e.getMessage(), e);
            rollbarService.error(e);
          }
        });
      } catch (Exception e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
      }
      log.info("Email DL refresh completed for domain {} ", domain.getName());
    });
  }
}
