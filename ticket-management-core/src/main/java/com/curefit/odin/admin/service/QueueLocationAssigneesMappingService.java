package com.curefit.odin.admin.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.models.AssigneeQueue;
import com.curefit.odin.admin.models.QueueLocationAssigneesMapping;
import com.curefit.odin.admin.pojo.QueueLocationAssigneesMappingEntry;
import com.curefit.odin.admin.pojo.UserEntry;
import com.curefit.odin.admin.repositories.LocationAssigneeMappingDao;
import com.curefit.odin.enums.LocationType;
import com.curefit.odin.enums.UserRole;
import com.curefit.odin.user.service.external.NeoService;
import com.curefit.odin.utils.Secured;
import com.curefit.odin.utils.pojo.LocationEntry;
import com.curefit.odin.utils.pojo.LocationHierarchyNode;
import com.curefit.odin.utils.service.LocationHierarchyService;
import com.curefit.odin.utils.service.LocationService;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.curefit.odin.commons.Constants.DISABLED_USER;
import static com.curefit.odin.enums.AssigneeQueueType.DYNAMIC;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class QueueLocationAssigneesMappingService extends BaseMySQLService<QueueLocationAssigneesMapping, QueueLocationAssigneesMappingEntry> {

    @Autowired
    RollbarService rollbarService;

    @Autowired
    AssigneeQueueService assigneeQueueService;

    @Autowired
    LocationHierarchyService locationHierarchyService;

    @Autowired
    LocationService locationService;

    @Autowired
    UserService userService;

    @Autowired
    NeoService neoService;

    public QueueLocationAssigneesMappingService(LocationAssigneeMappingDao locationAssigneeMappingDao) {
        super(locationAssigneeMappingDao);
    }

    @Override
    public QueueLocationAssigneesMappingEntry convertToEntry(QueueLocationAssigneesMapping entity) {
        QueueLocationAssigneesMappingEntry entry = super.convertToEntry(entity);
        entry.setAssigneeQueueId(entity.getAssigneeQueue().getId());
        return entry;
    }

    @Override
    public QueueLocationAssigneesMapping convertToEntity(QueueLocationAssigneesMappingEntry entry) {
        QueueLocationAssigneesMapping entity = super.convertToEntity(entry);
        if (entry.getAssigneeQueueId() != null) {
            try {
                entity.setAssigneeQueue(assigneeQueueService.fetchEntityById(entry.getAssigneeQueueId()));
            } catch (BaseException e) {
                log.error(e.getMessage(), e);
                rollbarService.error(e);
            }
        }
        return entity;
    }

    @Secured(allowedRoles = UserRole.ADMIN)
    public QueueLocationAssigneesMappingEntry fetchByAssigneeQueueId(Long assigneeQueueId) throws BaseException {
        Map<String, Set<String>> locationCodeAssigneesMap = fetchLocationCodeAssigneeMap(assigneeQueueId);
        Long tenantId = assigneeQueueService.fetchTenantIdForAssigneeQueue(assigneeQueueId);
        List<LocationEntry> locationEntries = locationService.getAllAuthorizedCentres(tenantId);

        LocationHierarchyNode locationHierarchyNode = locationHierarchyService.getLocationHierarchyTree(locationEntries);
        updateAssigneesInLocations(locationHierarchyNode, locationCodeAssigneesMap);

        return new QueueLocationAssigneesMappingEntry(assigneeQueueId, locationHierarchyNode);
    }

    @Secured(allowedRoles = UserRole.ADMIN)
    public QueueLocationAssigneesMappingEntry fetchFlatHierarchyByAssigneeQueueId(Long assigneeQueueId) throws BaseException {
        Map<String, Set<String>> locationCodeAssigneesMap = fetchLocationCodeAssigneeMap(assigneeQueueId);
        Long tenantId = assigneeQueueService.fetchTenantIdForAssigneeQueue(assigneeQueueId);
        List<LocationEntry> locationEntries = locationService.getAllAuthorizedCentres(tenantId);

        List locationHierarchyNodes = locationHierarchyService.getFlatLocationHierarchy(locationEntries);
        updateAssigneesInLocations(locationHierarchyNodes, locationCodeAssigneesMap);

        return new QueueLocationAssigneesMappingEntry(assigneeQueueId, locationHierarchyNodes);
    }

    private Map<String, Set<String>> fetchLocationCodeAssigneeMap(Long assigneeQueueId) throws BaseException {
        try {
            List<QueueLocationAssigneesMappingEntry> entries = search(0, -1, null, null, "assigneeQueue.id.eq:" + assigneeQueueId).getElements();
            return entries.stream()
                    .collect(Collectors.toMap(QueueLocationAssigneesMappingEntry::getUniqueCode, QueueLocationAssigneesMappingEntry::getAssignees));
        } catch (InvalidSeachQueryException e) {
            throw new BaseException(e);
        }
    }


    private List<QueueLocationAssigneesMappingEntry> searchByAssigneeQueueId(Long assigneeQueueId) throws BaseException {
        try {
            return search(0, -1, null, null, "assigneeQueue.id.eq:" + assigneeQueueId).getElements();
        } catch (InvalidSeachQueryException e) {
            throw new BaseException(e);
        }
    }

    private void updateAssigneesInLocations(LocationHierarchyNode<Set<UserEntry>> locationHierarchyNode, Map<String, Set<String>> locationCodeAssigneesMap) {
        if (locationHierarchyNode == null) {
            return;
        }
        Set<String> assignees = locationCodeAssigneesMap.get(locationHierarchyNode.getUniqueCode());
        if (assignees != null) {
            locationHierarchyNode.setData(assignees.stream()
                    .filter(assigneeId -> !StringUtils.isBlank(assigneeId))
                    .map(assigneeId -> userService.findUserByMailIdWithDefault(assigneeId))
                    .collect(Collectors.toSet()));
        }
        if (locationHierarchyNode.getChildren() != null) {
            locationHierarchyNode.getChildren().forEach(node -> updateAssigneesInLocations(node, locationCodeAssigneesMap));
        }
    }

    private void updateAssigneesInLocations(List<LocationHierarchyNode<Set<UserEntry>>> locationHierarchyNodes, Map<String, Set<String>> locationCodeAssigneesMap) {
        if (locationHierarchyNodes == null) {
            return;
        }
        locationHierarchyNodes.forEach(locationHierarchyNode -> {
            Set<String> assignees = locationCodeAssigneesMap.get(locationHierarchyNode.getUniqueCode());
            if (assignees != null) {
                locationHierarchyNode.setData(assignees.stream()
                        .filter(assigneeId -> !StringUtils.isBlank(assigneeId))
                        .map(assigneeId -> userService.findUserByMailIdWithDefault(assigneeId))
                        .collect(Collectors.toSet()));
            }
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Secured(allowedRoles = UserRole.ADMIN)
    public QueueLocationAssigneesMappingEntry createOrUpdate(QueueLocationAssigneesMappingEntry assigneesMappingEntry) throws BaseException {
        Map<String, QueueLocationAssigneesMappingEntry> locationCodeQueueLocationsAssigneesMap = new HashMap<>();

        if (assigneesMappingEntry.getLocationsAssignees() != null) {
            populateLocationCodeMap(assigneesMappingEntry.getAssigneeQueueId(), assigneesMappingEntry.getLocationsAssignees(), locationCodeQueueLocationsAssigneesMap);
        } else {
            populateLocationCodeMap(assigneesMappingEntry.getAssigneeQueueId(), assigneesMappingEntry.getLocationHierarchyNodesAssignees(), locationCodeQueueLocationsAssigneesMap);
        }
        Map<String, QueueLocationAssigneesMappingEntry> locationCodeQueueAssigneesMappingEntryMap = searchByAssigneeQueueId(assigneesMappingEntry.getAssigneeQueueId())
                .stream().collect(Collectors.toMap(QueueLocationAssigneesMappingEntry::getUniqueCode, entry -> entry));

        bulkPatchUpdate(locationCodeQueueLocationsAssigneesMap.entrySet().stream().filter(entry -> locationCodeQueueAssigneesMappingEntryMap.containsKey(entry.getKey()))
                .map(entry -> {
                    QueueLocationAssigneesMappingEntry queueLocationAssigneesMappingEntry = locationCodeQueueAssigneesMappingEntryMap.get(entry.getKey());
                    queueLocationAssigneesMappingEntry.setAssignees(entry.getValue().getAssignees());
                    return queueLocationAssigneesMappingEntry;
                })
                .collect(Collectors.toList()));

        bulkCreate(locationCodeQueueLocationsAssigneesMap.entrySet().stream().filter(entry -> !locationCodeQueueAssigneesMappingEntryMap.containsKey(entry.getKey()))
                .map(entry -> {
                    QueueLocationAssigneesMappingEntry entryValue = entry.getValue();
                    return new QueueLocationAssigneesMappingEntry(entryValue.getAssigneeQueueId(), entryValue.getLocationType(), entryValue.getLocationCode(), entryValue.getAssignees());
                }).collect(Collectors.toList()));

        return fetchByAssigneeQueueId(assigneesMappingEntry.getAssigneeQueueId());
    }

    private void populateLocationCodeMap(Long assigneeQueueId, LocationHierarchyNode<Set<UserEntry>> locationHierarchyNode, Map<String, QueueLocationAssigneesMappingEntry> locationCodeQueueLocationsAssigneesMap) {
        if (locationHierarchyNode == null) {
            return;
        }
        locationCodeQueueLocationsAssigneesMap.put(locationHierarchyNode.getUniqueCode(), new QueueLocationAssigneesMappingEntry(assigneeQueueId, locationHierarchyNode.getType(), locationHierarchyNode.getCode(), locationHierarchyNode.getData()
                .stream()
                .map(UserEntry::getEmailId)
                .filter(StringUtils::isNotEmpty)
                .map(String::toLowerCase)
                .collect(Collectors.toSet())));
        if (locationHierarchyNode.getChildren() != null) {
            locationHierarchyNode.getChildren().forEach(node -> populateLocationCodeMap(assigneeQueueId, node, locationCodeQueueLocationsAssigneesMap));
        }
    }

    private void populateLocationCodeMap(Long assigneeQueueId, List<LocationHierarchyNode<Set<UserEntry>>> locationHierarchyNodes, Map<String, QueueLocationAssigneesMappingEntry> locationCodeQueueLocationsAssigneesMap) {
        if (locationHierarchyNodes == null) {
            return;
        }
        locationHierarchyNodes.forEach(locationHierarchyNode ->
                locationCodeQueueLocationsAssigneesMap.put(locationHierarchyNode.getUniqueCode(), new QueueLocationAssigneesMappingEntry(assigneeQueueId, locationHierarchyNode.getType(), locationHierarchyNode.getCode(), locationHierarchyNode.getData()
                        .stream()
                        .map(UserEntry::getEmailId)
                        .filter(StringUtils::isNotEmpty)
                        .map(String::toLowerCase)
                        .collect(Collectors.toSet()))));
    }


    // For User
    public Set<String> fetchByLocationAndQueueId(Long locationId, Long queueId, String email, Boolean checkHierarchy) {
        AssigneeQueue assigneeQueue = null;
        try {
            assigneeQueue = assigneeQueueService.fetchEntityById(queueId);
        } catch (BaseException e) {
            throw new RuntimeException(e);
        }

        if (DYNAMIC.name().equals(assigneeQueue.getType())) {
            if (StringUtils.isEmpty(email)) {
                throw new RuntimeException("Given Email can't be NULL as the associated assigneeQueue is of DYNAMIC type");
            }
            Map<Integer, List<String>> employeeManagersByEmail = null;
            try {
                // TODO(@gagan) remove this hardcode
                // Hardcoding to fetch employee's level 2 manager if sub_category is Skip Level
                if (queueId == 503) {
                    employeeManagersByEmail = neoService.getEmployeeManagersByEmail(email, Collections.singletonList(2));
                } else {
                    employeeManagersByEmail = neoService.getEmployeeManagersByEmail(email, Collections.singletonList(1));
                }
            } catch (BaseException e) {
                throw new RuntimeException(e);
            }
            Set<String> result = new HashSet<>();
            employeeManagersByEmail.forEach((level, managerEmails) -> result.addAll(managerEmails));
            return result;
        } else {
            List<LocationHierarchyNode> locationNodes = null;
            if (locationId != null) {
                try {
                    LocationEntry locationEntry = locationService.findOneById(locationId);
                    locationNodes = locationHierarchyService.getAllLocationNodes(locationEntry);
                } catch (BaseException e) {
                    log.error(e.getMessage(), e);
                    rollbarService.error(e);
                }
            }
            if (locationNodes == null) {
                LocationHierarchyNode locationHierarchyNode = new LocationHierarchyNode();
                locationHierarchyNode.setCode(LocationType.ALL.name());
                locationHierarchyNode.setType(LocationType.ALL);
                locationNodes = Collections.singletonList(locationHierarchyNode);
            }

            if (checkHierarchy) {
                return locationNodes
                        .stream()
                        .map(locationNode -> findByLocationCodeAndQueueId(locationNode.getCode(), locationNode.getType(), queueId))
                        .filter(entry -> entry != null && CollectionUtils.isNotEmpty(entry.getAssignees()))
                        .map(QueueLocationAssigneesMappingEntry::getAssignees)
                        .flatMap(Collection::stream)
                        .collect(Collectors.toSet());
            } else {
                return locationNodes
                        .stream().map(locationNode -> findByLocationCodeAndQueueId(locationNode.getCode(), locationNode.getType(), queueId))
                        .filter(entry -> entry != null && CollectionUtils.isNotEmpty(entry.getAssignees()))
                        .findFirst()
                        .map(QueueLocationAssigneesMappingEntry::getAssignees).orElse(Collections.emptySet());
            }
        }
    }

    private QueueLocationAssigneesMappingEntry findByLocationCodeAndQueueId(String locationCode, LocationType locationType, Long assigneeQueueId) {
        try {
            List<QueueLocationAssigneesMappingEntry> entries = search(0, -1, null, null, "assigneeQueue.id.eq:" + assigneeQueueId + ";locationCode.eq:" + locationCode + ";locationType.eq:" + locationType).getElements();
            if (!entries.isEmpty()) {
                QueueLocationAssigneesMappingEntry entry = entries.getFirst();
                entry.setAssignees(entry.getAssignees().stream()
                        .filter(assigneeEmail -> !DISABLED_USER.equalsIgnoreCase(userService.findUserByMailIdWithDefault(assigneeEmail).getName()))
                        .collect(Collectors.toSet())
                );
                return entry;
            }
        } catch (InvalidSeachQueryException e) {
            log.error(e.getMessage(), e);
            rollbarService.error(e, e.getMessage());
        }
        return null;
    }
}
