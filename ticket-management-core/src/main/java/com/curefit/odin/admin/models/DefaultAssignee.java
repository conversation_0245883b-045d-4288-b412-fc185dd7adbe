package com.curefit.odin.admin.models;

import javax.persistence.*;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Setter
@Getter
@javax.persistence.Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "default_assignee",
    uniqueConstraints = {@UniqueConstraint(columnNames = {"assignee_queue_id", "sub_category_id"}),@UniqueConstraint(columnNames = {"assignee_queue_id", "category_id"})})
public class DefaultAssignee extends BaseMySQLModel {
  /**
  * 
  */
  private static final long serialVersionUID = -6760126565624591674L;

  @ManyToOne
  @JoinColumn(name = "assignee_queue_id", referencedColumnName = "id")
  AssigneeQueue assigneeQueue;

  @Column(name = "sub_category_id")
  Long subCategoryId;

  @Column(name = "category_id")
  Long categoryId;
}
