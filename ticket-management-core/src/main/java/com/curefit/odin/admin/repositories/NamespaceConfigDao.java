package com.curefit.odin.admin.repositories;

import com.curefit.commons.sf.repository.BaseMySQLRepository;
import com.curefit.odin.admin.models.NamespaceConfig;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * <AUTHOR>
 */

@Repository
public interface NamespaceConfigDao extends BaseMySQLRepository<NamespaceConfig> {

  Optional<NamespaceConfig> findByName(String name);
}
