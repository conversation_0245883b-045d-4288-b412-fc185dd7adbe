package com.curefit.odin.admin.models;

import com.curefit.commons.sf.audit.annotation.IgnoreAudit;
import com.curefit.commons.sf.model.BaseMySQLEntity;
import com.curefit.commons.sf.util.HeadersUtils;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.persistence.PreUpdate;

@MappedSuperclass
@Getter
@Setter
@FieldDefaults(level = AccessLevel.PROTECTED)
public abstract class BaseMySQLModel extends BaseMySQLEntity {
    private static final long serialVersionUID = 3108136148890639803L;

    Boolean active;

    @IgnoreAudit
    @Column(name = "last_modified_by")
    String lastModifiedBy;

    public BaseMySQLModel() {
        active = true;
    }

    @PreUpdate
    protected void onUpdate() {
        String requestedUserHeader = HeadersUtils.getCurrentUser();
        lastModifiedBy = (lastModifiedBy == null || lastModifiedBy.isEmpty()) ? requestedUserHeader : lastModifiedBy;
    }
}
