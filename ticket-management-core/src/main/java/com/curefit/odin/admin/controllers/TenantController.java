package com.curefit.odin.admin.controllers;

import java.util.List;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import com.curefit.common.data.exception.BaseException;
import com.curefit.odin.admin.models.Tenant;
import com.curefit.odin.admin.service.TenantService;
import com.curefit.odin.utils.pojo.TenantEntry;

@RestController
@RequestMapping("/tenant")
public class TenantController extends BaseOdinController<Tenant, TenantEntry> {


  public TenantController(TenantService tenantService) {
    super(tenantService);
  }

  @RequestMapping(method = RequestMethod.GET, value = "/filter")
  public ResponseEntity<List<TenantEntry>> filter(
      @RequestHeader("X_NAMESPACE") String namespace) throws BaseException {
      return new ResponseEntity<>(((TenantService) baseMySQLService).fetchAllForNamespace(namespace),
          HttpStatus.OK);
  }
}
