package com.curefit.odin.admin.controllers;

import com.curefit.odin.enums.EscalationLevel;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */

@RestController
@RequestMapping("/escalationLevel")
public class EscalationLevelController {

  @RequestMapping(method = RequestMethod.GET, value = "/all")
  public ResponseEntity<List<EscalationLevel>> fetchAll() {
    return new ResponseEntity<>(Arrays.asList(EscalationLevel.values()), HttpStatus.OK);
  }
}
