package com.curefit.odin.admin.controllers;

import com.curefit.common.data.exception.BaseException;
import com.curefit.odin.admin.models.QueueLocationAssigneesMapping;
import com.curefit.odin.admin.pojo.QueueLocationAssigneesMappingEntry;
import com.curefit.odin.admin.service.QueueLocationAssigneesMappingService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 */

@RestController
@RequestMapping("/queue_location_assignees_mapping")
public class QueueLocationAssigneesMappingController extends BaseOdinController<QueueLocationAssigneesMapping, QueueLocationAssigneesMappingEntry> {

  public QueueLocationAssigneesMappingController(QueueLocationAssigneesMappingService queueLocationAssigneesMappingService) {
    super(queueLocationAssigneesMappingService);
  }

  @RequestMapping(method = RequestMethod.GET)
  public ResponseEntity<QueueLocationAssigneesMappingEntry> fetchByAssigneeQueueId(@RequestParam Long assigneeQueueId) throws BaseException {
    return new ResponseEntity<>(((QueueLocationAssigneesMappingService) baseMySQLService).fetchByAssigneeQueueId(assigneeQueueId), HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET, value = "/flat")
  public ResponseEntity<QueueLocationAssigneesMappingEntry> fetchFlatHierarchyByAssigneeQueueId(@RequestParam Long assigneeQueueId) throws BaseException {
    return new ResponseEntity<>(((QueueLocationAssigneesMappingService) baseMySQLService).fetchFlatHierarchyByAssigneeQueueId(assigneeQueueId), HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.POST, value = "/createOrUpdate")
  public ResponseEntity<QueueLocationAssigneesMappingEntry> createOrUpdate(@RequestBody QueueLocationAssigneesMappingEntry queueLocationAssigneesMappingEntry) throws BaseException {
    return new ResponseEntity<>(((QueueLocationAssigneesMappingService) baseMySQLService).createOrUpdate(queueLocationAssigneesMappingEntry),
            HttpStatus.OK);
  }
}
