package com.curefit.odin.admin.models;

import com.curefit.commons.sf.model.BaseMySQLEntity;
import com.curefit.odin.admin.pojo.EscalationRule;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.hibernate.annotations.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.List;

/**
 * <AUTHOR>
 */

@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "watcher_config")
public class WatcherConfig extends BaseMySQLModel {

    @Column(name = "category_id")
    Long categoryId;

    @Column(name = "sub_category_id")
    Long subCategoryId;

    @Column(name = "watchers_queue_id")
    Long watchersQueueId;
}
