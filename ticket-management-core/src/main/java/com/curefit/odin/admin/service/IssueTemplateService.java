package com.curefit.odin.admin.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.models.IssueTemplate;
import com.curefit.odin.admin.pojo.*;
import com.curefit.odin.admin.repositories.IssueTemplateDAO;
import com.curefit.odin.user.service.TicketService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class IssueTemplateService extends BaseMySQLService<IssueTemplate, IssueTemplateEntry> {

    @Autowired
    private IssueTemplateDAO IssueTemplateDAO;

    @Autowired
    private TicketService ticketService;

    public IssueTemplateService(IssueTemplateDAO crudRepository) {
        super(crudRepository);
    }

    // @Cacheable(value = "activeIssueTemplatesBySubCategoryId")
    public List<IssueTemplateEntry> findActiveBySubCategoryId(Long subCategoryId) {
        List<IssueTemplate> activeIssueTemplates = IssueTemplateDAO.findBySubCategoryIdAndActive(subCategoryId, true);
        return activeIssueTemplates.stream()
                .map(this::convertToEntry)
                .collect(Collectors.toList());
    }

    @Override
    public IssueTemplateEntry patchUpdate(Long id, IssueTemplateEntry entry) throws BaseException {
        IssueTemplateEntry updatedEntry = super.patchUpdate(id, entry);

        boolean shouldRefreshOpenTickets = entry.getRefreshOpenTickets() != null ? entry.getRefreshOpenTickets() : false;
        if (shouldRefreshOpenTickets) {
            try {
                ticketService.refreshOpenTicketsAfterSlaOrAssigneeUpdate(null, null, id);
            } catch (InvalidSeachQueryException e) {
                log.error("error while refreshing open tickets for issueTemplateId {}", id, e);
            }
        }
        return updatedEntry;
    }
}
