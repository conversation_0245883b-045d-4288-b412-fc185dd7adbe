package com.curefit.odin.admin.models;

import com.curefit.commons.sf.model.BaseMySQLEntity;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 */

@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "ticket_approval_config")
public class TicketApprovalConfig extends BaseMySQLModel {

    @Column(name = "category_id")
    Long categoryId;

    @Column(name = "sub_category_id")
    Long subCategoryId;

    @Column(name = "approval_queue_id")
    Long approvalQueueId;
}
