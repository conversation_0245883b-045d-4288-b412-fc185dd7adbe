package com.curefit.odin.admin.models;

import com.curefit.odin.enums.Priority;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import javax.persistence.*;

/**
 * <AUTHOR>
 */

@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "priority_mapping", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"name", "tenant_id", "category_id", "sub_category_id"})})
public class PriorityMapping extends BaseMySQLModel {

  @Enumerated(value = EnumType.STRING)
  Priority name;

  @ManyToOne
  @JoinColumn(name = "category_id", referencedColumnName = "id")
  Category category;

  @ManyToOne
  @JoinColumn(name = "sub_category_id", referencedColumnName = "id")
  SubCategory subCategory;

  @ManyToOne
  @JoinColumn(name = "tenant_id", referencedColumnName = "id")
  Tenant tenant;

  @Column(name = "order_by")
  Integer order;
}
