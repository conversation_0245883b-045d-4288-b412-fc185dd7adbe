package com.curefit.odin.admin.controllers;

import com.curefit.common.data.exception.BaseException;
import com.curefit.odin.admin.models.PriorityMapping;
import com.curefit.odin.admin.pojo.PriorityMappingEntry;
import com.curefit.odin.admin.pojo.PriorityFilterRequestEntry;
import com.curefit.odin.admin.service.PriorityService;
import com.curefit.odin.enums.Priority;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/priority")
public class PriorityController extends BaseOdinController<PriorityMapping, PriorityMappingEntry> {

  public PriorityController(PriorityService priorityService) {
    super(priorityService);
  }

  @RequestMapping(method = RequestMethod.GET, value = "/all")
  public ResponseEntity<List<Priority>> fetchAll() {
    List<Priority> priorities = new ArrayList<>(Arrays.asList(Priority.values()));
    priorities.remove(Priority.NA);
    return new ResponseEntity<>(priorities, HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.POST, value = "/filter")
  public List<PriorityMappingEntry> filterV2(@RequestBody PriorityFilterRequestEntry filterRequest) {
    return ((PriorityService) baseMySQLService).filter(filterRequest);
  }

  @RequestMapping(method = RequestMethod.POST, value = "/filter/active")
  public List<PriorityMappingEntry> filterActiveV2(@RequestBody PriorityFilterRequestEntry filterRequest) {
    return ((PriorityService) baseMySQLService).filterActive(filterRequest);
  }

  @RequestMapping(method = RequestMethod.POST, value = "/createOrUpdate")
  public PriorityMappingEntry createOrUpdate(@RequestBody PriorityMappingEntry priorityMappingEntry) throws BaseException {
    return ((PriorityService) baseMySQLService).createOrUpdate(priorityMappingEntry);
  }
}
