package com.curefit.odin.admin.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.gymfit.client.spi.CenterMachineService;
import com.curefit.gymfit.models.CenterMachine;
import com.curefit.gymfit.models.CenterMachineSearchRequest;
import com.curefit.gymfit.utils.Enums;
import com.curefit.odin.admin.pojo.AssetInfo;
import com.curefit.odin.utils.pojo.LocationEntry;
import com.curefit.odin.utils.service.LocationService;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssetService {

    @Autowired
    CenterMachineService centerMachineService;

    @Autowired
    LocationService locationService;

    @Cacheable(value = "getAssetsByLocationCode", unless = "#result == null")
    public List<AssetInfo> getAssetsByLocationCode(Long locationId) throws BaseException {
        LocationEntry locationEntry = locationService.findOneById(locationId);
        if (locationEntry == null || locationEntry.getCenterServiceRefId() == null) {
            return new ArrayList<>();
        }
        CenterMachineSearchRequest payload = CenterMachineSearchRequest.builder()
                .centerServiceCenterId(Long.valueOf(locationEntry.getCenterServiceRefId()))
                .status(Enums.Status.ACTIVE)
                .build();
        List<CenterMachine> centerMachines = new ArrayList<>(centerMachineService.searchCenterMachines(payload));
        return convertCenterMachineToAssetInfo(centerMachines);
    }

    private List<AssetInfo> convertCenterMachineToAssetInfo(List<CenterMachine> centerMachines) {
        return centerMachines.stream()
                .map(centerMachine -> new AssetInfo(centerMachine.getId().toString(), centerMachine.getDisplayName()))
                .collect(Collectors.toList());
    }
}
