package com.curefit.odin.admin.models;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.curefit.commons.sf.audit.annotation.Audit;
import com.curefit.odin.enums.DataType;
import com.curefit.odin.enums.FieldType;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "field", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"name", "tenant_id", "category_id", "sub_category_id"})})
@Audit
public class CustomField extends BaseMySQLModel {
  /**
   * 
   */
  private static final long serialVersionUID = 6768355293549503161L;
  String name;
  
  @Column(name = "data_type")
  @Enumerated(EnumType.STRING)
  DataType dataType;
  
  @ManyToOne
  @JoinColumn(name = "data_source_id", referencedColumnName = "id")
  DataSource dataSource;
  
  @ManyToOne
  @JoinColumn(name = "category_id", referencedColumnName = "id")
  Category category;
  
  @ManyToOne
  @JoinColumn(name = "sub_category_id", referencedColumnName = "id")
  SubCategory subCategory;
  
  @ManyToOne
  @JoinColumn(name = "tenant_id", referencedColumnName = "id")
  Tenant tenant;
  
  @Column(name = "mandatory")
  boolean isMandatory;
  
  @Column(name = "order_by")
  Long order;

  @Enumerated(EnumType.STRING)
  FieldType type;

  @Column(name = "mandatory_on_closing", columnDefinition = "boolean default false")
  boolean mandatoryOnClosing;

  @Column(name = "show_on_creation", columnDefinition = "boolean default true")
  boolean showOnCreation;

  @Column(name = "show_on_updation", columnDefinition = "boolean default true")
  boolean showOnUpdation;
}
