package com.curefit.odin.admin.models;

import com.curefit.odin.enums.AssigneeQueueSource;
import com.curefit.odin.enums.AssigneeQueueType;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import javax.persistence.*;

/**
 * <AUTHOR>
 */

@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "assignee_queue")
public class AssigneeQueue extends BaseMySQLModel {

  /**
   *
   */
  private static final long serialVersionUID = 7035856205421482107L;

  String name;

  @Enumerated(value = EnumType.STRING)
  AssigneeQueueSource source;

  @Column(name = "tenant_id")
  Long tenantId;

  @Column(name = "type")
  String type;
}
