package com.curefit.odin.admin.controllers;

import com.curefit.common.data.exception.BaseException;
import com.curefit.odin.admin.models.DataSource;
import com.curefit.odin.admin.pojo.DataSourceEntry;
import com.curefit.odin.admin.service.DataSourceService;
import com.curefit.odin.scheduledTasks.DataSourceValueUpdater;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/data_source")
public class DataSourceController extends BaseOdinController<DataSource, DataSourceEntry> {

  public DataSourceController(DataSourceService datasourceService) {
    super(datasourceService);
  }

  @Autowired
  DataSourceValueUpdater dataSourceValueUpdater;


  @RequestMapping(method = RequestMethod.POST, value = "/refresh")
  public void refreshData() throws BaseException {
    dataSourceValueUpdater.refreshData();
  }

  @RequestMapping(method = RequestMethod.GET)
  public ResponseEntity<List<DataSourceEntry>> fetchAllByTenantId(@RequestParam(value = "tenantId") Long id) {
    return new ResponseEntity<>(((DataSourceService) baseMySQLService).fetchAllByTenantId(id), HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET, value = "/active")
  public ResponseEntity<List<DataSourceEntry>> fetchActiveByTenantId(@RequestParam(value = "tenantId") Long id) {
    return new ResponseEntity<>(((DataSourceService) baseMySQLService).fetchActiveByTenantId(id), HttpStatus.OK);
  }
}
