package com.curefit.odin.admin.listener;

import com.amazonaws.regions.Regions;
import com.amazonaws.services.sqs.model.Message;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.consumer.BaseSQSConsumer;
import com.curefit.odin.config.OdinConfigurations;
import com.curefit.odin.user.pojo.TicketEscalationPayload;
import com.curefit.odin.user.service.TicketEscalationService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
@Profile("!local & !alpha")
public class EscalationEventsListener extends BaseSQSConsumer {

  @Autowired
  ObjectMapper objectMapper;

  @Autowired
  RollbarService rollbarService;

  @Autowired
  TicketEscalationService ticketEscalationService;

  public EscalationEventsListener(OdinConfigurations odinConfigurations) {
    super(odinConfigurations.getEscalationEventsQueue(), Regions.AP_SOUTH_1, odinConfigurations.getEscalationEventsQueueWaitTimeInSec(), odinConfigurations.getEscalationEventsQueueBatchSize());
  }

  @Override
  public List<Boolean> process(List<Message> messages) {
    return messages.stream().map(message -> {
      try {
        TicketEscalationPayload payload = objectMapper.readValue(message.getBody(), TicketEscalationPayload.class);
        if (payload.getTicketId() == null) {
          return false;
        }
        ticketEscalationService.handleEscalationEvent(payload);
        return true;
      } catch (Exception e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
        return false;
      }
    }).collect(Collectors.toList());
  }
}
