package com.curefit.odin.admin.repositories;

import java.util.List;
import java.util.Optional;
import org.springframework.stereotype.Repository;
import com.curefit.commons.sf.repository.BaseMySQLRepository;
import com.curefit.odin.admin.models.SubCategory;

@Repository
public interface SubCategoryDAO extends BaseMySQLRepository<SubCategory> {
	Optional<SubCategory> findByNameAndCategoryId(String ticketSubCategoryName, Long id);

	List<SubCategory> findByCategoryId(Long ticketCategoryId);
	
	List<SubCategory> findByCategoryIdAndActive(Long ticketCategoryId, Boolean active);

	Optional<SubCategory> findByCategoryIdAndName(Long categoryId, String subCategoryName);
}
