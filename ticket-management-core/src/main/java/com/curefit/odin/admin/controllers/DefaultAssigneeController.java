package com.curefit.odin.admin.controllers;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.curefit.common.data.exception.BaseException;
import com.curefit.odin.admin.models.DefaultAssignee;
import com.curefit.odin.admin.pojo.DefaultAssigneeEntry;
import com.curefit.odin.admin.service.DefaultAssigneeService;

@RestController
@RequestMapping("/default_assignee")
public class DefaultAssigneeController
        extends BaseOdinController<DefaultAssignee, DefaultAssigneeEntry> {

  public DefaultAssigneeController(DefaultAssigneeService defaultAssigneeService) {
    super(defaultAssigneeService);
  }

  @RequestMapping(method = RequestMethod.DELETE, value = "/{id}")
  public ResponseEntity<DefaultAssigneeEntry> deleteById(@PathVariable Long id)
          throws BaseException {
    return new ResponseEntity<>(((DefaultAssigneeService) baseMySQLService).deleteById(id),
            HttpStatus.OK);
  }

  @GetMapping
  public ResponseEntity<?> fetchDefaultAssignee(
          @RequestParam(required = false) Long locationId,
          @RequestParam(required = false) Long categoryId,
          @RequestParam(required = false) Long subCategoryId,
          @RequestHeader(value = "X_USER_ID", required = false) String email) {

    DefaultAssigneeService defaultAssigneeService = (DefaultAssigneeService) baseMySQLService;
    DefaultAssigneeEntry defaultAssigneeEntry = defaultAssigneeService.fetchDefaultAssignee(locationId, categoryId, subCategoryId, email);

    if (defaultAssigneeEntry == null) {
      return ResponseEntity.ok("{}");
    }

    return ResponseEntity.ok(defaultAssigneeEntry);
  }

}
