package com.curefit.odin.admin.service;

import java.util.ArrayList;
import java.util.List;

import com.curefit.odin.enums.UserRole;
import com.curefit.odin.utils.Secured;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.models.DataSourceValue;
import com.curefit.odin.admin.pojo.DataSourceValueEntry;
import com.curefit.odin.admin.repositories.DataSourceValueDAO;
import com.curefit.odin.user.pojo.FieldEntry;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class DataSourceValueService
    extends BaseMySQLService<DataSourceValue, DataSourceValueEntry> {

  @Autowired
  RollbarService rollbarService;

  @Autowired
  DataSourceService dataSourceService;

  public DataSourceValueService(DataSourceValueDAO dataSourceDao) {
    super(dataSourceDao);
  }
  
  /* (non-Javadoc)
   * @see com.curefit.commons.sf.service.BaseMySQLService#convertToEntity(com.curefit.cf.commons.pojo.BaseEntry)
   */
  @Override
  public DataSourceValue convertToEntity(DataSourceValueEntry entry) {
    DataSourceValue dataSourceValue = super.convertToEntity(entry);
    try {
      if (entry.getDataSourceId() != null) {
        dataSourceValue.setDataSource(dataSourceService.fetchEntityById(entry.getDataSourceId()));
      }
    } catch (BaseException e) {
      log.error(e.getMessage(), e);
    }
    return dataSourceValue;
  }
  
  /* (non-Javadoc)
   * @see com.curefit.commons.sf.service.BaseMySQLService#convertToEntry(com.curefit.commons.sf.model.BaseMySQLEntity)
   */
  @Override
  public DataSourceValueEntry convertToEntry(DataSourceValue entity) {
    // TODO Auto-generated method stub

    DataSourceValueEntry dataSourceValueEntry = super.convertToEntry(entity);
    if (entity.getDataSource() != null) {
      dataSourceValueEntry.setDataSourceId(entity.getDataSource().getId());
    }
    return dataSourceValueEntry;
  }

  @Secured(allowedRoles = UserRole.ADMIN)
  public DataSourceValueEntry deleteById(Long id) throws BaseException {
    log.info("Deactivating DataSourceValue {}", id);
    DataSourceValueEntry dataSourceValueEntry = new DataSourceValueEntry();
    dataSourceValueEntry.setActive(false);
    return super.patchUpdate(id, dataSourceValueEntry);
  }

  @Cacheable(value = "dataSourceById")
  public List<DataSourceValueEntry> getByDataSourceId(Long dataSourceId) throws BaseException {
    try {
      return search(0, -1, null, null, "dataSource.id.eq:" + dataSourceId).getElements();
    } catch (InvalidSeachQueryException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
      throw new BaseException(e);
    }
  }

  @Cacheable(value = "activeDataSourceById")
  public List<DataSourceValueEntry> getActiveByDataSourceId(Long dataSourceId)
      throws BaseException {
    try {
      return search(0, -1, null, null, "dataSource.id.eq:" + dataSourceId + ";active.eq:true")
          .getElements();
    } catch (InvalidSeachQueryException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
      throw new BaseException(e);
    }
  }

  public DataSourceValueEntry getValueFromDataSourceIdForField(Long id) throws BaseException {
    return findOneById(id);
  }

  public DataSourceValueEntry getValueFromActiveDataSourceIdForField(Long id) throws BaseException {
    return findOneById(id);
  }

  public List<FieldEntry> getValuesByDataSourceId(Long dataSourceId) throws BaseException {
    log.info("Getting values for data source id {}", dataSourceId);
    List<FieldEntry> values = new ArrayList<>();
    List<DataSourceValueEntry> dataSourceValues = getActiveByDataSourceId(dataSourceId);
    dataSourceValues
        .forEach(value -> values.add(new FieldEntry(value.getId(), null, value.getValue())));
    return values;
  }

}
