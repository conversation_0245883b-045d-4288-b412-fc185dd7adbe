package com.curefit.odin.admin.service;

import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.odin.admin.models.SLAMapping;
import com.curefit.odin.admin.pojo.BusinessHours;
import com.curefit.odin.admin.pojo.CategoryEntry;
import com.curefit.odin.admin.repositories.SlaMappingDao;
import com.curefit.odin.enums.Priority;
import com.curefit.odin.utils.BusinessHoursUtils;
import com.curefit.odin.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import static com.curefit.odin.commons.Constants.*;

@Service
@Slf4j
public class SLAService {

  @Autowired
  CategoryService categoryService;

  @Autowired
  RollbarService rollbarService;

  @Autowired
  private SlaMappingDao slaMappingDao;

  public List<SLAMapping> convertToSlaMapping(Long categoryId, Long subCategoryId,
                                              Map<Priority, Integer> prioritySlaInHoursMapping) {
    log.info("Converting map to SlaMapping");
    List<SLAMapping> slaMapping = null;
    if (null != prioritySlaInHoursMapping) {
      slaMapping = prioritySlaInHoursMapping.entrySet().stream()
          .map(entry -> new SLAMapping(categoryId, subCategoryId, entry.getKey(), entry.getValue()))
          .collect(Collectors.toList());
    }
    return slaMapping;
  }

  public Map<Priority, Integer> convertToMap(Iterable<SLAMapping> slaMappings) {
    Map<Priority, Integer> map = new HashMap<>();
    if (slaMappings != null) {
      slaMappings.forEach(slaMapping -> map.put(slaMapping.getPriority(), slaMapping.getSlaInHours()));
    }
    return map;
  }

  public List<SLAMapping> getSlaMappings(Long categoryId, Long subCategoryId) {
    List<SLAMapping> slaMappings = null;
    if (null != categoryId) {
      slaMappings = slaMappingDao.findAllByCategoryId(categoryId);
    }
    if (null != subCategoryId) {
      slaMappings = slaMappingDao.findAllBySubCategoryId(subCategoryId);
    }
    return slaMappings;
  }

  public Iterable<SLAMapping> updateSlaMapping(Long categoryId, Long subCategoryId, Map<Priority, Integer> map) {
    log.info("Updating SLA Mappings for categoryId {},subCategoryId {}", categoryId, subCategoryId);
    List<SLAMapping> slaMappings = getSlaMappings(categoryId, subCategoryId);
    for (Entry<Priority, Integer> entry : map.entrySet()) {
      Optional<SLAMapping> mappingInDb = slaMappings.stream()
          .filter(mapping -> mapping.getPriority().equals(entry.getKey()))
          .findFirst();
      if (mappingInDb.isPresent()) {
        log.info("updating slaMapping id {}", mappingInDb.get().getId());
        mappingInDb.get().setSlaInHours(entry.getValue());
      } else if (entry.getValue() != null) {
        log.info("adding new slaMapping with priority :{},sla: {}", entry.getKey(), entry.getValue());
        slaMappings.add(new SLAMapping(categoryId, subCategoryId, entry.getKey(), entry.getValue()));
      }
    }
    return slaMappingDao.saveAll(slaMappings);
  }

  @Cacheable(value = "getSLAInHours", unless = "#result == null")
  public Integer getSLAInHours(Long categoryId, Long subCategoryId, Priority priority) {
    Optional<SLAMapping> slaMapping = Optional.empty();
    if (categoryId != null && subCategoryId != null) {
      List<SLAMapping> slaMappings = slaMappingDao.findAllByCategoryIdOrSubCategoryId(categoryId, subCategoryId);
      slaMappings = slaMappings.stream().filter(s -> s.getPriority().equals(priority)).collect(Collectors.toList());
      slaMapping = slaMappings.stream().filter(s -> s.getSubCategoryId() != null && s.getSubCategoryId().equals(subCategoryId)).findFirst();
      if (slaMapping.isPresent()) {
        return slaMapping.get().getSlaInHours();
      }
      slaMapping = slaMappings.stream().filter(s -> s.getCategoryId() != null && s.getCategoryId().equals(categoryId)).findFirst();
      if (slaMapping.isPresent()) {
        return slaMapping.get().getSlaInHours();
      }
    }
    if (subCategoryId != null) {
      slaMapping = slaMappingDao.findAllBySubCategoryIdAndPriority(subCategoryId, priority);
    }
    if (slaMapping.isEmpty() && categoryId != null) {
      slaMapping = slaMappingDao.findAllByCategoryIdAndPriority(categoryId, priority);
    }
    return slaMapping.map(SLAMapping::getSlaInHours).orElse(24);
  }

  public Long getDueDate(Long categoryId, Long subCategoryId, Priority priority) {
    return getDueDate(System.currentTimeMillis(), categoryId, subCategoryId, priority);
  }
  public Long getDueDate(Long categoryId, Long subCategoryId, Priority priority, Date createdOn) {
    return getDueDate(createdOn.getTime(), categoryId, subCategoryId, priority);
  }

  public Long getDueDate(Long timeInMillies, Long categoryId, Long subCategoryId, Priority priority) {
    int slaInHours = getSLAInHours(categoryId, subCategoryId, priority);
    BusinessHours businessHours = null;
    if (categoryId != null) {
      try {
        CategoryEntry categoryEntry = categoryService.findOneById(categoryId);
        businessHours = categoryEntry.getBusinessHours();
      } catch (Exception e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
      }
    }
    return BusinessHoursUtils.calculateDueDateTime(timeInMillies, (long) slaInHours * MINUTES_IN_HOUR, businessHours, DateUtils.IST_ZONE);
  }

  @Cacheable(value = "getDueDateV2", key = "{#categoryId, #subCategoryId, #priority}", unless = "#result == null")
  public Long getDueDateV2(Long timeInMs, Long categoryId, Long subCategoryId, Priority priority, BusinessHours businessHours) {
    int slaInHours = getSLAInHours(categoryId, subCategoryId, priority);
    return BusinessHoursUtils.calculateDueDateTime(timeInMs, (long) slaInHours * MINUTES_IN_HOUR, businessHours, DateUtils.IST_ZONE);
  }

}
