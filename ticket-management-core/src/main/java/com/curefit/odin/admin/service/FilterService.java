package com.curefit.odin.admin.service;

import java.io.IOException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.odin.admin.models.Filter;
import com.curefit.odin.admin.pojo.FilterEntry;
import com.curefit.odin.admin.repositories.FilterDAO;
import com.curefit.odin.user.pojo.TicketFilterRequestEntry;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class FilterService extends BaseMySQLService<Filter, FilterEntry> {

  @Autowired
  ObjectMapper objectMapper;

  @Autowired
  RollbarService rollbarService;

  public FilterService(FilterDAO filterDAO) {
    super(filterDAO);
  }


  /*
   * (non-Javadoc)
   * 
   * @see
   * com.curefit.commons.sf.service.BaseMySQLService#convertToEntity(com.curefit.cf.commons.pojo.
   * BaseEntry)
   */
  @Override
  public Filter convertToEntity(FilterEntry entry) {
    Filter convertedEntity = super.convertToEntity(entry);
    try {
      convertedEntity.setValue(objectMapper.writeValueAsString(entry.getFilter()));
    } catch (JsonProcessingException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    return convertedEntity;
  }

  /*
   * (non-Javadoc)
   * 
   * @see
   * com.curefit.commons.sf.service.BaseMySQLService#convertToEntry(com.curefit.commons.sf.model.
   * BaseMySQLEntity)
   */
  @Override
  public FilterEntry convertToEntry(Filter entity) {
    FilterEntry convertedEntry = super.convertToEntry(entity);
    try {
      convertedEntry.setFilter(objectMapper.readValue(entity.getValue(), TicketFilterRequestEntry.class));
    } catch (IOException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    return convertedEntry;
  }
}
