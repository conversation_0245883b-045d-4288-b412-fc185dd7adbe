package com.curefit.odin.admin.controllers;

import java.util.List;

import com.curefit.common.data.exception.BaseException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.curefit.odin.admin.models.Category;
import com.curefit.odin.admin.pojo.CategoryEntry;
import com.curefit.odin.admin.service.CategoryService;

@RestController
@RequestMapping("/category")
public class CategoryController extends BaseOdinController<Category, CategoryEntry> {


  public CategoryController(CategoryService categoryService) {
    super(categoryService);
  }

  @RequestMapping(method = RequestMethod.GET)
  public ResponseEntity<List<CategoryEntry>> fetchAllByTenantId(@RequestParam(value = "tenantId") Long id) {
    return new ResponseEntity<>(((CategoryService) baseMySQLService).findByTenantId(id), HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET, value = "/active")
  public ResponseEntity<List<CategoryEntry>> fetchActiveByTenantId(@RequestParam(value = "tenantId") Long id) {
    return new ResponseEntity<>(((CategoryService) baseMySQLService).fetchActiveByTenantId(id), HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET, value = "/filter")
  public ResponseEntity<List<CategoryEntry>> filter(
          @RequestHeader("X_NAMESPACE") String namespace) throws BaseException {
    return new ResponseEntity<>(((CategoryService) baseMySQLService).fetchAllForNamespace(namespace),
            HttpStatus.OK);
  }
}
