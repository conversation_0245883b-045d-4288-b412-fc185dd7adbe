package com.curefit.odin.admin.controllers;

import com.curefit.common.data.exception.BaseException;
import com.curefit.odin.admin.models.AssigneeQueue;
import com.curefit.odin.admin.pojo.AssigneeQueueEntry;
import com.curefit.odin.admin.service.AssigneeQueueService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */

@RestController
@RequestMapping("/assignee_queue")
public class AssigneeQueueController extends BaseOdinController<AssigneeQueue, AssigneeQueueEntry> {

  public AssigneeQueueController(AssigneeQueueService assigneeQueueService) {
    super(assigneeQueueService);
  }

  @RequestMapping(method = RequestMethod.GET)
  public ResponseEntity<List<AssigneeQueueEntry>> fetchForTenant(@RequestParam Long tenantId) throws BaseException {
    return new ResponseEntity<>(((AssigneeQueueService) baseMySQLService).fetchByTenantId(tenantId), HttpStatus.OK);
  }
}
