package com.curefit.odin.admin.models;

import java.util.List;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.curefit.odin.admin.pojo.BusinessHours;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.hibernate.annotations.Type;

@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "category", uniqueConstraints = { @UniqueConstraint(columnNames = { "name", "tenant_id" }) })
public class Category extends BaseMySQLModel {

	/**
	* 
	*/
	private static final long serialVersionUID = 7035856205421482107L;

	@Column(name = "name")
	String name;

	@ManyToOne
	@JoinColumn(name = "tenant_id", referencedColumnName = "id")
	Tenant tenant;

	@Column(name = "max_status_change_time_in_hours")
	Integer maxStatusChangeTime;

	@Type(type = "json")
	@Column(name = "business_hours", columnDefinition = "json")
	BusinessHours businessHours;

	@Column(name = "is_confidential")
	Boolean isConfidential;

	@OneToMany(cascade = CascadeType.ALL)
	@JoinColumn(name = "category_id", referencedColumnName = "id")
	List<SLAMapping> slaMappings;


}
