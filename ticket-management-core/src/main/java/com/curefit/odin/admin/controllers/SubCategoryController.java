package com.curefit.odin.admin.controllers;

import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.curefit.odin.admin.models.SubCategory;
import com.curefit.odin.admin.pojo.SubCategoryEntry;
import com.curefit.odin.admin.service.SubCategoryService;

@RestController
@RequestMapping("/sub_category")
public class SubCategoryController extends BaseOdinController<SubCategory, SubCategoryEntry> {

  public SubCategoryController(SubCategoryService ticketSubCategoryService) {
    super(ticketSubCategoryService);
  }

  @GetMapping
  public ResponseEntity<List<SubCategoryEntry>> findByCategoryId(@RequestParam Long categoryId) {
    return new ResponseEntity<>(((SubCategoryService) baseMySQLService).findByCategoryId(categoryId), HttpStatus.OK);
  }

  @GetMapping(value = "/active")
  public ResponseEntity<List<SubCategoryEntry>> findActiveByCategoryId(@RequestParam Long categoryId) {
    return new ResponseEntity<>(((SubCategoryService) baseMySQLService).findActiveByCategoryId(categoryId), HttpStatus.OK);
  }
}
