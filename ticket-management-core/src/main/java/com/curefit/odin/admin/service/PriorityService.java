package com.curefit.odin.admin.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.models.PriorityMapping;
import com.curefit.odin.admin.pojo.BusinessHours;
import com.curefit.odin.admin.pojo.PriorityMappingEntry;
import com.curefit.odin.admin.pojo.PriorityFilterRequestEntry;
import com.curefit.odin.admin.repositories.PriorityDAO;
import com.curefit.odin.enums.Priority;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
public class PriorityService extends BaseMySQLService<PriorityMapping, PriorityMappingEntry> {

  @Autowired
  private CategoryService categoryService;

  @Autowired
  private SubCategoryService subCategoryService;

  @Autowired
  private TenantService tenantService;

  @Autowired
  RollbarService rollbarService;

  @Autowired
  private SLAService slaService;

  public PriorityService(PriorityDAO priorityDAO) {
    super(priorityDAO);
  }

  private void sanitize(PriorityMappingEntry entry) {
    if (entry.getSubCategoryId() != null) {
      entry.setCategoryId(null);
      entry.setTenantId(null);
    } else if (entry.getCategoryId() != null) {
      entry.setTenantId(null);
    }
  }

  @Override
  public PriorityMappingEntry convertToEntry(PriorityMapping priorityMapping) {
    PriorityMappingEntry entry = super.convertToEntry(priorityMapping);
    BusinessHours businessHours = null;
    if (null != priorityMapping.getCategory()) {
      entry.setCategoryId(priorityMapping.getCategory().getId());
      businessHours = priorityMapping.getCategory().getBusinessHours();
    }
    if (null != priorityMapping.getSubCategory()) {
      entry.setSubCategoryId(priorityMapping.getSubCategory().getId());
    }
    if (null != priorityMapping.getTenant()) {
      entry.setTenantId(priorityMapping.getTenant().getId());
    }
    if (entry.getCategoryId() == null && entry.getSubCategoryId() != null) {
      entry.setCategoryId(priorityMapping.getSubCategory().getCategory().getId());
      businessHours = priorityMapping.getSubCategory().getCategory().getBusinessHours();
    }
    try {
      entry.setDueDate(slaService.getDueDateV2(Instant.now().toEpochMilli(), entry.getCategoryId(), entry.getSubCategoryId(), entry.getName(), businessHours));
    } catch (Exception e) {
      log.error("error while finding sla for entry: {}, error: {}", priorityMapping, e);
    }
    return entry;
  }

  @Override
  public PriorityMapping convertToEntity(PriorityMappingEntry entry) {
    sanitize(entry);
    PriorityMapping priorityMapping = super.convertToEntity(entry);
    Long categoryId = entry.getCategoryId();
    if (null != categoryId) {
      try {
        priorityMapping.setCategory(categoryService.fetchEntityById(categoryId));
      } catch (BaseException e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
      }
    }
    if (null != entry.getSubCategoryId()) {
      try {
        priorityMapping.setSubCategory(subCategoryService.fetchEntityById(entry.getSubCategoryId()));
      } catch (BaseException e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
      }
    }
    if (null != entry.getTenantId()) {
      try {
        priorityMapping.setTenant(tenantService.fetchEntityById(entry.getTenantId()));
      } catch (BaseException e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
      }
    }
    return priorityMapping;
  }

  public List<PriorityMappingEntry> filter(PriorityFilterRequestEntry filterRequest) {
    List<PriorityMappingEntry> entries = new ArrayList<>();
    if (filterRequest.getTenantId() != null) {
      entries = findAllByTenantId(filterRequest.getTenantId());
    }
    if (filterRequest.getCategoryId() != null) {
      entries = overridePriorities(entries, findAllByCategoryId(filterRequest.getCategoryId()));
    }
    if (filterRequest.getSubCategoryId() != null) {
      entries = overridePriorities(entries, findAllBySubCategoryId(filterRequest.getSubCategoryId()));
    }
    entries.sort((o1, o2) -> {
      if (o1.getOrder() == null) {
        return 1;
      }
      if (o2.getOrder() == null) {
        return -1;
      }
      return o1.getOrder() - o2.getOrder();
    });
    return entries;
  }

  public List<PriorityMappingEntry> filterActive(PriorityFilterRequestEntry filterRequest) {
    List<PriorityMappingEntry> priorities = filter(filterRequest);

    Long categoryId = filterRequest.getCategoryId();
    Long subCategoryId = filterRequest.getSubCategoryId();

    return priorities.stream()
            .filter(BaseOdinEntry::getActive)
            .peek(priority -> priority.setDueDate(slaService.getDueDate(categoryId, subCategoryId, priority.getName())))
            .collect(Collectors.toList());
  }

  private List<PriorityMappingEntry> overridePriorities(List<PriorityMappingEntry> priorities, List<PriorityMappingEntry> overridePriorities) {
    Map<Priority, PriorityMappingEntry> priorityMap = priorities.stream()
            .collect(Collectors.toMap(PriorityMappingEntry::getName, priority -> priority));

    overridePriorities.forEach(overrideField -> {
      if (priorityMap.containsKey(overrideField.getName())) {
        priorities.remove(priorityMap.get(overrideField.getName()));
      }
    });
    overridePriorities.addAll(priorities);
    return overridePriorities;
  }

  private List<PriorityMappingEntry> findAllByTenantId(Long tenantId) {
    return search("tenant.id.eq:" + tenantId);
  }

  private List<PriorityMappingEntry> findAllByCategoryId(Long categoryId) {
    return search("category.id.eq:" + categoryId);
  }

  private List<PriorityMappingEntry> findAllBySubCategoryId(Long categoryId) {
    return search("subCategory.id.eq:" + categoryId);
  }

  private List<PriorityMappingEntry> search(String query) {
    try {
      return search(0, -1, "order", Sort.Direction.ASC.name(), query).getElements();
    } catch (InvalidSeachQueryException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    return Collections.emptyList();
  }

  public PriorityMappingEntry createOrUpdate(PriorityMappingEntry entry) throws BaseException {
    PriorityMappingEntry priorityMappingEntry = findOneById(entry.getId());
    PriorityMappingEntry updateEntry = createOrUpdate(entry.getSubCategoryId(), priorityMappingEntry.getSubCategoryId(), entry.getId(), entry);
    if (updateEntry == null) {
      updateEntry = createOrUpdate(entry.getCategoryId(), priorityMappingEntry.getCategoryId(), entry.getId(), entry);
    }
    if (updateEntry == null) {
      updateEntry = createOrUpdate(entry.getTenantId(), priorityMappingEntry.getTenantId(), entry.getId(), entry);
    }
    return updateEntry;
  }

  private PriorityMappingEntry createOrUpdate(Long requestId, Long dbId, Long id, PriorityMappingEntry priorityMappingEntry) throws BaseException {
    if (requestId != null) {
      if (dbId != null) {
        return patchUpdate(id, priorityMappingEntry);
      } else {
        priorityMappingEntry.setId(null);
        return create(priorityMappingEntry);
      }
    }
    return null;
  }
}



