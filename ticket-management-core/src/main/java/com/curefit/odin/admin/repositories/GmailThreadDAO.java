package com.curefit.odin.admin.repositories;

import com.curefit.commons.sf.repository.BaseMySQLRepository;
import com.curefit.odin.utils.models.GmailThread;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * <AUTHOR>
 */

@Repository
public interface GmailThreadDAO extends BaseMySQLRepository<GmailThread> {

  Optional<GmailThread> findByThreadId(String threadId);

  Optional<GmailThread> findByTicketId(String ticketId);
}
