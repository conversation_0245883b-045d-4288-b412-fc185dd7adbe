package com.curefit.odin.admin.controllers;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.curefit.odin.admin.models.NamespaceTenantsMapping;
import com.curefit.odin.admin.service.NamespaceTenantsMappingService;
import com.curefit.odin.utils.pojo.NamespaceTenantsMappingEntry;

@RestController
@RequestMapping("/tenant_namespace")
public class NamespaceTenantMapController
    extends BaseOdinController<NamespaceTenantsMapping, NamespaceTenantsMappingEntry> {


  public NamespaceTenantMapController(NamespaceTenantsMappingService tenantNamespaceService) {
    super(tenantNamespaceService);
  }

 
}
