package com.curefit.odin.admin.repositories;

import com.curefit.commons.sf.repository.BaseMySQLRepository;
import com.curefit.odin.admin.models.Category;
import com.curefit.odin.admin.models.IssueTemplate;
import com.curefit.odin.admin.models.SubCategory;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IssueTemplateDAO extends BaseMySQLRepository<IssueTemplate> {

	List<IssueTemplate> findBySubCategoryIdAndActive(Long subCategoryId, Boolean active);

	@Query(value = "SELECT DISTINCT c\n" +
				   "FROM IssueTemplate it\n" +
				   "         JOIN SubCategory sc ON it.subCategoryId = sc.id AND sc.active = true\n" +
				   "         JOIN Category c ON sc.category.id = c.id AND c.tenant.id = :tenantId AND c.active = true\n" +
				   "WHERE it.active = true"
	)
	List<Category> getCategoriesByTenantId(Long tenantId);

	@Query(value = "SELECT DISTINCT sc\n" +
				   "FROM IssueTemplate it\n" +
				   "         JOIN SubCategory sc ON it.subCategoryId = sc.id AND sc.active = true\n" +
				   "WHERE it.active = true AND sc.category.id = :categoryId"
	)
	List<SubCategory> getSubCategoriesByCategoryId(Long categoryId);
}
