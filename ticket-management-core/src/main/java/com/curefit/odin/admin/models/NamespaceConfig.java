package com.curefit.odin.admin.models;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 * <AUTHOR>
 */

@Getter
@Setter
@javax.persistence.Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "namespace_config",
        uniqueConstraints = {@UniqueConstraint(columnNames = {"name"})})
public class NamespaceConfig extends BaseMySQLModel {

  String name;

  @Column(name = "base_url")
  String baseUrl;

}
