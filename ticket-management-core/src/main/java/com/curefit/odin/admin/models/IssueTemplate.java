package com.curefit.odin.admin.models;

import com.curefit.odin.enums.Priority;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import javax.persistence.*;

@Getter
@Setter
@Entity
@Table(name = "issue_template", uniqueConstraints = {@UniqueConstraint(columnNames = {"title", "sub_category_id"})})
@FieldDefaults(level = AccessLevel.PRIVATE)
public class IssueTemplate extends BaseMySQLModel {

    /**
     *
     */
    private static final long serialVersionUID = -1896388606518420721L;

    String title;

    @Column(name = "sub_category_id")
    Long subCategoryId;

    @Enumerated(EnumType.STRING)
    Priority priority;
}
