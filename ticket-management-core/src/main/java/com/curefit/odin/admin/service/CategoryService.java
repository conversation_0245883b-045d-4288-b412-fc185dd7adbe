package com.curefit.odin.admin.service;

import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.repositories.IssueTemplateDAO;
import com.curefit.odin.enums.Status;
import com.curefit.odin.enums.UserRole;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.odin.admin.models.Category;
import com.curefit.odin.admin.models.SLAMapping;
import com.curefit.odin.admin.models.Tenant;
import com.curefit.odin.admin.pojo.*;
import com.curefit.odin.admin.repositories.CategoryDAO;
import com.curefit.odin.enums.Priority;
import com.curefit.odin.user.pojo.TicketEntry;
import com.curefit.odin.user.service.TicketService;
import com.curefit.odin.utils.Secured;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CategoryService extends BaseMySQLService<Category, CategoryEntry> {

    @Autowired
    private TenantService tenantService;

    @Autowired
    private SLAService slaService;

    @Autowired
    RollbarService rollbarService;

    @Autowired
    DefaultAssigneeService defaultAssigneeService;

    @Autowired
    EscalationConfigService escalationConfigService;

    @Autowired
    PriorityService priorityService;

    @Autowired
    WatcherConfigService watcherConfigService;

    @Autowired
    TicketApprovalConfigService ticketApprovalConfigService;

    @Autowired
    SubCategoryService subCategoryService;

    @Autowired
    NamespaceSubCategoryMappingService namespaceSubCategoryMappingService;

    @Autowired
    IssueTemplateDAO IssueTemplateDao;

    @Autowired
    TicketService ticketService;

    public CategoryService(CategoryDAO categoryDAO) {
        super(categoryDAO);
    }

    @Override
    public CategoryEntry convertToEntry(Category category) {
        CategoryEntry categoryEntry = super.convertToEntry(category);
        Tenant tenant = category.getTenant();
        if (null != tenant) {
            categoryEntry.setTenantId(tenant.getId());
        }
        Map<Priority, Integer> slaMapping = slaService.convertToMap(slaService.getSlaMappings(category.getId(), null));
        categoryEntry.setPrioritySLAEntries(fetchPrioritySLAEntries(category.getTenant().getId(), category.getId(), slaMapping));

        DefaultAssigneeEntry defaultAssigneeEntry = defaultAssigneeService.fetchDefaultAssignee(category.getId(), null);
        if (defaultAssigneeEntry != null) {
            categoryEntry.setAssigneeQueueEntry(defaultAssigneeEntry.getAssigneeQueueEntry());
        }
        EscalationConfigEntry escalationConfigEntry = escalationConfigService.fetchEscalationConfig(categoryEntry.getId(), null);
        if (escalationConfigEntry != null) {
            categoryEntry.setEscalationRules(escalationConfigEntry.getEscalationRules());
        }

        WatcherConfigEntry watcherConfigEntry = watcherConfigService.fetchWatcherConfig(categoryEntry.getId(), null);
        if (watcherConfigEntry != null) {
            categoryEntry.setWatchersQueue(watcherConfigEntry.getWatchersQueue());
        }

        TicketApprovalConfigEntry approvalConfigEntry = ticketApprovalConfigService.fetchApprovalConfig(categoryEntry.getId(), null);
        if (approvalConfigEntry != null) {
            categoryEntry.setApprovalQueue(approvalConfigEntry.getApprovalQueue());
        }

        return categoryEntry;
    }

    @Override
    public Category convertToEntity(CategoryEntry entry) {
        Category convertedEntity = super.convertToEntity(entry);
        Long entityId = entry.getTenantId();
        if (entityId != null) {
            try {
                convertedEntity.setTenant(tenantService.fetchEntityById(entityId));
            } catch (BaseException e) {
                log.error(e.getMessage(), e);
                rollbarService.error(e);
            }
        }
        return convertedEntity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Secured(allowedRoles = UserRole.ADMIN)
    @CacheEvict(value = {"category", "activeCategory"}, allEntries = true)
    public CategoryEntry create(CategoryEntry entry) throws BaseException {
        CategoryEntry categoryEntry = super.create(entry);
        if (entry.getPrioritySLAEntries() != null) {
            updatePriorityMappingAndSLA(categoryEntry, entry.getPrioritySLAEntries());
        }
        categoryEntry.setAssigneeQueueEntry(createOrUpdateDefaultAssignee(categoryEntry.getId(), entry.getAssigneeQueueEntry()));
        categoryEntry.setEscalationRules(createOrUpdateEscalationRules(categoryEntry.getId(), entry.getEscalationRules()));
        categoryEntry.setWatchersQueue(createOrUpdateWatcherConfig(categoryEntry.getId(), entry.getWatchersQueue()));
        categoryEntry.setApprovalQueue(createOrUpdateApprovalConfig(categoryEntry.getId(), entry.getApprovalQueue()));
        return categoryEntry;
    }

    @Transactional(rollbackFor = Exception.class)
    @Secured(allowedRoles = UserRole.ADMIN)
    @CacheEvict(value = {"category", "activeCategory", "categoryById"}, allEntries = true)
    public CategoryEntry patchUpdate(Long id, CategoryEntry entry) throws BaseException {
        CategoryEntry categoryEntry = super.patchUpdate(id, entry);
        categoryEntry.setAssigneeQueueEntry(createOrUpdateDefaultAssignee(categoryEntry.getId(), entry.getAssigneeQueueEntry()));
        categoryEntry.setEscalationRules(createOrUpdateEscalationRules(categoryEntry.getId(), entry.getEscalationRules()));
        categoryEntry.setWatchersQueue(createOrUpdateWatcherConfig(categoryEntry.getId(), entry.getWatchersQueue()));
        categoryEntry.setApprovalQueue(createOrUpdateApprovalConfig(categoryEntry.getId(), entry.getApprovalQueue()));

        if (entry.getPrioritySLAEntries() != null) {
            updatePriorityMappingAndSLA(categoryEntry, entry.getPrioritySLAEntries());
        }
        boolean shouldRefreshOpenTickets = entry.getRefreshOpenTickets() != null ? entry.getRefreshOpenTickets() : false;
        if (shouldRefreshOpenTickets) {
            try {
                ticketService.refreshOpenTicketsAfterSlaOrAssigneeUpdate(id,null,null);
            } catch (InvalidSeachQueryException e) {
                log.error("error while refreshing open tickets for categoryId {}", id, e);
            }
        }
        return categoryEntry;
    }


    private void updatePriorityMappingAndSLA(CategoryEntry entry, List<CustomPrioritySLAEntry> prioritySLAEntries) {
        Map<Priority, Integer> prioritySlaInHoursMapping = prioritySLAEntries
                .stream()
                .filter(prioritySLAEntry -> prioritySLAEntry.getSla() != null)
                .collect(Collectors.toMap(prioritySLAEntry -> prioritySLAEntry.getPriorityEntry().getName(), CustomPrioritySLAEntry::getSla));
        Iterable<SLAMapping> slaMappings = slaService.updateSlaMapping(entry.getId(), null, prioritySlaInHoursMapping);
        prioritySLAEntries.forEach(prioritySLAEntry -> {
            try {
                prioritySLAEntry.getPriorityEntry().setCategoryId(entry.getId());
                priorityService.createOrUpdate(prioritySLAEntry.getPriorityEntry());
            } catch (BaseException e) {
                log.error(e.getMessage(), e);
                rollbarService.error(e);
            }
        });
        entry.setPrioritySLAEntries(fetchPrioritySLAEntries(entry.getTenantId(), entry.getId(), slaService.convertToMap(slaMappings)));
    }

    private List<CustomPrioritySLAEntry> fetchPrioritySLAEntries(Long tenantId, Long categoryId, Map<Priority, Integer> slaMapping) {
        PriorityFilterRequestEntry priorityFilterRequestEntry = new PriorityFilterRequestEntry();
        priorityFilterRequestEntry.setTenantId(tenantId);
        priorityFilterRequestEntry.setCategoryId(categoryId);
        List<PriorityMappingEntry> customPriorityEntries = priorityService.filter(priorityFilterRequestEntry);
        return customPriorityEntries
                .stream()
                .map(customPriorityEntry -> new CustomPrioritySLAEntry(customPriorityEntry, slaMapping.getOrDefault(customPriorityEntry.getName(), null)))
                .collect(Collectors.toList());
    }

    private AssigneeQueueEntry createOrUpdateDefaultAssignee(Long categoryId, AssigneeQueueEntry assigneeQueueEntry) throws BaseException {
        if (assigneeQueueEntry == null) {
            return null;
        }
        DefaultAssigneeEntry defaultAssigneeEntry = new DefaultAssigneeEntry();
        defaultAssigneeEntry.setCategoryId(categoryId);
        defaultAssigneeEntry.setAssigneeQueueEntry(assigneeQueueEntry);
        return defaultAssigneeService.createOrUpdate(defaultAssigneeEntry).getAssigneeQueueEntry();
    }

    private List<EscalationRule> createOrUpdateEscalationRules(Long categoryId, List<EscalationRule> escalationRules) throws BaseException {
        if (escalationRules == null) {
            return null;
        }
        EscalationConfigEntry escalationConfigEntry = new EscalationConfigEntry();
        escalationConfigEntry.setCategoryId(categoryId);
        escalationConfigEntry.setEscalationRules(escalationRules);
        return escalationConfigService.createOrUpdate(escalationConfigEntry).getEscalationRules();
    }

    private AssigneeQueueEntry createOrUpdateWatcherConfig(Long categoryId, AssigneeQueueEntry watcherQueue) throws BaseException {
        if (watcherQueue == null) {
            return null;
        }
        WatcherConfigEntry watcherConfigEntry = new WatcherConfigEntry();
        watcherConfigEntry.setCategoryId(categoryId);
        watcherConfigEntry.setWatchersQueue(watcherQueue);
        return watcherConfigService.createOrUpdate(watcherConfigEntry).getWatchersQueue();
    }

    private AssigneeQueueEntry createOrUpdateApprovalConfig(Long categoryId, AssigneeQueueEntry approvalQueue) throws BaseException {
        if (approvalQueue == null) {
            return null;
        }
        TicketApprovalConfigEntry ticketApprovalConfigEntry = new TicketApprovalConfigEntry();
        ticketApprovalConfigEntry.setCategoryId(categoryId);
        ticketApprovalConfigEntry.setApprovalQueue(approvalQueue);
        return ticketApprovalConfigService.createOrUpdate(ticketApprovalConfigEntry).getApprovalQueue();
    }

    @Override
    @Cacheable(value = "categoryById", unless = "#result == null")
    public CategoryEntry findOneById(Long id) throws BaseException {
        return super.findOneById(id);
    }

    @Cacheable(value = "category", unless = "#result == null")
    public List<CategoryEntry> findByTenantId(Long id) {
        List<Category> categories = ((CategoryDAO) baseMySQLRepository).findByTenantId(id);
        return categories.stream().map(this::convertToEntry).collect(Collectors.toList());
    }

    @Cacheable(value = "activeCategory", unless = "#result == null")
    public List<CategoryEntry> fetchActiveByTenantId(Long id) {
        List<Category> categories = ((CategoryDAO) baseMySQLRepository).findByTenantIdAndActive(id, true);
        return categories.stream().map(this::convertToEntry)
                .collect(Collectors.toList());
    }

    public List<CategoryEntry> findAllById(List<Long> ids) {
        List<Category> categories = (List<Category>) ((CategoryDAO) baseMySQLRepository).findAllById(ids);
        return categories.stream()
                .map(this::convertToEntry)
                .collect(Collectors.toList());
    }

    public CategoryEntry fetchByTenantIdAndName(Long tenantId, String name) {
        Optional<Category> categoryOptional = ((CategoryDAO) super.baseMySQLRepository).findByTenantIdAndName(tenantId, name);
        return categoryOptional.map(this::convertToEntry).orElse(null);
    }

    public List<CategoryEntry> fetchAllForNamespace(String namespace) throws BaseException {
        List<Long> subCategoryIdsByNamespace = namespaceSubCategoryMappingService.getSubCategoryIdsByNamespace(namespace);
        List<SubCategoryEntry> subCategoryEntryList = subCategoryService.findAllById(subCategoryIdsByNamespace);
        return findAllById(subCategoryEntryList.stream()
                .map(SubCategoryEntry::getCategoryId)
                .collect(Collectors.toList())
        );
    }

    @Cacheable(value = "categoriesForIssueTemplates", unless = "#result == null")
    public List<CategoryEntry> fetchCategoriesForIssueTemplates(Long tenantId) {
        return IssueTemplateDao.getCategoriesByTenantId(tenantId)
                .stream()
                .map(this::convertToEntry)
                .collect(Collectors.toList());
    }

    @Cacheable(value = "subCategoriesForIssueTemplates", unless = "#result == null")
    public List<SubCategoryEntry> fetchSubCategoriesForIssueTemplates(Long categoryId) {
        return IssueTemplateDao.getSubCategoriesByCategoryId(categoryId)
                .stream()
                .map(subCategoryService::convertToEntry)
                .collect(Collectors.toList());
    }
}
