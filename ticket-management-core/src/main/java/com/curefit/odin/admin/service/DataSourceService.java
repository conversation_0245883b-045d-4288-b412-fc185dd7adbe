package com.curefit.odin.admin.service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import com.curefit.odin.enums.UserRole;
import com.curefit.odin.utils.Secured;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.rest.client.CommonHttpHelper;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.models.DataSource;
import com.curefit.odin.admin.pojo.DataSourceEntry;
import com.curefit.odin.admin.pojo.DataSourceValueEntry;
import com.curefit.odin.admin.repositories.DataSourceDAO;
import com.curefit.odin.utils.pojo.LocationEntry;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONArray;
import org.springframework.transaction.annotation.Transactional;


@Service
@Slf4j
public class DataSourceService extends BaseMySQLService<DataSource, DataSourceEntry> {

  @Autowired
  private CommonHttpHelper httpHelper;

  @Autowired
  DataSourceValueService dataSourceValueService;

  @Autowired
  RollbarService rollbarService;

  public DataSourceService(DataSourceDAO dataSourceDao) {
    super(dataSourceDao);
  }

  @Override
  public DataSource convertToEntity(DataSourceEntry entry) {
    DataSource convertedEntity = super.convertToEntity(entry);
    try {
      if (entry.getMappingObject() != null) {
        convertedEntity.setKeyMapping(objectMapper.writeValueAsString(entry.getMappingObject()));
      }
      if (entry.getHeaderObject() != null) {
        convertedEntity.setHeaders(objectMapper.writeValueAsString(entry.getHeaderObject()));
      }
      convertedEntity.setStatic(entry.isStaticList());
      convertedEntity.setDataSourceValues(null);
    } catch (JsonProcessingException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    return convertedEntity;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  @Secured(allowedRoles = UserRole.ADMIN)
  public DataSourceEntry create(DataSourceEntry entry) throws BaseException {
    DataSourceEntry dataSourceEntry = super.create(entry);
    if (entry.isStaticList() && CollectionUtils.isNotEmpty(entry.getDataSourceValues())) {
      dataSourceEntry.setDataSourceValues(dataSourceValueService.bulkCreate(entry.getDataSourceValues().stream()
              .peek(dsv -> dsv.setDataSourceId(dataSourceEntry.getId()))
              .collect(Collectors.toList())));
    }
    return dataSourceEntry;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  @Secured(allowedRoles = UserRole.ADMIN)
  public DataSourceEntry patchUpdate(Long id, DataSourceEntry entry) throws BaseException {
    if (CollectionUtils.isNotEmpty(entry.getDataSourceValues())) {
      DataSource dataSource = fetchEntityById(id);
      List<DataSourceValueEntry> dataSourceValueEntries = entry.getDataSourceValues().stream()
              .peek(dataSourceValue -> dataSourceValue.setDataSourceId(dataSource.getId()))
              .collect(Collectors.toList());

      dataSourceValueService.bulkPatchUpdate(dataSourceValueEntries.stream()
              .filter(dataSourceValue -> dataSourceValue.getId() != null)
              .collect(Collectors.toList()));

      dataSourceValueService.bulkCreate(dataSourceValueEntries.stream()
              .filter(dataSourceValue -> dataSourceValue.getId() == null)
              .collect(Collectors.toList()));
    }
    entry.setDataSourceValues(null);
    return super.patchUpdate(id, entry);
  }

  @Override
  public DataSourceEntry convertToEntry(DataSource entity) {
    DataSourceEntry convertedEntry = super.convertToEntry(entity);
    try {
      if (entity.getHeaders() != null) {
        convertedEntry.setHeaderObject(objectMapper.readValue(entity.getHeaders(),
                new TypeReference<HashMap<String, String>>() {
                }));
      }
      if (entity.getKeyMapping() != null) {
        convertedEntry.setMappingObject(objectMapper.readValue(entity.getKeyMapping(),
                new TypeReference<HashMap<String, String>>() {
                }));
      }
      convertedEntry.setStaticList(entity.isStatic());
      if (convertedEntry.isStaticList() && CollectionUtils.isNotEmpty(convertedEntry.getDataSourceValues())) {
        convertedEntry.setDataSourceValues(entity.getDataSourceValues().stream()
                .map(dsv -> dataSourceValueService.convertToEntry(dsv)).collect(Collectors.toList()));
      }
    } catch (IOException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    return convertedEntry;
  }


  public List<DataSourceEntry> getActiveDynamicDataSources() throws BaseException {
    try {
      return search(0, -1, null, null, "isStatic.eq:false;active.eq:true").getElements();
    } catch (InvalidSeachQueryException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
      throw new BaseException(e.getMessage());
    }
  }


  public void updateDataSourceValues(DataSourceEntry entry, Long id) {
    log.info("updating datasource values in data source entry ");
    List<DataSourceValueEntry> dataSourceValues;
    if (entry.isStaticList() && CollectionUtils.isNotEmpty(entry.getDataSourceValues())) {
      dataSourceValues = entry.getDataSourceValues();
    } else {
      dataSourceValues = getValuesFromUrl(entry.getUrl(), entry.getHeaderObject(),
              entry.getMappingObject(), entry.getRootPath(), id);
    }
    entry.setDataSourceValues(dataSourceValues);
    log.info("The latest values for the datasource are {}", dataSourceValues);
  }

  private List<DataSourceValueEntry> parseResponse(String json, String centerFieldMap,
                                                   Map<String, String> centersFieldsMap) {
    List<DataSourceValueEntry> listOfTargets = new ArrayList<>();
    Object document = Configuration.defaultConfiguration()
            .addOptions(Option.DEFAULT_PATH_LEAF_TO_NULL).jsonProvider().parse(json);
    JSONArray centers = JsonPath.read(document, centerFieldMap);
    for (Object elementDoc : centers) {
      ObjectNode centerObj = objectMapper.createObjectNode();
      for (Map.Entry<String, String> formQuestionFieldsEntries : centersFieldsMap.entrySet()) {
        String key = formQuestionFieldsEntries.getKey();
        String value = formQuestionFieldsEntries.getValue();
        try {
          centerObj.put(key, JsonPath.read(elementDoc, value).toString());
        } catch (Exception e) {
          log.error(e.getMessage(), e);
          rollbarService.error(e);
          centerObj.put(key, objectMapper.createObjectNode().toString());
        }
      }
      LocationEntry target = objectMapper.convertValue(centerObj, LocationEntry.class);
      listOfTargets.add(target);
    }
    return listOfTargets;
  }

  private List<DataSourceValueEntry> getValuesFromUrl(String url, Map<String, String> headerObject,
                                                      Map<String, String> mapping, String rootPath, Long id) {
    log.info("updating datasource values in data source entry ");
    log.info("Fetching values from the url {}", url);
    ResponseEntity<String> response = httpHelper.request(url, HttpMethod.GET, null, headerObject);
    return parseResponse(response.getBody(), rootPath, mapping);
  }

  public List<DataSourceEntry> fetchAllByTenantId(Long tenantId) {
    try {
      return search(0, -1, null, null, "tenantId.eq:" + tenantId).getElements();
    } catch (InvalidSeachQueryException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    return Collections.emptyList();
  }

  public List<DataSourceEntry> fetchActiveByTenantId(Long tenantId) {
    try {
      return search(0, -1, null, null, "tenantId.eq:" + tenantId + ";active.eq:true").getElements();
    } catch (InvalidSeachQueryException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    return Collections.emptyList();
  }
}
