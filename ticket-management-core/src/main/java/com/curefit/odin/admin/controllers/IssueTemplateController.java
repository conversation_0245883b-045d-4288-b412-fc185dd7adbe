package com.curefit.odin.admin.controllers;

import com.curefit.odin.admin.models.IssueTemplate;
import com.curefit.odin.admin.pojo.CategoryEntry;
import com.curefit.odin.admin.pojo.IssueTemplateEntry;
import com.curefit.odin.admin.pojo.SubCategoryEntry;
import com.curefit.odin.admin.service.CategoryService;
import com.curefit.odin.admin.service.IssueTemplateService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/issue_template")
public class IssueTemplateController extends BaseOdinController<IssueTemplate, IssueTemplateEntry> {
    private final CategoryService categoryService;

    public IssueTemplateController(IssueTemplateService ticketIssueTemplateService, CategoryService categoryService) {
        super(ticketIssueTemplateService);
        this.categoryService = categoryService;
    }

    @GetMapping(value = "/active")
    public ResponseEntity<List<IssueTemplateEntry>> findActiveBySubCategoryId(@RequestParam Long subCategoryId) {
        return new ResponseEntity<>(((IssueTemplateService) baseMySQLService).findActiveBySubCategoryId(subCategoryId), HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.GET, value = "/categories")
    public ResponseEntity<List<CategoryEntry>> fetchCategories(
            @RequestParam(value = "tenantId") Long tenantId
    ) {
        List<CategoryEntry> categories = categoryService.fetchCategoriesForIssueTemplates(tenantId);
        return new ResponseEntity<>(categories, HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.GET, value = "/subCategories")
    public ResponseEntity<List<SubCategoryEntry>> fetchSubCategoriesByCategoryId(
            @RequestParam(value = "categoryId") Long categoryId
    ) {
        List<SubCategoryEntry> categories = categoryService.fetchSubCategoriesForIssueTemplates(categoryId);
        return new ResponseEntity<>(categories, HttpStatus.OK);
    }
}
