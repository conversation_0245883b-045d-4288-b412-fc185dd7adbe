package com.curefit.odin.admin.models;

import java.util.List;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "data_source")
@ToString
public class DataSource extends BaseMySQLModel {


  /**
  * 
  */
  private static final long serialVersionUID = 6389659763768636765L;

  @Column(name = "tenant_id")
  Long tenantId;

  String name;

  @Column(name = "is_static")
  boolean isStatic;

  String url;
  
  @Column(name="root_path")
  String rootPath;

  String headers;

  @Column(name = "key_mapping")
  String keyMapping;

  @Column(name = "sync_duration_in_hours")
  int syncDurationInHours;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "dataSource", orphanRemoval = true, fetch = FetchType.LAZY)
  List<DataSourceValue> dataSourceValues;
}
