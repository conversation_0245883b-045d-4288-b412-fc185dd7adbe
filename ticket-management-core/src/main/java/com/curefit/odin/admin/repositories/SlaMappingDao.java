package com.curefit.odin.admin.repositories;

import com.curefit.commons.sf.repository.BaseMySQLRepository;
import com.curefit.odin.admin.models.SLAMapping;
import com.curefit.odin.enums.Priority;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SlaMappingDao extends BaseMySQLRepository<SLAMapping> {

    List<SLAMapping> findAllByCategoryId(Long categoryId);

    List<SLAMapping> findAllBySubCategoryId(Long subCategoryId);

    List<SLAMapping> findAllByCategoryIdOrSubCategoryId(Long categoryId, Long subCategoryId);

    Optional<SLAMapping> findAllByCategoryIdAndPriority(Long categoryId, Priority priority);

    Optional<SLAMapping> findAllBySubCategoryIdAndPriority(Long subCategoryId, Priority priority);

}
