package com.curefit.odin.admin.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.models.WatcherConfig;
import com.curefit.odin.admin.pojo.WatcherConfigEntry;
import com.curefit.odin.admin.repositories.WatcherConfigRepository;
import com.curefit.odin.user.pojo.TicketEntry;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */

@Service
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class WatcherConfigService extends BaseMySQLService<WatcherConfig, WatcherConfigEntry> {

    @Autowired
    AssigneeQueueService assigneeQueueService;

    @Autowired
    RollbarService rollbarService;

    @Autowired
    WatcherConfigService self;

    @Autowired
    QueueLocationAssigneesMappingService queueLocationAssigneesMappingService;

    public WatcherConfigService(WatcherConfigRepository watcherConfigRepository) {
        super(watcherConfigRepository);
    }

    @Override
    public WatcherConfig convertToEntity(WatcherConfigEntry entry) {
        WatcherConfig watcherConfig = super.convertToEntity(entry);
        if (entry.getWatchersQueue() != null) {
            watcherConfig.setWatchersQueueId(entry.getWatchersQueue().getId());
        }
        return watcherConfig;
    }

    @Override
    public WatcherConfigEntry convertToEntry(WatcherConfig entity) {
        WatcherConfigEntry entry = super.convertToEntry(entity);

        try {
            entry.setWatchersQueue(assigneeQueueService.findOneById(entity.getWatchersQueueId()));
        } catch (BaseException e) {
            log.error(e.getMessage(), e);
            rollbarService.error(e);
        }
        return entry;
    }

    @Cacheable(value = "fetchWatcherConfig", unless = "#result == null")
    public WatcherConfigEntry fetchWatcherConfig(Long categoryId, Long subCategoryId) {
        String query;
        WatcherConfigEntry watcherConfigEntry = null;
        if (subCategoryId != null) {
            query = "subCategoryId.eq:" + subCategoryId;
            watcherConfigEntry = fetchWatcherConfig(query);
        }

        if (watcherConfigEntry == null && categoryId != null) {
            query = "categoryId.eq:" + categoryId;
            watcherConfigEntry = fetchWatcherConfig(query);
        }
        return watcherConfigEntry;
    }

    public WatcherConfigEntry createOrUpdate(WatcherConfigEntry entry) throws BaseException {
        WatcherConfigEntry watcherConfigEntry = fetchWatcherConfig(entry.getCategoryId(), entry.getSubCategoryId());
        if (watcherConfigEntry == null) {
            return create(entry);
        } else {
            return patchUpdate(watcherConfigEntry.getId(), entry);
        }
    }

    private WatcherConfigEntry fetchWatcherConfig(String query) {
        try {
            List<WatcherConfigEntry> configEntries = search(0, 1, null, null, query).getElements();
            if (configEntries.size() != 0) {
                return configEntries.get(0);
            }
        } catch (InvalidSeachQueryException e) {
            log.error(e.getMessage(), e);
            rollbarService.error(e);
        }
        return null;
    }

    public Set<String> fetchWatchers(TicketEntry ticketEntry) {
        WatcherConfigEntry watcherConfigEntry = self.fetchWatcherConfig(ticketEntry.getCategoryId(), ticketEntry.getSubCategoryId());
        if (watcherConfigEntry == null) {
            return Collections.emptySet();
        }
        Long locationId = ticketEntry.getLocationEntry() != null ? ticketEntry.getLocationEntry().getId() : null;
        Long queueId = watcherConfigEntry.getWatchersQueue().getId();
        return queueLocationAssigneesMappingService.fetchByLocationAndQueueId(locationId, queueId, ticketEntry.getCreatedBy(), false);
    }
}