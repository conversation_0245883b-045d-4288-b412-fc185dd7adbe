package com.curefit.odin.admin.controllers;

import com.curefit.odin.admin.models.NamespaceSubCategoryMapping;
import com.curefit.odin.admin.service.NamespaceSubCategoryMappingService;
import com.curefit.odin.utils.pojo.NamespaceSubCategoryMappingEntry;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/sub_category_namespace")
public class NamespaceSubCategoryMapController
    extends BaseOdinController<NamespaceSubCategoryMapping, NamespaceSubCategoryMappingEntry> {


  public NamespaceSubCategoryMapController(NamespaceSubCategoryMappingService namespaceSubCategoryMappingService) {
    super(namespaceSubCategoryMappingService);
  }


}
