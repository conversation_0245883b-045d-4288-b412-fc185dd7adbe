package com.curefit.odin.admin.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.models.EscalationConfig;
import com.curefit.odin.admin.pojo.EscalationConfigEntry;
import com.curefit.odin.admin.repositories.EscalationConfigDAO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.curefit.odin.commons.Constants.DISABLED_USER;


/**
 * <AUTHOR>
 */

@Service
@Slf4j
public class EscalationConfigService extends BaseMySQLService<EscalationConfig, EscalationConfigEntry> {


  @Autowired
  private AssigneeQueueService assigneeQueueService;

  @Autowired
  RollbarService rollbarService;

  @Autowired
  QueueLocationAssigneesMappingService queueLocationAssigneesMappingService;

  @Autowired
  UserService userService;

  public EscalationConfigService(EscalationConfigDAO escalationConfigDAO) {
    super(escalationConfigDAO);
  }

  @Override
  public EscalationConfigEntry convertToEntry(EscalationConfig entity) {
    EscalationConfigEntry entry =  super.convertToEntry(entity);
    entry.getEscalationRules().forEach(escalationRule -> {
      try {
        escalationRule.setQueueEntry(assigneeQueueService.findOneById(escalationRule.getQueueId()));
      } catch (BaseException e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
      }
    });
    return entry;
  }

  @Override
  public EscalationConfig convertToEntity(EscalationConfigEntry entry) {
    entry.getEscalationRules()
            .forEach(escalationRule -> {
              if (escalationRule.getQueueId() == null) {
                escalationRule.setQueueId(escalationRule.getQueueEntry().getId());
              }
              escalationRule.setQueueEntry(null);
            });
    return super.convertToEntity(entry);
  }

  public EscalationConfigEntry createOrUpdate(EscalationConfigEntry entry) throws BaseException {
    EscalationConfigEntry escalationConfigEntry = fetchEscalationConfig(entry.getCategoryId(), entry.getSubCategoryId());
    if (escalationConfigEntry == null) {
      return create(entry);
    } else {
      return patchUpdate(escalationConfigEntry.getId(), entry);
    }
  }

  @Cacheable(value = "fetchEscalationUsers", unless = "#result == null")
  public EscalationConfigEntry fetchEscalationUsers(Long locationId, Long categoryId, Long subCategoryId, String email) {
    EscalationConfigEntry escalationConfigEntry = fetchEscalationConfig(categoryId, subCategoryId);
    if (escalationConfigEntry == null) {
      return null;
    }
    escalationConfigEntry.getEscalationRules().forEach(escalationRule -> {
      Set<String> assignees = queueLocationAssigneesMappingService.fetchByLocationAndQueueId(locationId, escalationRule.getQueueId(), email, false);
      escalationRule.setEscalationUsers(
                      assignees.stream()
                      .filter(userId -> !StringUtils.isBlank(userId))
                      .map(userId -> userService.findUserByMailIdWithDefault(userId))
                      .filter(userEntry -> !userEntry.getName().equalsIgnoreCase(DISABLED_USER))
                      .collect(Collectors.toList()));
    });
    return escalationConfigEntry;
  }

  @Cacheable(value = "fetchEscalationConfig", unless = "#result == null")
  public EscalationConfigEntry fetchEscalationConfig(Long categoryId, Long subCategoryId) {
    String query;
    EscalationConfigEntry escalationConfigEntry = null;
    if (subCategoryId != null) {
      query = "subCategoryId.eq:" + subCategoryId;
      escalationConfigEntry = fetchEscalationConfig(query);
    }

    if (escalationConfigEntry == null && categoryId != null) {
      query = "categoryId.eq:" + categoryId;
      escalationConfigEntry = fetchEscalationConfig(query);
    }
    return escalationConfigEntry;
  }

  private EscalationConfigEntry fetchEscalationConfig(String query) {
    try {
      List<EscalationConfigEntry> escalationConfigs = search(0, 1, null, null, query).getElements();
      if (escalationConfigs.size() != 0) {
        EscalationConfigEntry escalationConfigEntry = escalationConfigs.get(0);
        escalationConfigEntry.getEscalationRules()
                .sort((rule1, rule2) -> (int)(rule1.getEscalationTimeInHours() - rule2.getEscalationTimeInHours()));
        return escalationConfigEntry;
      }
    } catch (InvalidSeachQueryException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    return null;
  }
}
