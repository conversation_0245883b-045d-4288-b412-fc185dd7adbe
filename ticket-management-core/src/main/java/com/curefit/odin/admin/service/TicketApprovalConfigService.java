package com.curefit.odin.admin.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.models.TicketApprovalConfig;
import com.curefit.odin.admin.pojo.TicketApprovalConfigEntry;
import com.curefit.odin.admin.repositories.TicketApprovalConfigRepository;
import com.curefit.odin.user.pojo.TicketEntry;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TicketApprovalConfigService extends BaseMySQLService<TicketApprovalConfig, TicketApprovalConfigEntry> {

    @Autowired
    AssigneeQueueService assigneeQueueService;

    @Autowired
    RollbarService rollbarService;

    @Autowired
    QueueLocationAssigneesMappingService queueLocationAssigneesMappingService;

    @Autowired
    TicketApprovalConfigService self;

    public TicketApprovalConfigService(TicketApprovalConfigRepository ticketApprovalConfigRepository) {
        super(ticketApprovalConfigRepository);
    }

    @Override
    public TicketApprovalConfig convertToEntity(TicketApprovalConfigEntry entry) {
        TicketApprovalConfig ticketApprovalConfig = super.convertToEntity(entry);
        if (entry.getApprovalQueue() != null) {
            ticketApprovalConfig.setApprovalQueueId(entry.getApprovalQueue().getId());
        }
        return ticketApprovalConfig;
    }

    @Override
    public TicketApprovalConfigEntry convertToEntry(TicketApprovalConfig entity) {
        TicketApprovalConfigEntry entry = super.convertToEntry(entity);

        try {
            entry.setApprovalQueue(assigneeQueueService.findOneById(entity.getApprovalQueueId()));
        } catch (BaseException e) {
            log.error(e.getMessage(), e);
            rollbarService.error(e);
        }
        return entry;
    }

    @Cacheable(value = "fetchApprovalConfig", unless = "#result == null")
    public TicketApprovalConfigEntry fetchApprovalConfig(Long categoryId, Long subCategoryId) {
        String query;
        TicketApprovalConfigEntry approvalConfigEntry = null;
        if (subCategoryId != null) {
            query = "subCategoryId.eq:" + subCategoryId;
            approvalConfigEntry = fetchApprovalConfig(query);
        }

        if (approvalConfigEntry == null && categoryId != null) {
            query = "categoryId.eq:" + categoryId;
            approvalConfigEntry = fetchApprovalConfig(query);
        }
        return approvalConfigEntry;
    }

    public TicketApprovalConfigEntry createOrUpdate(TicketApprovalConfigEntry entry) throws BaseException {
        TicketApprovalConfigEntry ticketApprovalConfigEntry = fetchApprovalConfig(entry.getCategoryId(), entry.getSubCategoryId());
        if (ticketApprovalConfigEntry == null) {
            return create(entry);
        } else {
            return patchUpdate(ticketApprovalConfigEntry.getId(), entry);
        }
    }

    private TicketApprovalConfigEntry fetchApprovalConfig(String query) {
        try {
            List<TicketApprovalConfigEntry> configEntries = search(0, 1, null, null, query).getElements();
            if (!configEntries.isEmpty()) {
                return configEntries.getFirst();
            }
        } catch (InvalidSeachQueryException e) {
            log.error(e.getMessage(), e);
            rollbarService.error(e, e.getMessage());
        }
        return null;
    }

    public Set<String> fetchTicketApprovers(TicketEntry ticketEntry) {
        TicketApprovalConfigEntry approvalConfigEntry = self.fetchApprovalConfig(ticketEntry.getCategoryId(), ticketEntry.getSubCategoryId());
        if (approvalConfigEntry == null) {
            return Collections.emptySet();
        }
        Long locationId = ticketEntry.getLocationEntry() != null ? ticketEntry.getLocationEntry().getId() : null;
        Long queueId = approvalConfigEntry.getApprovalQueue().getId();
        return queueLocationAssigneesMappingService.fetchByLocationAndQueueId(locationId, queueId, ticketEntry.getCreatedBy(), true);
    }
}