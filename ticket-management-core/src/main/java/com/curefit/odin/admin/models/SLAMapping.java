package com.curefit.odin.admin.models;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import com.curefit.odin.enums.Priority;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "sla_mapping")
@AllArgsConstructor
@NoArgsConstructor
public class SLAMapping extends BaseMySQLModel {

  /**
  * 
  */
  private static final long serialVersionUID = -6716001193637778232L;

  @Column(name = "category_id")
  Long categoryId;
  
  @Column(name = "sub_category_id")
  Long subCategoryId;
  
  @Enumerated(EnumType.STRING)
  Priority priority;
  
  @Column(name = "sla_in_hours")
  int slaInHours;

}
