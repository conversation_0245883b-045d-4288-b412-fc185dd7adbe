package com.curefit.odin.admin.controllers;

import java.util.List;

import com.curefit.odin.admin.pojo.*;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import com.curefit.common.data.exception.BaseException;
import com.curefit.odin.admin.models.CustomField;
import com.curefit.odin.admin.service.CustomFieldService;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/field")
@Slf4j
public class CustomFieldController extends BaseOdinController<CustomField, CustomFieldEntry> {


  public CustomFieldController(CustomFieldService fieldService) {
    super(fieldService);
  }

  @Deprecated
  @RequestMapping(method = RequestMethod.POST, value = "/filter")
  public List<CustomFieldEntry> filter(@RequestBody CustomFieldFilterRequestEntry fieldFilterRequestEntry) {
    return ((CustomFieldService) baseMySQLService).filter(fieldFilterRequestEntry);
  }

  @Deprecated
  @RequestMapping(method = RequestMethod.POST, value = "/filter/active")
  public List<CustomFieldEntry> filterActive(@RequestBody CustomFieldFilterRequestEntry fieldFilterRequestEntry) {
    return ((CustomFieldService) baseMySQLService).filterActive(fieldFilterRequestEntry);
  }

  @RequestMapping(method = RequestMethod.POST, value = "/v2/filter")
  public List<CustomFieldEntry> filterV2(@RequestBody CustomFieldFilterRequestEntry fieldFilterRequestEntry) {
    return ((CustomFieldService) baseMySQLService).filterV2(fieldFilterRequestEntry);
  }

  @RequestMapping(method = RequestMethod.POST, value = "/v2/filter/active")
  public List<CustomFieldEntry> filterActiveV2(@RequestBody CustomFieldFilterRequestEntry fieldFilterRequestEntry) {
    return ((CustomFieldService) baseMySQLService).filterActiveV2(fieldFilterRequestEntry);
  }

  @RequestMapping(method = RequestMethod.POST, value = "/filter/tree/active/custom")
  public CustomFieldTreeResponse filterActiveCustomTree(@RequestBody CustomFieldTreeRequestEntry fieldTreeRequestEntry) {
    return ((CustomFieldService) baseMySQLService).filterActiveCustomTree(fieldTreeRequestEntry);
  }

  @RequestMapping(method = RequestMethod.GET, value = "/data_types")
  public ResponseEntity<List<String>> getDataTypes() {
    return new ResponseEntity<>(((CustomFieldService) baseMySQLService).getDataTypes(), HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET, value = "/category/{id}")
  public ResponseEntity<List<CustomFieldEntry>> fetchAllByCategoryId(@PathVariable Long id) throws BaseException {
    return new ResponseEntity<>(((CustomFieldService) baseMySQLService).findFieldsByCategoryId(id), HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET, value = "/category/{id}/active")
  public ResponseEntity<List<CustomFieldEntry>> fetchAllActiveByCategoryId(@PathVariable Long id) throws BaseException {
    return new ResponseEntity<>(((CustomFieldService) baseMySQLService).findActiveFieldsByCategoryId(id), HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET, value = "/subCategory/{id}")
  public ResponseEntity<List<CustomFieldEntry>> fetchAllBySubCategoryId(@PathVariable Long id) throws BaseException {
    return new ResponseEntity<>(((CustomFieldService) baseMySQLService).findFieldsBySubCategoryId(id), HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET, value = "/subCategory/{id}/active")
  public ResponseEntity<List<CustomFieldEntry>> fetchAllActiveBySubCategoryId(@PathVariable Long id) throws BaseException {
    return new ResponseEntity<>(((CustomFieldService) baseMySQLService).findActiveFieldsBySubCategoryId(id), HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.POST, value = "/createOrUpdate")
  public CustomFieldEntry createOrUpdate(@RequestBody CustomFieldEntry customFieldEntry) throws BaseException {
    return ((CustomFieldService) baseMySQLService).createOrUpdate(customFieldEntry);
  }

}
