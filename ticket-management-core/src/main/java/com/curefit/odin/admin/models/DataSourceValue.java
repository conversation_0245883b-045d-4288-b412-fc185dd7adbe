package com.curefit.odin.admin.models;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "data_source_value",
    uniqueConstraints = {@UniqueConstraint(columnNames = {"reference_id", "data_source_id"})})
public class DataSourceValue extends BaseMySQLModel {

  /**
   * 
   */
  private static final long serialVersionUID = -480602704945060139L;

  @Column(name = "reference_id")
  String referenceId;

  @Column(name = "value")
  String value;

  String metadata;

  @ManyToOne
  @JoinColumn(name = "data_source_id")
  DataSource dataSource;

  @Override
  public String toString() {
    return "id:" + getId() + ",value:" + value;
  }
}
