package com.curefit.odin.admin.models;

import javax.persistence.*;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "filter", uniqueConstraints = {@UniqueConstraint(columnNames = {"name"})})
public class Filter extends BaseMySQLModel {

  /**
  * 
  */
  private static final long serialVersionUID = -473479658108822371L;

  String name;

  String value;
}
