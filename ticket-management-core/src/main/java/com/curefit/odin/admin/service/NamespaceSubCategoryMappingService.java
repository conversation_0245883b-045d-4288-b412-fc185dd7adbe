package com.curefit.odin.admin.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.models.NamespaceSubCategoryMapping;
import com.curefit.odin.admin.repositories.SubCategoryNamespaceDao;
import com.curefit.odin.utils.pojo.NamespaceSubCategoryMappingEntry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class NamespaceSubCategoryMappingService
        extends BaseMySQLService<NamespaceSubCategoryMapping, NamespaceSubCategoryMappingEntry> {

    @Autowired
    private RollbarService rollbarService;

    public NamespaceSubCategoryMappingService(SubCategoryNamespaceDao subCategoryNamespaceDao) {
        super(subCategoryNamespaceDao);
    }

    public List<Long> getSubCategoryIdsByNamespace(String namespace) throws BaseException {
        try {
            return search(0, -1, null, null, "name.eq:" + namespace).getElements().stream()
                    .map(NamespaceSubCategoryMappingEntry::getSubCategoryId).collect(Collectors.toList());
        } catch (InvalidSeachQueryException e) {
            log.error(e.getMessage(), e);
            rollbarService.error(e);
            throw new BaseException("Error:" + e.getMessage());
        }
    }
}
