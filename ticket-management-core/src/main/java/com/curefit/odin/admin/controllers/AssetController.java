package com.curefit.odin.admin.controllers;

import com.curefit.common.data.exception.BaseException;
import com.curefit.odin.admin.service.AssetService;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;

@RestController
@Validated
@FieldDefaults(level = AccessLevel.PRIVATE)
@RequestMapping("/assets")
public class AssetController {

    @Autowired
    AssetService assetService;

    @GetMapping(value = "/location")
    public ResponseEntity<?> getAssetsByLocationCode(@RequestParam(value = "locationId") @NotNull Long locationId) throws BaseException {
        return new ResponseEntity<>(assetService.getAssetsByLocationCode(locationId), HttpStatus.OK);
    }
}
