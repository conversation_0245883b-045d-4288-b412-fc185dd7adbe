package com.curefit.odin.admin.models;

import com.curefit.commons.sf.audit.annotation.IgnoreAudit;
import com.curefit.odin.enums.LocationType;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */

@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "queue_location_assignees_map")
public class QueueLocationAssigneesMapping extends BaseMySQLModel {

  @ManyToOne
  @JoinColumn(name = "assignee_queue_id", referencedColumnName = "id")
  AssigneeQueue assigneeQueue;

  @Column(name = "location_type")
  @Enumerated(value = EnumType.STRING)
  LocationType locationType;

  @Column(name = "location_code")
  String locationCode;

  @IgnoreAudit
  @Type(type = "json")
  @Column(columnDefinition = "json")
  Set<String> assignees;
}
