package com.curefit.odin.admin.service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.models.NamespaceTenantsMapping;
import com.curefit.odin.admin.repositories.TenantNamespaceDao;
import com.curefit.odin.utils.pojo.NamespaceTenantsMappingEntry;
import com.curefit.odin.utils.pojo.TenantEntry;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class NamespaceTenantsMappingService
    extends BaseMySQLService<NamespaceTenantsMapping, NamespaceTenantsMappingEntry> {

  @Autowired
  private TenantService tenantService;

  @Autowired
  private RollbarService rollbarService;

  public NamespaceTenantsMappingService(TenantNamespaceDao tenantNamespaceDao) {
    super(tenantNamespaceDao);
  }

  @Override
  public NamespaceTenantsMappingEntry convertToEntry(NamespaceTenantsMapping entity) {
    NamespaceTenantsMappingEntry tenantNamespaceEntry = super.convertToEntry(entity);
    tenantNamespaceEntry.setTenantId(entity.getTenant().getId());
    tenantNamespaceEntry.setTenantEntry(tenantService.convertToEntry(entity.getTenant()));
    return tenantNamespaceEntry;
  }

  @Override
  public NamespaceTenantsMapping convertToEntity(NamespaceTenantsMappingEntry entry) {
    NamespaceTenantsMapping convertedEntity = super.convertToEntity(entry);
    try {
      convertedEntity.setTenant(tenantService.fetchEntityById(entry.getTenantId()));
    } catch (BaseException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    return convertedEntity;
  }


  public List<TenantEntry> getTenantByNamespace(String namespace) throws BaseException {
    try {
      return search(0, -1, null, null, "name.eq:" + namespace).getElements().stream()
          .map(NamespaceTenantsMappingEntry::getTenantEntry).collect(Collectors.toList());
    } catch (InvalidSeachQueryException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
      throw new BaseException("Error:" + e.getMessage());
    }

  }

  @Cacheable(value = "getNamespaceByTenant", unless = "#result == null")
  public List<NamespaceTenantsMappingEntry> getNamespacesByTenant(Long tenantId) {
    try {
      return search(0, -1, null, null, "tenant.id.eq:" + tenantId).getElements();
    } catch (InvalidSeachQueryException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    return new ArrayList<>();
  }
}
