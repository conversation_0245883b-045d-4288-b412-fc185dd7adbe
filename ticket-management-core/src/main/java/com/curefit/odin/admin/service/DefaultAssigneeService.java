package com.curefit.odin.admin.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.models.AssigneeQueue;
import com.curefit.odin.admin.models.DefaultAssignee;
import com.curefit.odin.admin.pojo.DefaultAssigneeEntry;
import com.curefit.odin.admin.pojo.EscalationConfigEntry;
import com.curefit.odin.admin.pojo.EscalationRule;
import com.curefit.odin.admin.repositories.DefaultAssigneeDAO;
import com.curefit.odin.enums.EscalationLevel;
import com.curefit.odin.enums.UserRole;
import com.curefit.odin.utils.Secured;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.curefit.odin.commons.Constants.DISABLED_USER;
import static com.curefit.odin.enums.AssigneeQueueType.DYNAMIC;

@Service
@Slf4j
public class DefaultAssigneeService extends BaseMySQLService<DefaultAssignee, DefaultAssigneeEntry> {

  @Autowired
  private AssigneeQueueService assigneeQueueService;

  @Autowired
  RollbarService rollbarService;

  @Autowired
  QueueLocationAssigneesMappingService queueLocationAssigneesMappingService;

  @Autowired
  UserService userService;

  @Autowired
  EscalationConfigService escalationConfigService;

  public DefaultAssigneeService(DefaultAssigneeDAO defaultAssigneeDao) {
    super(defaultAssigneeDao);
  }

  @Override
  public DefaultAssigneeEntry convertToEntry(DefaultAssignee defaultAssignee) {
    DefaultAssigneeEntry defaultAssigneeEntry = super.convertToEntry(defaultAssignee);
    defaultAssigneeEntry.setAssigneeQueueEntry(assigneeQueueService.convertToEntry(defaultAssignee.getAssigneeQueue()));
    return defaultAssigneeEntry;
  }

  @Override
  public DefaultAssignee convertToEntity(DefaultAssigneeEntry entry) {
    DefaultAssignee defaultAssignee = super.convertToEntity(entry);
    if (entry.getAssigneeQueueEntry() != null) {
      try {
        defaultAssignee.setAssigneeQueue(assigneeQueueService.fetchEntityById(entry.getAssigneeQueueEntry().getId()));
      } catch (BaseException e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
      }
    }
    return defaultAssignee;
  }

  @Secured(allowedRoles = UserRole.ADMIN)
  public DefaultAssigneeEntry deleteById(Long id) throws BaseException {
    DefaultAssigneeEntry defaultAssigneeEntry = new DefaultAssigneeEntry();
    defaultAssigneeEntry.setActive(false);
    return patchUpdate(id, defaultAssigneeEntry);
  }

  private DefaultAssigneeEntry fetchDefaultAssignee(String query) {
    try {
      List<DefaultAssigneeEntry> defaultAssigneeEntries = search(0, -1, null, null, query).getElements();
      if (defaultAssigneeEntries.size() != 0) {
        return defaultAssigneeEntries.get(0);
      }
    } catch (InvalidSeachQueryException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    return null;
  }

  public DefaultAssigneeEntry fetchDefaultAssignee(Long categoryId, Long subCategoryId) {
    String query;
    DefaultAssigneeEntry defaultAssigneeEntry = null;
    if (subCategoryId != null) {
      query = "subCategoryId.eq:" + subCategoryId;
      defaultAssigneeEntry = fetchDefaultAssignee(query);
    }

    if (defaultAssigneeEntry == null && categoryId != null) {
      query = "categoryId.eq:" + categoryId;
      defaultAssigneeEntry = fetchDefaultAssignee(query);
    }
    return defaultAssigneeEntry;
  }

  // For User
  @Cacheable(value = "fetchDefaultAssignee", unless = "#result == null")
  public DefaultAssigneeEntry fetchDefaultAssignee(Long locationId, Long categoryId, Long subCategoryId, String email) {
    DefaultAssigneeEntry defaultAssigneeEntry = fetchDefaultAssignee(categoryId, subCategoryId);
    if (defaultAssigneeEntry == null) {
      return null;
    }
    Long assigneeQueueId = defaultAssigneeEntry.getAssigneeQueueEntry().getId();
    Set<String> assignees = queueLocationAssigneesMappingService.fetchByLocationAndQueueId(locationId, assigneeQueueId, email, false);
    if (assignees.isEmpty()) {
      AssigneeQueue assigneeQueue = null;
      try {
        assigneeQueue = assigneeQueueService.fetchEntityById(assigneeQueueId);
      } catch (BaseException e) {
        throw new RuntimeException(e);
      }
      if (DYNAMIC.name().equals(assigneeQueue.getType())) {
        // if employee manager isn't there, escalate it to next set of people
        EscalationConfigEntry escalationConfigEntry = escalationConfigService.fetchEscalationUsers(locationId, categoryId, subCategoryId, email);
        if (escalationConfigEntry != null && escalationConfigEntry.getEscalationRules() != null) {
          Optional<EscalationRule> escalationRuleOpt = escalationConfigEntry.getEscalationRules().stream()
                  .filter(escalationRule -> escalationRule.getEscalationLevel() == EscalationLevel.L1)
                  .findFirst();
          if (escalationRuleOpt.isPresent())
            escalationRuleOpt.filter(escalationRule1 -> escalationRule1.getEscalationUsers() != null)
                    .ifPresent(escalationRule -> {
                      defaultAssigneeEntry.setAssignedUsers(
                              escalationRule.getEscalationUsers()
                                      .stream()
                                      .filter(userEntry -> !userEntry.getName().equalsIgnoreCase(DISABLED_USER))
                                      .collect(Collectors.toList()));
                      defaultAssigneeEntry.setAssigneeQueueEntry(escalationRule.getQueueEntry());
                    });
        }
      }
    } else {
      defaultAssigneeEntry.setAssignedUsers(assignees.stream()
              .filter(assigeeId -> !StringUtils.isBlank(assigeeId))
              .map(assigneeId -> userService.findUserByMailIdWithDefault(assigneeId))
              .filter(userEntry -> !DISABLED_USER.equalsIgnoreCase(userEntry.getName()))
              .collect(Collectors.toList()));
    }
    return defaultAssigneeEntry;
  }

  public DefaultAssigneeEntry createOrUpdate(DefaultAssigneeEntry entry) throws BaseException {
    DefaultAssigneeEntry defaultAssigneeEntry = fetchDefaultAssignee(entry.getCategoryId(), entry.getSubCategoryId());
    if (defaultAssigneeEntry == null) {
      return create(entry);
    } else {
      return patchUpdate(defaultAssigneeEntry.getId(), entry);
    }
  }
}
