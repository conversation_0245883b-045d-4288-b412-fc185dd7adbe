package com.curefit.odin.admin.repositories;

import java.util.List;
import java.util.Optional;
import org.springframework.stereotype.Repository;
import com.curefit.commons.sf.repository.BaseMySQLRepository;
import com.curefit.odin.admin.models.CustomField;

@Repository
public interface CustomFieldDAO extends BaseMySQLRepository<CustomField> {
  
	List<CustomField> findBySubCategoryIdOrderByOrder(Long ticketSubCategoryId);

	List<CustomField> findBySubCategoryIdAndActiveOrderByOrder(Long ticketSubCategoryId, Boolean active);

	List<CustomField> findByCategoryIdOrderByOrder(Long categoryId);

	List<CustomField> findByCategoryIdAndActiveOrderByOrder(Long categoryId, Boolean active);

	List<CustomField> findByCategoryIdOrSubCategoryIdOrTenantIdOrderByOrder(Long categoryId, Long subCategoryId, Long tenantId);

	List<CustomField> findByCategoryIdOrTenantIdOrderByOrder(Long categoryId, Long tenantIdOfCategory);

	List<CustomField> findByTenantIdOrderByOrder(Long tenantId);
}
