package com.curefit.odin.admin.controllers;

import com.curefit.odin.admin.models.NamespaceConfig;
import com.curefit.odin.admin.service.NamespaceConfigService;
import com.curefit.odin.utils.pojo.NamespaceConfigEntry;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */

@RestController
@RequestMapping("/namespace_config")
public class NamespaceConfigController extends BaseOdinController<NamespaceConfig, NamespaceConfigEntry> {

  public NamespaceConfigController(NamespaceConfigService namespaceConfigService) {
    super(namespaceConfigService);
  }
}
