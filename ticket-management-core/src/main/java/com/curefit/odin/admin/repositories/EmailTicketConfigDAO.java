package com.curefit.odin.admin.repositories;

import com.curefit.commons.sf.repository.BaseMySQLRepository;
import com.curefit.odin.utils.models.EmailTicketConfig;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * <AUTHOR>
 */

@Repository
public interface EmailTicketConfigDAO extends BaseMySQLRepository<EmailTicketConfig> {

  Optional<EmailTicketConfig> findByEmail(String email);

  Optional<EmailTicketConfig> findByAliasEmail(String email);

}
