package com.curefit.odin.admin.repositories;

import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Repository;
import com.curefit.commons.sf.repository.BaseMySQLRepository;
import com.curefit.odin.admin.models.Category;

@Repository
public interface CategoryDAO extends BaseMySQLRepository<Category> {

  List<Category> findByTenantId(Long id);

  List<Category> findByTenantIdAndActive(Long id, Boolean active);

  Optional<Category> findByTenantIdAndName(Long tenantId, String name);

}
