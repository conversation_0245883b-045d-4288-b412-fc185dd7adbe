package com.curefit.odin.admin.service;

import com.curefit.cf.commons.pojo.BaseEntry;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.commons.sf.util.HeadersUtils;
import com.curefit.odin.admin.models.Tenant;
import com.curefit.odin.admin.repositories.TenantDAO;
import com.curefit.odin.enums.AuthType;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import com.curefit.odin.utils.AuthService;
import com.curefit.odin.utils.pojo.TenantEntry;
import com.curefit.odin.utils.service.DomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.curefit.odin.commons.Constants.ODIN_NAMESPACE;

@Service
@Slf4j
public class TenantService extends BaseMySQLService<Tenant, TenantEntry> {


  @Autowired
  RollbarService rollbarService;

  @Autowired
  CustomFieldService customFieldService;

  @Autowired
  DomainService domainService;

  @Autowired
  NamespaceTenantsMappingService namespaceTenantsMappingService;

  @Autowired
  AuthService authService;

  public TenantService(TenantDAO tenantDAO) {
    super(tenantDAO);
  }

  /**
   * @param namespace
   * @return
   * @throws BaseException
   */

  public List<TenantEntry> fetchAllForNamespace(String namespace) throws BaseException {
    try {
      List<BaseOdinEntry> contexts = authService.getContexts(AuthType.TENANT);
      List<TenantEntry> authorizedTenants = null;
      if (!contexts.isEmpty()) {
       authorizedTenants = contexts.stream()
                .map(context -> ((TenantEntry) context))
                .collect(Collectors.toList());
      }
      List<TenantEntry> tenantEntries = new ArrayList<>();
      if (namespace.equalsIgnoreCase(ODIN_NAMESPACE)) {
        tenantEntries = fetchTenantsByDomain();
      }
      if (tenantEntries.size() == 0) {
        tenantEntries = namespaceTenantsMappingService.getTenantByNamespace(namespace);
      }

      if (authorizedTenants != null) {
        tenantEntries = (List<TenantEntry>) CollectionUtils.intersection(tenantEntries, authorizedTenants);
      }
      tenantEntries.sort(Comparator.comparing(BaseEntry::getId));
      return tenantEntries;

    } catch (BaseException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
      throw e;
    }
  }

  private List<TenantEntry> fetchTenantsByDomain() {
    String user = HeadersUtils.getCurrentUser();
    String userEmailDomain = user.split("@")[1];
    return domainService.fetchWhiteListedTenantsByDomain(userEmailDomain);
  }

  @Override
  public TenantEntry create(TenantEntry entry) throws BaseException {
    entry = super.create(entry);
    customFieldService.createDefaultFields(entry.getId());
    return entry;
  }

  public TenantEntry fetchByTenantName(String name) {
    Optional<Tenant> tenantOptional = ((TenantDAO)super.baseMySQLRepository).findByName(name);
    return tenantOptional.map(this::convertToEntry).orElse(null);
  }

  @Cacheable(value = "fetchByNeoCode", unless = "#result == null")
  public TenantEntry fetchByNeoCode(String neoCode) throws BaseException {
    try {
      return search(0, -1, null, null, "neoCode.eq:" + neoCode).getElements().get(0);
    } catch (Exception e) {
      throw new BaseException(e);
    }
  }

  public List<TenantEntry> findAllById(List<Long> ids) {
    List<Tenant> tenants = (List<Tenant>) ((TenantDAO)baseMySQLRepository).findAllById(ids);
    return tenants.stream()
        .map(this::convertToEntry)
        .collect(Collectors.toList());
  }
}
