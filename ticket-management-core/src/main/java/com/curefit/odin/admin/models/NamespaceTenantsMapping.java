package com.curefit.odin.admin.models;

import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@javax.persistence.Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "namespace_tenant_mapping",
    uniqueConstraints = {@UniqueConstraint(columnNames = {"name"})})
public class NamespaceTenantsMapping extends BaseMySQLModel {

  /**
  * 
  */
  private static final long serialVersionUID = 2634020024343637596L;

  String name;

  @ManyToOne
  @JoinColumn(name = "tenant_id", referencedColumnName = "id")
  Tenant tenant;
}
