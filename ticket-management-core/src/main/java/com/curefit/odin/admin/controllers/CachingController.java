package com.curefit.odin.admin.controllers;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import com.curefit.odin.admin.service.CachingService;

@RestController
@RequestMapping("/cache")
public class CachingController {

  @Autowired
  CachingService cachingService;

  @RequestMapping(value="clear", method = RequestMethod.PUT)
  public void clearAllCaches() {
    cachingService.evictAllCaches();
  }
}
