package com.curefit.odin.admin.repositories;

import com.curefit.commons.sf.repository.BaseMySQLRepository;
import com.curefit.odin.utils.models.GmailMessage;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */

@Repository
public interface GmailMessageDAO extends BaseMySQLRepository<GmailMessage> {

  Optional<GmailMessage> findByMessageId(String messageId);

  List<GmailMessage> findByMessageIdIn(Collection<String> messageIds);
}
