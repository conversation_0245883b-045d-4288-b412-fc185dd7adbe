package com.curefit.odin.admin.controllers;

import com.curefit.odin.enums.Priority;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.curefit.odin.admin.service.SLAService;

import java.util.Date;

@RestController
@RequestMapping("/due_date")
public class DueDateController {

  @Autowired
  private SLAService slaService;

  @RequestMapping(method = RequestMethod.GET)
  public ResponseEntity<Long> getDueDate(@RequestParam(required = false) Long categoryId,
                                         @RequestParam(required = false) Long subCategoryId,
                                         @RequestParam String priority,
                                         @RequestParam(required = false) Long createdOn) {
    if(createdOn != null) {
      return new ResponseEntity<>(slaService.getDueDate(categoryId, subCategoryId, Priority.get(priority), new Date(createdOn)),
          HttpStatus.OK);
    } else {
      return new ResponseEntity<>(slaService.getDueDate(categoryId, subCategoryId, Priority.get(priority)),
          HttpStatus.OK);
    }
  }
}
