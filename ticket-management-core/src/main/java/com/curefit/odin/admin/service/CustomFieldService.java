package com.curefit.odin.admin.service;

import com.curefit.cf.commons.pojo.PagedResultEntry;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.ResourceNotFoundException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.models.CustomField;
import com.curefit.odin.admin.models.SubCategory;
import com.curefit.odin.admin.pojo.*;
import com.curefit.odin.admin.repositories.CustomFieldDAO;
import com.curefit.odin.enums.CustomFieldTreeNodeType;
import com.curefit.odin.enums.DataType;
import com.curefit.odin.enums.FieldType;
import com.curefit.odin.enums.UserRole;
import com.curefit.odin.user.pojo.BaseOdinEntry;
import com.curefit.odin.utils.Secured;
import com.curefit.odin.utils.pojo.TenantEntry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.curefit.odin.enums.DataType.*;
import static com.curefit.odin.enums.FieldType.CUSTOM;
import static com.curefit.odin.enums.FieldType.DEFAULT;

@Service
@Slf4j
public class CustomFieldService extends BaseMySQLService<CustomField, CustomFieldEntry> {

  @Autowired
  private CategoryService categoryService;

  @Autowired
  private SubCategoryService subCategoryService;

  @Autowired
  private TenantService tenantService;

  @Autowired
  private DataSourceService dataSourceService;

  @Autowired
  private DataSourceValueService dataSourceValueService;

  @Autowired
  RollbarService rollbarService;

  public CustomFieldService(CustomFieldDAO fieldDao) {
    super(fieldDao);
  }

  private void sanitize(CustomFieldEntry entry) {
    if (entry.getSubCategoryId() != null) {
      entry.setCategoryId(null);
      entry.setTenantId(null);
    } else if (entry.getCategoryId() != null) {
      entry.setTenantId(null);
    }
  }

  @Override
  public CustomFieldEntry convertToEntry(CustomField field) {
    CustomFieldEntry fieldEntry = super.convertToEntry(field);
    if (null != field.getCategory()) {
      fieldEntry.setCategoryId(field.getCategory().getId());
    }
    if (null != field.getSubCategory()) {
      fieldEntry.setSubCategoryId(field.getSubCategory().getId());
    }
    if (null != field.getTenant()) {
      fieldEntry.setTenantId(field.getTenant().getId());
    }
    if (null != field.getDataSource()) {
      fieldEntry.setDataSourceId(field.getDataSource().getId());
      try {
        fieldEntry.setDataSourceEntry(dataSourceService.findOneById(field.getDataSource().getId()));
      } catch (BaseException e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
      }
    }
    return fieldEntry;
  }

  @Override
  public CustomField convertToEntity(CustomFieldEntry entry) {
    sanitize(entry);
    CustomField convertedEntity = super.convertToEntity(entry);
    Long categoryId = entry.getCategoryId();
    if (null != categoryId) {
      try {
        convertedEntity.setCategory(categoryService.fetchEntityById(categoryId));
      } catch (BaseException e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
      }
    }
    if (null != entry.getSubCategoryId()) {
      try {
        convertedEntity.setSubCategory(subCategoryService.fetchEntityById(entry.getSubCategoryId()));
      } catch (BaseException e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
      }
    }
    if (null != entry.getTenantId()) {
      try {
        convertedEntity.setTenant(tenantService.fetchEntityById(entry.getTenantId()));
      } catch (BaseException e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
      }
    }
    if (null != entry.getDataSourceId()) {
      try {
        convertedEntity.setDataSource(dataSourceService.fetchEntityById(entry.getDataSourceId()));
      } catch (BaseException e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
      }
    }
    return convertedEntity;
  }

  public List<String> getDataTypes() {
    log.info("Fetching the data types");
    List<String> ls = new ArrayList<>();
    for (DataType s : DataType.values()) {
      ls.add(s.name());
    }
    return ls;
  }

  public List<CustomFieldEntry> filter(CustomFieldFilterRequestEntry fieldFilterRequestEntry) {
    List<CustomField> fields = new ArrayList<>();
    if (null != fieldFilterRequestEntry.getSubCategoryId()) {
      SubCategory subCategory;
      try {
        subCategory = subCategoryService.fetchEntityById(fieldFilterRequestEntry.getSubCategoryId());
        Long categoryId = subCategory != null && subCategory.getCategory() != null
                ? subCategory.getCategory().getId()
                : null;
        Long tenantId = categoryId == null ? null
                : categoryService.fetchEntityById(categoryId).getTenant().getId();
        log.info("Fetching fields belonging to the ticket sub category {}",
                fieldFilterRequestEntry.getSubCategoryId());
        fields = ((CustomFieldDAO) baseMySQLRepository)
                .findByCategoryIdOrSubCategoryIdOrTenantIdOrderByOrder(categoryId,
                        fieldFilterRequestEntry.getSubCategoryId(), tenantId);
      } catch (BaseException e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
      }

    } else if (null != fieldFilterRequestEntry.getCategoryId()) {
      log.info("Fetching fields belonging to the ticket category {}",
              fieldFilterRequestEntry.getCategoryId());
      Long tenantId;
      try {
        tenantId = categoryService
                .fetchEntityById(fieldFilterRequestEntry.getCategoryId()).getTenant().getId();
        fields = ((CustomFieldDAO) baseMySQLRepository).findByCategoryIdOrTenantIdOrderByOrder(
                fieldFilterRequestEntry.getCategoryId(), tenantId);
      } catch (BaseException e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
      }

    } else if (null != fieldFilterRequestEntry.getTenantId()) {
      fields = ((CustomFieldDAO) baseMySQLRepository)
              .findByTenantIdOrderByOrder(fieldFilterRequestEntry.getTenantId());
    }
    List<CustomFieldEntry> entries = fields.stream()
            .filter(field -> field.getType() == FieldType.CUSTOM)
            .map(this::convertToEntry)
            .collect(Collectors.toList());
    fetchValuesFromList(entries);
    return entries;
  }

  public List<CustomFieldEntry> filterActive(CustomFieldFilterRequestEntry fieldFilterRequestEntry) {
    List<CustomFieldEntry> fields = filter(fieldFilterRequestEntry);
    fields = fields.stream().filter(BaseOdinEntry::getActive)
            .collect(Collectors.toList());
    return fields;
  }

  private void fetchValuesFromList(List<CustomFieldEntry> entries) {
    entries.stream().filter(entry -> entry.getDataSourceId() != null).forEach(fieldEntry -> {
      log.info("Fetching values from data source for field id {}", fieldEntry.getId());
      try {
        fieldEntry.setValues(
                dataSourceValueService.getValuesByDataSourceId(fieldEntry.getDataSourceId()));
      } catch (BaseException e) {
        log.error(e.getMessage(), e);
        rollbarService.error(e);
      }
    });
  }

  public List<CustomFieldEntry> findFieldsByCategoryId(Long id) throws ResourceNotFoundException {
    log.info("Fetching fields by category id {}", id);
    List<CustomFieldEntry> fieldEntries;
    List<CustomField> fieldList = ((CustomFieldDAO) baseMySQLRepository).findByCategoryIdOrderByOrder(id);
    if (!fieldList.isEmpty()) {
      fieldEntries = fieldList.stream().map(this::convertToEntry).collect(Collectors.toList());
    } else {
      throw new ResourceNotFoundException("No fields found for given ID " + id);
    }
    return fieldEntries;
  }

  public List<CustomFieldEntry> findActiveFieldsByCategoryId(Long id)
          throws ResourceNotFoundException {
    log.info("Fetching fields by category id {}", id);
    List<CustomFieldEntry> fieldEntries;
    List<CustomField> fieldList =
            ((CustomFieldDAO) baseMySQLRepository).findByCategoryIdAndActiveOrderByOrder(id, true);
    if (!fieldList.isEmpty()) {
      fieldEntries = fieldList.stream().map(this::convertToEntry).collect(Collectors.toList());
    } else {
      throw new ResourceNotFoundException("No fields found for given ID " + id);
    }
    return fieldEntries;
  }

  public List<CustomFieldEntry> findFieldsBySubCategoryId(Long id)
          throws ResourceNotFoundException {
    log.info("Fetching fields by category id {}", id);
    List<CustomFieldEntry> fieldEntries;
    List<CustomField> fieldList =
            ((CustomFieldDAO) baseMySQLRepository).findBySubCategoryIdOrderByOrder(id);
    if (!fieldList.isEmpty()) {
      fieldEntries = fieldList.stream().map(this::convertToEntry).collect(Collectors.toList());
    } else {
      throw new ResourceNotFoundException("No fields found for given ID " + id);
    }
    return fieldEntries;
  }

  public List<CustomFieldEntry> findActiveFieldsBySubCategoryId(Long id)
          throws ResourceNotFoundException {
    log.info("Fetching fields by category id {}", id);
    List<CustomFieldEntry> fieldEntries;
    List<CustomField> fieldList =
            ((CustomFieldDAO) baseMySQLRepository).findBySubCategoryIdAndActiveOrderByOrder(id, true);
    if (!fieldList.isEmpty()) {
      fieldEntries = fieldList.stream().map(this::convertToEntry).collect(Collectors.toList());
    } else {
      throw new ResourceNotFoundException("No fields found for given ID " + id);
    }
    return fieldEntries;
  }


  @Secured(allowedRoles = UserRole.ADMIN)
  public List<CustomFieldEntry> filterV2(CustomFieldFilterRequestEntry filterRequest) {
    List<CustomFieldEntry> entries = new ArrayList<>();
    if (filterRequest.getTenantId() != null) {
      entries = findAllByTenantId(filterRequest.getTenantId());
    }
    if (filterRequest.getCategoryId() != null) {
      entries = overrideFields(entries, findAllByCategoryId(filterRequest.getCategoryId()));
    }
    if (filterRequest.getSubCategoryId() != null) {
      entries = overrideFields(entries, findAllBySubCategoryId(filterRequest.getSubCategoryId()));
    }
    entries.sort((o1, o2) -> {
      if (o1.getOrder() == null) {
        return 1;
      }
      if (o2.getOrder() == null) {
        return -1;
      }
      return (int) (o1.getOrder() - o2.getOrder());
    });
    fetchValuesFromList(entries);
    return entries;
  }

  public List<CustomFieldEntry> filterActiveV2(CustomFieldFilterRequestEntry fieldFilterRequestEntry) {
    List<CustomFieldEntry> fields = filterV2(fieldFilterRequestEntry);
    fields = fields.stream().filter(BaseOdinEntry::getActive)
            .collect(Collectors.toList());
    return fields;
  }
  public CustomFieldTreeResponse filterActiveCustomTree(CustomFieldTreeRequestEntry fieldTreeRequestEntry) {
    CustomFieldTreeNode root = new CustomFieldTreeNode(CustomFieldTreeNodeType.ROOT);
    Map<Long, CustomFieldEntry> fields = new HashMap<>();
    List<SubCategoryEntry> subCategoryEntries = subCategoryService.findAllById(fieldTreeRequestEntry.getSubCategoryIds());
    Set<Long> categoryIds = new HashSet<>(fieldTreeRequestEntry.getCategoryIds());
    subCategoryEntries.forEach(entry -> categoryIds.add(entry.getCategoryId()));
    List<CategoryEntry> categoryEntries = categoryService.findAllById(new ArrayList<>(categoryIds));
    Set<Long> tenantIds = new HashSet<>(fieldTreeRequestEntry.getTenantIds());
    categoryEntries.forEach(entry -> tenantIds.add(entry.getTenantId()));
    List<TenantEntry> tenantEntries = tenantService.findAllById(new ArrayList<>(tenantIds));
    populateTenantNodes(root, fields, subCategoryEntries, categoryEntries, tenantEntries);
    return new CustomFieldTreeResponse(root, fields);
  }

  private void populateTenantNodes(CustomFieldTreeNode root, Map<Long, CustomFieldEntry> fields, List<SubCategoryEntry> subCategoryEntries, List<CategoryEntry> categoryEntries, List<TenantEntry> tenantEntries) {
    for(TenantEntry tenantEntry : tenantEntries) {
      CustomFieldTreeNode tenantNode = new CustomFieldTreeNode(CustomFieldTreeNodeType.TENANT, tenantEntry.getName(), tenantEntry.getId());
      List<CustomFieldEntry> tenantFields = filterCustomActiveFields(findAllByTenantId(tenantEntry.getId()));
      populateCategoryNodes(tenantNode, fields, subCategoryEntries, categoryEntries);
      tenantNode.setFieldIds(tenantFields.stream().map(field -> {fields.put(field.getId(),field);return field.getId();}).collect(Collectors.toList()));
      root.getChildren().add(tenantNode);
    }
  }

  private void populateCategoryNodes(CustomFieldTreeNode tenantNode, Map<Long, CustomFieldEntry> fields, List<SubCategoryEntry> subCategoryEntries, List<CategoryEntry> categoryEntries) {
    for(CategoryEntry categoryEntry : categoryEntries.stream().filter(entry -> tenantNode.getCode().equals(entry.getTenantId())).collect(Collectors.toList())) {
      CustomFieldTreeNode categoryNode = new CustomFieldTreeNode(CustomFieldTreeNodeType.CATEGORY, categoryEntry.getName(), categoryEntry.getId());
      List<CustomFieldEntry> categoryFields = filterCustomActiveFields(findAllByCategoryId(categoryEntry.getId()));
      populateSubCategoryNodes(categoryNode, fields, subCategoryEntries);
      categoryNode.setFieldIds(categoryFields.stream().map(field -> {fields.put(field.getId(),field);return field.getId();}).collect(Collectors.toList()));
      tenantNode.getChildren().add(categoryNode);
    }
  }

  private void populateSubCategoryNodes(CustomFieldTreeNode categoryNode, Map<Long, CustomFieldEntry> fields, List<SubCategoryEntry> subCategoryEntries) {
    for(SubCategoryEntry subCategoryEntry : subCategoryEntries.stream().filter(entry -> categoryNode.getCode().equals(entry.getCategoryId())).collect(Collectors.toList())) {
      CustomFieldTreeNode subCategoryNode = new CustomFieldTreeNode(CustomFieldTreeNodeType.SUB_CATEGORY, subCategoryEntry.getName(), subCategoryEntry.getId());
      List<CustomFieldEntry> subCategoryFields = filterCustomActiveFields(findAllBySubCategoryId(subCategoryEntry.getId()));
      subCategoryNode.setFieldIds(subCategoryFields.stream().map(field -> {fields.put(field.getId(),field);return field.getId();}).collect(Collectors.toList()));
      categoryNode.getChildren().add(subCategoryNode);
    }
  }

  private List<CustomFieldEntry> filterCustomActiveFields(List<CustomFieldEntry> fields) {
    return fields.stream().filter(field -> field.getActive() && field.getType()== CUSTOM).collect(Collectors.toList());
  }

  private List<CustomFieldEntry> overrideFields(List<CustomFieldEntry> fields, List<CustomFieldEntry> overrideFields) {
    Map<String, CustomFieldEntry> fieldsMap = fields.stream()
            .collect(Collectors.toMap(CustomFieldEntry::getName, field -> field));

    overrideFields.forEach(overrideField -> {
      if (fieldsMap.containsKey(overrideField.getName())) {
        fields.remove(fieldsMap.get(overrideField.getName()));
      }
    });
    overrideFields.addAll(fields);
    return overrideFields;
  }

  private List<CustomFieldEntry> findAllByTenantId(Long tenantId) {
    return search("tenant.id.eq:" + tenantId);
  }

  private List<CustomFieldEntry> findAllByCategoryId(Long categoryId) {
    return search("category.id.eq:" + categoryId);
  }

  private List<CustomFieldEntry> findAllBySubCategoryId(Long categoryId) {
    return search("subCategory.id.eq:" + categoryId);
  }

  private List<CustomFieldEntry> search(String query) {
    try {
      return search(0, -1, "order", Sort.Direction.ASC.name(), query).getElements();
    } catch (InvalidSeachQueryException e) {
      log.error(e.getMessage(), e);
      rollbarService.error(e);
    }
    return Collections.emptyList();
  }

  @Override
  @Secured(allowedRoles = UserRole.ADMIN)
  public PagedResultEntry<Long, CustomFieldEntry> search(int offset, int limit, String sortBy,
                                                   String sortOrder, String query) throws InvalidSeachQueryException {
    sortBy = sortBy == null ? "order" : sortBy;
    sortOrder = sortOrder == null ? Sort.Direction.ASC.name() : sortOrder;
    return super.search(offset, limit, sortBy, sortOrder, query);
  }

  public void createDefaultFields(Long tenantId) throws BaseException {
    List<CustomFieldEntry> defaultFields = new ArrayList<>();
    defaultFields.add(new CustomFieldEntry("categoryId", LIST, tenantId, 1L, DEFAULT));
    defaultFields.add(new CustomFieldEntry("subCategoryId", LIST, tenantId, 2L, DEFAULT));
    defaultFields.add(new CustomFieldEntry("location", LIST, tenantId, 3L, DEFAULT));
    defaultFields.add(new CustomFieldEntry("title", TEXT, tenantId, 4L, DEFAULT));
    defaultFields.add(new CustomFieldEntry("description", TEXT, tenantId, 5L, DEFAULT));
    defaultFields.add(new CustomFieldEntry("priority", LIST, tenantId, 6L, DEFAULT));
    defaultFields.add(new CustomFieldEntry("dueDate", DATE, tenantId, 7L, DEFAULT));
    defaultFields.add(new CustomFieldEntry("assignee", LIST, tenantId, 8L, DEFAULT));
    defaultFields.add(new CustomFieldEntry("userId", TEXT, tenantId, 9L, DEFAULT));
    bulkCreate(defaultFields);
  }

  @Override
  @Secured(allowedRoles = UserRole.ADMIN)
  public CustomFieldEntry create(CustomFieldEntry entry) throws BaseException {
    return super.create(entry);
  }

  @Override
  @Secured(allowedRoles = UserRole.ADMIN)
  public CustomFieldEntry patchUpdate(Long id, CustomFieldEntry entry) throws BaseException {
    return super.patchUpdate(id, entry);
  }

  @Secured(allowedRoles = UserRole.ADMIN)
  public CustomFieldEntry createOrUpdate(CustomFieldEntry entry) throws BaseException {
    Long id = entry.getId();
    if (id == null) {
      return create(entry);
    } else {
      CustomFieldEntry customFieldEntry = findOneById(id);
      CustomFieldEntry updateEntry = createOrUpdate(entry.getSubCategoryId(), customFieldEntry.getSubCategoryId(), id, entry);
      if (updateEntry == null) {
        updateEntry = createOrUpdate(entry.getCategoryId(), customFieldEntry.getCategoryId(), id, entry);
      }
      if (updateEntry == null) {
        updateEntry = createOrUpdate(entry.getTenantId(), customFieldEntry.getTenantId(), id, entry);
      }
      return updateEntry;
    }
  }

  private CustomFieldEntry createOrUpdate(Long requestId, Long dbId, Long id, CustomFieldEntry customFieldEntry) throws BaseException {
    if (requestId != null) {
      if (dbId != null) {
        return patchUpdate(id, customFieldEntry);
      } else {
        return create(customFieldEntry);
      }
    }
    return null;
  }

  public List<CustomFieldEntry> findAllById(List<Long> fieldIds) {
    List<CustomField> fieldEntities = (List<CustomField>) ((CustomFieldDAO)baseMySQLRepository).findAllById(fieldIds);
    return fieldEntities.stream().map(this::convertToEntry).collect(Collectors.toList());
  }
}
