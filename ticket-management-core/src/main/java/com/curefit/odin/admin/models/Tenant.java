package com.curefit.odin.admin.models;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.curefit.commons.sf.audit.annotation.Audit;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "tenant", uniqueConstraints = {@UniqueConstraint(columnNames = {"name"}),
    @UniqueConstraint(columnNames = {"neo_code"})})
public class Tenant extends BaseMySQLModel {

  /**
  * 
  */
  private static final long serialVersionUID = -4549313157793489123L;
  
  String name;
  
  @Column(name = "neo_code")
  String neoCode;
}
