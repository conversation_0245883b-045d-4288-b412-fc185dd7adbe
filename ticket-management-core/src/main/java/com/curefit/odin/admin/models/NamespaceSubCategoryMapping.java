package com.curefit.odin.admin.models;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import javax.persistence.*;

@Getter
@Setter
@javax.persistence.Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "namespace_sub_category_mapping",
        uniqueConstraints = {@UniqueConstraint(columnNames = {"name"})})
public class NamespaceSubCategoryMapping extends BaseMySQLModel {

    /**
     *
     */
    private static final long serialVersionUID = 2634020024343637595L;

    String name;

    @Column(name = "sub_category_id")
    Long subCategoryId;
}
