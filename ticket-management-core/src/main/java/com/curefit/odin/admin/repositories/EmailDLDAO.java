package com.curefit.odin.admin.repositories;

import com.curefit.commons.sf.repository.BaseMySQLRepository;
import com.curefit.odin.utils.models.EmailDL;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */

@Repository
public interface EmailDLDAO extends BaseMySQLRepository<EmailDL> {

  List<EmailDL> findByEmail(String email);

  List<EmailDL> findByDl(String dl);
}
