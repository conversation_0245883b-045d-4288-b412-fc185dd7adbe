package com.curefit.odin.admin.controllers;

import java.util.List;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.curefit.common.data.exception.BaseException;
import com.curefit.odin.admin.models.DataSourceValue;
import com.curefit.odin.admin.pojo.DataSourceValueEntry;
import com.curefit.odin.admin.service.DataSourceValueService;

@RestController
@RequestMapping("/data_source_value")
public class DataSourceValueController
    extends BaseOdinController<DataSourceValue, DataSourceValueEntry> {

  public DataSourceValueController(DataSourceValueService datasourceValueService) {
    super(datasourceValueService);
  }

  @RequestMapping(method = RequestMethod.DELETE, value = "/{id}")
  public ResponseEntity<DataSourceValueEntry> deleteById(@PathVariable Long id)
      throws BaseException {
    return new ResponseEntity<>(((DataSourceValueService) baseMySQLService).deleteById(id),
        HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET)
  public ResponseEntity<List<DataSourceValueEntry>> getByDataSourceId(
      @RequestParam Long dataSourceId) throws BaseException {
    return new ResponseEntity<>(
        ((DataSourceValueService) baseMySQLService).getByDataSourceId(dataSourceId), HttpStatus.OK);
  }

  @RequestMapping(method = RequestMethod.GET, value = "/active")
  public ResponseEntity<List<DataSourceValueEntry>> getActiveByDataSourceId(
      @RequestParam Long dataSourceId) throws BaseException {
    return new ResponseEntity<>(
        ((DataSourceValueService) baseMySQLService).getActiveByDataSourceId(dataSourceId),
        HttpStatus.OK);
  }
}
