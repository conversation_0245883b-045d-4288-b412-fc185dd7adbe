package com.curefit.odin.admin.models;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Email;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "user", uniqueConstraints = {@UniqueConstraint(columnNames = {"email_id"})})
public class User extends BaseMySQLModel {

  /**
  *
  */
  private static final long serialVersionUID = -9071365000533926972L;

  @Column(name = "name")
  String name;

  @Email
  @Column(name = "email_id")
  String emailId;

  @Column(name = "mobile_number")
  String mobileNumber;

  String tenant;

  String designation;

  @Column(name = "is_dl")
  Boolean isDl;

  @Column(name = "member_count")
  Integer memberCount;
}
