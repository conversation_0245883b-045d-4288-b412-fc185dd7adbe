package com.curefit.odin.admin.repositories;

import com.curefit.commons.sf.repository.BaseMySQLRepository;
import com.curefit.odin.admin.models.QueueLocationAssigneesMapping;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * <AUTHOR>
 */

@Repository
public interface LocationAssigneeMappingDao extends BaseMySQLRepository<QueueLocationAssigneesMapping> {

  Optional<QueueLocationAssigneesMapping> findByLocationCodeAndAssigneeQueue(Long locationCode, Long queueId);

}
