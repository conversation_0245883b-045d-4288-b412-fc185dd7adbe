package com.curefit.odin.admin.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.models.SLAMapping;
import com.curefit.odin.admin.models.SubCategory;
import com.curefit.odin.admin.pojo.*;
import com.curefit.odin.admin.repositories.SubCategoryDAO;
import com.curefit.odin.enums.Priority;
import com.curefit.odin.enums.UserRole;
import com.curefit.odin.user.pojo.TicketEntry;
import com.curefit.odin.user.service.TicketService;
import com.curefit.odin.utils.Secured;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SubCategoryService extends BaseMySQLService<SubCategory, SubCategoryEntry> {

    @Autowired
    private CategoryService ticketCategoryService;

    @Autowired
    private SubCategoryDAO subCategoryDAO;

    @Autowired
    private SLAService slaService;

    @Autowired
    RollbarService rollbarService;

    @Autowired
    DefaultAssigneeService defaultAssigneeService;

    @Autowired
    EscalationConfigService escalationConfigService;

    @Autowired
    PriorityService priorityService;

    @Autowired
    WatcherConfigService watcherConfigService;

    @Autowired
    TicketApprovalConfigService ticketApprovalConfigService;

    @Autowired
    TicketService ticketService;

    public SubCategoryService(SubCategoryDAO crudRepository) {
        super(crudRepository);
    }

    @Override
    public SubCategoryEntry convertToEntry(SubCategory subCategory) {
        SubCategoryEntry subCategoryEntry = super.convertToEntry(subCategory);
        subCategoryEntry.setCategoryId(subCategory.getCategory().getId());
        subCategoryEntry.setTenantId(subCategory.getCategory().getTenant().getId());
        Map<Priority, Integer> slaMapping = slaService.convertToMap(slaService.getSlaMappings(null, subCategory.getId()));
        subCategoryEntry.setPrioritySLAEntries(fetchPrioritySLAEntries(subCategory.getCategory().getTenant().getId(), subCategory.getCategory().getId(), subCategory.getId(), slaMapping));
        DefaultAssigneeEntry defaultAssigneeEntry = defaultAssigneeService.fetchDefaultAssignee(null, subCategory.getId());
        if (defaultAssigneeEntry != null) {
            subCategoryEntry.setAssigneeQueueEntry(defaultAssigneeEntry.getAssigneeQueueEntry());
        }

        EscalationConfigEntry escalationConfigEntry = escalationConfigService.fetchEscalationConfig(null, subCategory.getId());
        if (escalationConfigEntry != null) {
            subCategoryEntry.setEscalationRules(escalationConfigEntry.getEscalationRules());
        }
        WatcherConfigEntry watcherConfigEntry = watcherConfigService.fetchWatcherConfig(null, subCategoryEntry.getId());
        if (watcherConfigEntry != null) {
            subCategoryEntry.setWatchersQueue(watcherConfigEntry.getWatchersQueue());
        }

        TicketApprovalConfigEntry approvalConfigEntry = ticketApprovalConfigService.fetchApprovalConfig(null, subCategoryEntry.getId());
        if (approvalConfigEntry != null) {
            subCategoryEntry.setApprovalQueue(approvalConfigEntry.getApprovalQueue());
        }
        return subCategoryEntry;
    }

    @Override
    public SubCategory convertToEntity(SubCategoryEntry entry) {
        log.info("Converting Ticket Sub Category entry to entity");
        SubCategory convertedEntity = super.convertToEntity(entry);
        Long ticketCategoryId = entry.getCategoryId();
        if (null != ticketCategoryId) {
            try {
                convertedEntity.setCategory(ticketCategoryService.fetchEntityById(ticketCategoryId));
            } catch (BaseException e) {
                log.error(e.getMessage(), e);
                rollbarService.error(e);
            }
        }
        return convertedEntity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Secured(allowedRoles = UserRole.ADMIN)
    @CacheEvict(value = {"categoryBySubCategoryId", "activeCategoryBySubCategoryId"}, allEntries = true)
    public SubCategoryEntry create(SubCategoryEntry entry) throws BaseException {
        SubCategoryEntry subCategoryEntry = super.create(entry);
        if (entry.getPrioritySLAEntries() != null) {
            updatePriorityMappingAndSLA(subCategoryEntry, entry.getPrioritySLAEntries());
        }
        subCategoryEntry.setAssigneeQueueEntry(createOrUpdateDefaultAssignee(subCategoryEntry.getId(), entry.getAssigneeQueueEntry()));
        subCategoryEntry.setEscalationRules(createOrUpdateEscalationRules(subCategoryEntry.getId(), entry.getEscalationRules()));
        subCategoryEntry.setWatchersQueue(createOrUpdateWatcherConfig(subCategoryEntry.getId(), entry.getWatchersQueue()));
        subCategoryEntry.setApprovalQueue(createOrUpdateApprovalConfig(subCategoryEntry.getId(), entry.getApprovalQueue()));

        return subCategoryEntry;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Secured(allowedRoles = UserRole.ADMIN)
    @CacheEvict(value = {"categoryBySubCategoryId", "activeCategoryBySubCategoryId"}, allEntries = true)
    public SubCategoryEntry patchUpdate(Long id, SubCategoryEntry entry) throws BaseException {
        SubCategoryEntry subCategoryEntry = super.patchUpdate(id, entry);
        subCategoryEntry.setAssigneeQueueEntry(createOrUpdateDefaultAssignee(subCategoryEntry.getId(), entry.getAssigneeQueueEntry()));
        subCategoryEntry.setEscalationRules(createOrUpdateEscalationRules(subCategoryEntry.getId(), entry.getEscalationRules()));
        subCategoryEntry.setWatchersQueue(createOrUpdateWatcherConfig(subCategoryEntry.getId(), entry.getWatchersQueue()));
        subCategoryEntry.setApprovalQueue(createOrUpdateApprovalConfig(subCategoryEntry.getId(), entry.getApprovalQueue()));

        if (entry.getPrioritySLAEntries() != null) {
            updatePriorityMappingAndSLA(subCategoryEntry, entry.getPrioritySLAEntries());
        }
        boolean shouldRefreshOpenTickets = entry.getRefreshOpenTickets() != null ? entry.getRefreshOpenTickets() : false;
        if (shouldRefreshOpenTickets) {
            try {
                ticketService.refreshOpenTicketsAfterSlaOrAssigneeUpdate(null, id, null);
            } catch (InvalidSeachQueryException e) {
                log.error("error while refreshing open tickets for subCategoryId {}", id, e);
            }
        }
        return subCategoryEntry;
    }

    private void updatePriorityMappingAndSLA(SubCategoryEntry entry, List<CustomPrioritySLAEntry> prioritySLAEntries) {
        Map<Priority, Integer> prioritySlaInHoursMapping = prioritySLAEntries
                .stream()
                .filter(prioritySLAEntry -> prioritySLAEntry.getSla() != null)
                .collect(Collectors.toMap(prioritySLAEntry -> prioritySLAEntry.getPriorityEntry().getName(), CustomPrioritySLAEntry::getSla));
        Iterable<SLAMapping> slaMappings = slaService.updateSlaMapping(null, entry.getId(), prioritySlaInHoursMapping);
        prioritySLAEntries.forEach(prioritySLAEntry -> {
            try {
                prioritySLAEntry.getPriorityEntry().setSubCategoryId(entry.getId());
                prioritySLAEntry.setPriorityEntry(priorityService.createOrUpdate(prioritySLAEntry.getPriorityEntry()));
            } catch (BaseException e) {
                log.error(e.getMessage(), e);
                rollbarService.error(e);
            }
        });
        entry.setPrioritySLAEntries(fetchPrioritySLAEntries(entry.getTenantId(), entry.getCategoryId(), entry.getId(), slaService.convertToMap(slaMappings)));
    }


    private List<CustomPrioritySLAEntry> fetchPrioritySLAEntries(Long tenantId, Long categoryId, Long subCategoryId, Map<Priority, Integer> slaMapping) {
        PriorityFilterRequestEntry priorityFilterRequestEntry = new PriorityFilterRequestEntry();
        priorityFilterRequestEntry.setTenantId(tenantId);
        priorityFilterRequestEntry.setCategoryId(categoryId);
        priorityFilterRequestEntry.setSubCategoryId(subCategoryId);
        List<PriorityMappingEntry> customPriorityEntries = priorityService.filter(priorityFilterRequestEntry);
        return customPriorityEntries
                .stream()
                .map(customPriorityEntry -> new CustomPrioritySLAEntry(customPriorityEntry, slaMapping.getOrDefault(customPriorityEntry.getName(), null)))
                .collect(Collectors.toList());
    }

    private AssigneeQueueEntry createOrUpdateDefaultAssignee(Long subCategoryId, AssigneeQueueEntry assigneeQueueEntry) throws BaseException {
        if (assigneeQueueEntry == null) {
            return null;
        }
        DefaultAssigneeEntry defaultAssigneeEntry = new DefaultAssigneeEntry();
        defaultAssigneeEntry.setSubCategoryId(subCategoryId);
        defaultAssigneeEntry.setAssigneeQueueEntry(assigneeQueueEntry);
        return defaultAssigneeService.createOrUpdate(defaultAssigneeEntry).getAssigneeQueueEntry();
    }

    private List<EscalationRule> createOrUpdateEscalationRules(Long subCategoryId, List<EscalationRule> escalationRules) throws BaseException {
        if (escalationRules == null) {
            return null;
        }
        EscalationConfigEntry escalationConfigEntry = new EscalationConfigEntry();
        escalationConfigEntry.setSubCategoryId(subCategoryId);
        escalationConfigEntry.setEscalationRules(escalationRules);
        return escalationConfigService.createOrUpdate(escalationConfigEntry).getEscalationRules();
    }

    private AssigneeQueueEntry createOrUpdateWatcherConfig(Long subCategoryId, AssigneeQueueEntry watcherQueue) throws BaseException {
        if (watcherQueue == null) {
            return null;
        }
        WatcherConfigEntry watcherConfigEntry = new WatcherConfigEntry();
        watcherConfigEntry.setSubCategoryId(subCategoryId);
        watcherConfigEntry.setWatchersQueue(watcherQueue);
        return watcherConfigService.createOrUpdate(watcherConfigEntry).getWatchersQueue();
    }

    private AssigneeQueueEntry createOrUpdateApprovalConfig(Long subCategoryId, AssigneeQueueEntry approvalQueue) throws BaseException {
        if (approvalQueue == null) {
            return null;
        }
        TicketApprovalConfigEntry ticketApprovalConfigEntry = new TicketApprovalConfigEntry();
        ticketApprovalConfigEntry.setSubCategoryId(subCategoryId);
        ticketApprovalConfigEntry.setApprovalQueue(approvalQueue);
        return ticketApprovalConfigService.createOrUpdate(ticketApprovalConfigEntry).getApprovalQueue();
    }

    @Cacheable(value = "categoryBySubCategoryId")
    public List<SubCategoryEntry> findByCategoryId(Long ticketCategoryId) {
        log.info("Fetching Ticket Sub Categories under the category {}", ticketCategoryId);
        List<SubCategory> subCategories = subCategoryDAO.findByCategoryId(ticketCategoryId);
        return subCategories.stream().map(this::convertToEntry).collect(Collectors.toList());
    }

    @Cacheable(value = "activeCategoryBySubCategoryId")
    public List<SubCategoryEntry> findActiveByCategoryId(Long ticketCategoryId) {
        log.info("Fetching Ticket Sub Categories under the category {}", ticketCategoryId);
        List<SubCategory> subCategories = subCategoryDAO.findByCategoryIdAndActive(ticketCategoryId, true);
        return subCategories.stream()
                .map(this::convertToEntry)
                .collect(Collectors.toList());
    }

    public List<SubCategoryEntry> findAllById(List<Long> ids) {
        List<SubCategory> subCategories = (List<SubCategory>) subCategoryDAO.findAllById(ids);
        return subCategories.stream()
                .map(this::convertToEntry)
                .collect(Collectors.toList());
    }

    public SubCategoryEntry fetchByCategoryIdAndName(Long categoryId, String name) {
        Optional<SubCategory> subCategoryOptional = ((SubCategoryDAO) super.baseMySQLRepository).findByCategoryIdAndName(categoryId, name);
        return subCategoryOptional.map(this::convertToEntry).orElse(null);
    }
}
