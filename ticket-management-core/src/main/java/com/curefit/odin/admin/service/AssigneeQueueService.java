package com.curefit.odin.admin.service;

import com.curefit.odin.enums.UserRole;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.models.AssigneeQueue;
import com.curefit.odin.admin.pojo.AssigneeQueueEntry;
import com.curefit.odin.admin.repositories.AssigneeQueueDAO;
import com.curefit.odin.utils.Secured;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssigneeQueueService extends BaseMySQLService<AssigneeQueue, AssigneeQueueEntry> {

  public AssigneeQueueService(AssigneeQueueDAO assigneeQueueDAO) {
    super(assigneeQueueDAO);
  }

  @Secured(allowedRoles = UserRole.ADMIN)
  public List<AssigneeQueueEntry> fetchByTenantId(Long tenantId) throws BaseException {
    try {
      return search(0, -1, null, null, "tenantId.eq:" + tenantId + ";active.eq:true").getElements();
    } catch (InvalidSeachQueryException e) {
      log.error(e.getMessage(), e);
      throw new BaseException(e.getMessage());
    }
  }

  @Cacheable(value = "fetchTenantIdForAssigneeQueue", unless = "#result == null")
  public Long fetchTenantIdForAssigneeQueue(Long assigneeQueueId) throws BaseException {
    return findOneById(assigneeQueueId).getTenantId();
  }

  @Override
  @Secured(allowedRoles = UserRole.ADMIN)
  public AssigneeQueueEntry create(AssigneeQueueEntry entry) throws BaseException {
    if (entry.getType() == null)
      entry.setType("STATIC");
    return super.create(entry);
  }

  @Override
  @Secured(allowedRoles = UserRole.ADMIN)
  public AssigneeQueueEntry patchUpdate(Long id, AssigneeQueueEntry entry) throws BaseException {
    return super.patchUpdate(id, entry);
  }
}
