package com.curefit.odin.admin.models;

import com.curefit.odin.admin.pojo.EscalationRule;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.util.List;

/**
 * <AUTHOR>
 */

@Setter
@Getter
@Entity
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "escalation_config")
public class EscalationConfig extends BaseMySQLModel {

  @Column(name = "category_id")
  Long categoryId;

  @Column(name = "sub_category_id")
  Long subCategoryId;

  @Type(type = "json")
  @Column(name = "escalation_rules", columnDefinition = "json")
  List<EscalationRule> escalationRules;
}
