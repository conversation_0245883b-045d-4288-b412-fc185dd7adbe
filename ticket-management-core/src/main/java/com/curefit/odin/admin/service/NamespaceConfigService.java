package com.curefit.odin.admin.service;

import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.odin.admin.models.NamespaceConfig;
import com.curefit.odin.admin.repositories.NamespaceConfigDao;
import com.curefit.odin.utils.pojo.NamespaceConfigEntry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
public class NamespaceConfigService extends BaseMySQLService<NamespaceConfig, NamespaceConfigEntry> {

  public NamespaceConfigService(NamespaceConfigDao namespaceConfigDao) {
    super(namespaceConfigDao);
  }

  @Cacheable(value = "findByNamespace", unless = "#result == null")
  public NamespaceConfigEntry findByNamespace(String namespace) {
    return ((NamespaceConfigDao)baseMySQLRepository).findByName(namespace)
            .map(this::convertToEntry)
            .orElse(null);
  }
}
