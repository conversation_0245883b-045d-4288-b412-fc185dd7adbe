package com.curefit.odin.admin.models;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@Entity
@Table(name = "sub_category", uniqueConstraints = {@UniqueConstraint(columnNames = {"name", "category_id"})})
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SubCategory extends BaseMySQLModel {
    private static final long serialVersionUID = -1896388606518420721L;

    String name;

    @ManyToOne
    @JoinColumn(name = "category_id", referencedColumnName = "id")
    Category category;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "sub_category_id", referencedColumnName = "id")
    List<SLAMapping> slaMappings;

    @Type(type = "json")
    @Column(name = "meta", columnDefinition = "json")
    Map<String, Object> meta;
}
