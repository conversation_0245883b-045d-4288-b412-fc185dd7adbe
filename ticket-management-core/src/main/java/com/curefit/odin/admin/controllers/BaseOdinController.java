package com.curefit.odin.admin.controllers;

import com.curefit.cf.commons.pojo.BaseEntry;
import com.curefit.cf.commons.pojo.PagedResultEntry;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.sf.controller.BaseController;
import com.curefit.commons.sf.model.BaseMySQLEntity;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.persistence.MappedSuperclass;

@MappedSuperclass
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BaseOdinController<T extends BaseMySQLEntity, E extends BaseEntry> extends BaseController<T, E> {

    public BaseOdinController(BaseMySQLService<T, E> baseMySQLService) {
        super(baseMySQLService);
    }

    @RequestMapping(method = RequestMethod.GET, value = "/searchActive")
    public ResponseEntity<PagedResultEntry<Long, E>> searchActive(
            @RequestParam(value = "offset", defaultValue = "0") int offset,
            @RequestParam(value = "limit", defaultValue = "100") int limit,
            @RequestParam(value = "sortBy", defaultValue = "NULL") String sortBy,
            @RequestParam(value = "sortOrder", defaultValue = "ASC") String sortOrder,
            @RequestParam(value = "query", required = false) String query) throws InvalidSeachQueryException, BaseException {
        query = StringUtils.isEmpty(query) ? "active.eq:true" : query + ";active.eq:true";
        return super.search(offset, limit, sortBy, sortOrder, query, null);
    }

}
