package com.curefit.odin.exceptions;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(value = HttpStatus.BAD_REQUEST)
public class InvalidResponseException extends RuntimeException {

  /**
  * 
  */
  private static final long serialVersionUID = 689943752379930430L;

  public InvalidResponseException() {
    super();
  }

  public InvalidResponseException(String message, Throwable cause) {
    super(message, cause);
  }

  public InvalidResponseException(String message) {
    super(message);
  }

  public InvalidResponseException(Throwable cause) {
    super(cause);
  }
}
