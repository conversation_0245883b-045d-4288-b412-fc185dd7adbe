package com.curefit.odin.exceptions;

import com.curefit.common.data.enums.AppStatus;
import com.curefit.common.data.exception.enums.LogType;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;
import com.curefit.common.data.exception.BaseException;

@ResponseStatus(value = HttpStatus.BAD_REQUEST)
public class InvalidDataException extends BaseException {

  /**
  * 
  */
  private static final long serialVersionUID = 689943752379930430L;

  public InvalidDataException() {
    super();
  }

  public InvalidDataException(String message, Throwable cause) {
    super(message, cause);
  }

  public InvalidDataException(String message) {
    super(message, AppStatus.BAD_REQUEST, LogType.WARNING);
  }

  public InvalidDataException(Throwable cause) {
    super(cause);
  }
}
