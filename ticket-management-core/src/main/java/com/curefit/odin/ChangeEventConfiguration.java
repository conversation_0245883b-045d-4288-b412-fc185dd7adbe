package com.curefit.odin;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Configuration
@Component
@Slf4j
@Setter
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE)
@PropertySource("classpath:${spring.profiles.active}/changeevents.properties")
public class ChangeEventConfiguration {

    @Value("${audit.events.queue.name}")
    String auditEventsQueue;

    @Value("${audit.events.queue.batchSize}")
    int auditEventsQueueBatchSize;

    @Value("${audit.events.queue.waitTime}")
    long auditEventsQueueWaitTimeInSec;

    @Value("${notification.events.queue.name}")
    String notificationEventsQueue;

    @Value("${notification.events.queue.batchSize}")
    int notificationEventsQueueBatchSize;

    @Value("${notification.events.queue.waitTime}")
    long notificationEventsQueueWaitTimeInSec;

    @Value("${webhook.events.queue.name}")
    String webhookEventsQueue;

    @Value("${webhook.events.queue.batchSize}")
    int webhookEventsQueueBatchSize;

    @Value("${webhook.events.queue.waitTime}")
    long webhookEventsQueueWaitTimeInSec;

}
