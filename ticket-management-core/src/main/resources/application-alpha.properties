spring.datasource.url=${DB_URL}
spring.datasource.username=${DB_USER}
spring.datasource.password=${DB_PWD}
spring.jpa.hibernate.ddl-auto=none
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true


management.endpoints.web.exposure.include=*

logging.level.org.springframework.web.filter.CommonsRequestLoggingFilter=DEBUG

logfile.path=/logs/ticketing-system

spring.jackson.time-zone: UTC

spring.jpa.generate-ddl=true
spring.jpa.properties.javax.persistence.schema-generation.create-source=metadata
spring.jpa.properties.javax.persistence.schema-generation.scripts.action=create
spring.jpa.properties.javax.persistence.schema-generation.scripts.create-target=create.sql  
hibernate.hbm2ddl.delimiter=";"
spring.jpa.database-platform=org.hibernate.dialect.MySQL5InnoDBDialect
spring.jpa.hibernate.ddl-auto=update

app.environment=alpha
rollbar.disabled=false
rollbar.accessToken=e2d30f547dff49cc9b1a4eec6ce187dd

spring.cache.redis.key-prefix=ODIN:
spring.cache.type=redis
spring.redis.host=carefit-cache-rw.production.cure.fit.internal
spring.redis.port=6379
spring.redis.database=5
spring.redis.ttl=60

management.metrics.export.prometheus.enabled=true
management.metrics.export.prometheus.descriptions=true
management.metrics.enable.root=true
management.ssl.enabled=false
management.address=127.0.0.1
email.enable=true
neo.url=http://neo.curefit.co

maverick.url=http://maverick.production.cure.fit.internal/platform/neo_employee_catalogue
maverick.etl.url=http://maverick.production.cure.fit.internal/etl/platform/neo_employee_catalogue

notification.default.emails=
iris.baseUrl=http://iris.production.cure.fit.internal
email.ticket.assign.creative=EMAIL_ODIN_TICKET_ASSIGN
email.ticket.update.creative=EMAIL_ODIN_TICKET_UPDATE
email.ticket.comment.creative=EMAIL_ODIN_TICKET_COMMENT
email.ticket.attachment.creative=EMAIL_ODIN_TICKET_ATTACHMENT
email.ticket.slaReminder.creative=EMAIL_ODIN_TICKET_SLA_REMINDER
email.ticket.notification.campaign=CUREFIT_ODIN_TICKET_NOTIFICATION

attachment.s3.bucket=cf-platform-odin-prod
mozart.base.url=http://mozart.production.cure.fit.internal/v1
email.dl.refresh.cron.expression=0 55 23 ? * *
email.dl.refresh.enable=true
google.oauth.callback.url=https://ticketing-system.curefit.co/google/oauth/callback
google.oauth.redirect.url=http://odin.curefit.co/
freshdesk.baseUrl=https://curefit.freshdesk.com
freshdesk.apiKey=${FRESHDESK_API_KEY}
freshdesk.source.email=<EMAIL>
google.location.url=https://maps.googleapis.com/maps/api/geocode/json
google.location.api.key=${GOOGLE_LOC_API_KEY}
email.read.cron.expression=0 0/2 * 1/1 * ?
email.read.enable=false
escalation.events.queue.name=production_ticketing_system_escalation
escalation.events.queue.batchSize=10
escalation.events.queue.waitTime=1
ticketing-system.employee.update.queue=production-ticketing-system-employee-update
neo.employee.update.events.queue.batchSize=10
neo.employee.update.events.queue.waitTime=2
mozart.sqs.job.submit.queue=prod-platforms-mozart-job-create
mozart.job.config.id=204b5d9e-5b6b-498b-867e-0a29511041b0
mozart.sla.breach.next.day.job.config.id=ticketing-system-sla-breach-next-day
email.ticket.escalation.creative=EMAIL_ODIN_TICKET_ESCALATION
email.ticket.slaToBeBreachedTomorrow.slaReminder.creative=EMAIL_EMAIL_ODIN_TICKET_SLA_TO_BE_BREACHED_TOMORROW_SLA_REMINDER
google.oauth.token.directory=/opt/odin_oauth_tokens
customer.support.email=<EMAIL>
notification.ignored.emails=<EMAIL>
crypto.key=${CRYTO_KEY}
hr.freshdesk.baseUrl=https://curefithr.freshdesk.com
hr.freshdesk.apiKey=${HR_FRESHDESK_API_KEY}
partner.freshdesk.baseUrl=
partner.freshdesk.apiKey=
apiKey=${API_KEY}
K8S_APP_NAME=ticketing-system
audit.scan.package=com.curefit.odin
encryption.scan.package=com.curefit.odin

identity.url=http://identity.alpha.cure.fit.internal
watchmen.url=http://watchmen.alpha.cure.fit.internal

watchmen.apiKey=${WATCHMEN_API_KEY}

watchmen.membership.queue.name=production-odin-watchmen-membership
watchmen.membership.queue.batchSize=10
watchmen.membership.queue.waitTime=1

sugarfit.freshdesk.baseUrl=https://sugarfit.freshdesk.com
sugarfit.freshdesk.apiKey=${SUGARFIT_FRESHDESK_API_KEY}

odin.email=<EMAIL>

external.neo.baseUrl: http://neo.curefit.co
external.cult-api.baseUrl: http://cultapi.production.internal.cult.fit

retry.maxAttempts: 3
retry.delay: 2000

gymfit.baseUrl=http://gymfit.alpha.cure.fit.internal
gymfit.apiKey=

spring.autoconfigure.exclude= org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration,org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration

spring.flyway.baseline-on-migrate=true
spring.main.allow-circular-references=true

ticketingSystem.baseUrl=http://ticketing-system.alpha.cure.fit.internal
ticketingSystem.user=<EMAIL>

services.report-issues.baseUrl=http://report-issues.production.cure.fit.internal
services.report-issues.apiKey=gauzietheiTua1fu5os0Eehoh0eigheejahcoh1i
crypto.cipher.transformation=AES/CTR/NoPadding

iris.requestQueue=production-iris-campaign
curefit.rashi.redis.port=6379
curefit.rashi.redis.host=platforms-cache.production.cure.fit.internal
curefit.rashi.redis.clusterEnabled=true
services.rashi.baseUrl=http://rashi.production.cure.fit.internal
rashi.sns.topicArn=arn:aws:sns:ap-south-1:035243212545:production-rashi-user-events
rashi.event.prefix=TICKETING_SYSTEM_

external.user-service-v1.baseUrl=http://user-service.alpha.cure.fit.internal/v1
external.user-service-v2.baseUrl=http://user-service-v2.alpha.cure.fit.internal/v1

configstore.enabled=true
configstore.apiKey=0d5ae5de-6679-4cb9-9831-b537f2965799
configstore.url=http://config-store.production.cure.fit.internal
app.name=ticketing-system
services.falcon.baseUrl: http://falcon.production.cure.fit.internal
services.falcon.port: 1433
services.falcon.apiKey: 927fbb0c-0fb5-43ac-a581-4a9f54fe6ca1
services.falcon.etlUrl: http://falcon.production.cure.fit.internal/v1/index/etl