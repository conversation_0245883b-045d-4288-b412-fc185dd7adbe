create table issue_template (
    id bigint not null auto_increment,
    title varchar(50) not null,
    sub_category_id bigint,
    active bool default true,
    priority varchar(64),
    created_by varchar(255),
    last_modified_by varchar(255),
    created_on datetime default CURRENT_TIMESTAMP,
    last_modified_on datetime default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP,
    version bigint default 1,
    primary key (id),
    constraint `title_subCategoryId_index` UNIQUE (`title`, `sub_category_id`),
    index `sub_category_id` (`sub_category_id`),
    foreign key (sub_category_id) references sub_category (id)
) engine=InnoDB;