create TABLE `sprinklr_cult_configuration`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT,
    `l1`               text,
    `sub_l1`           text,
    `status`           text,
    `l2`               text,
    `action_type`      varchar(128),
    `action_value`     JSON,
    `deleted_on`       timestamp DEFAULT NULL,
    `active`           bool DEFAULT TRUE,
    `created_on`       timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `created_by`       varchar(255),
    `last_modified_by` varchar(255),
    `version`          bigint(20),
    PRIMARY KEY (`id`),
    constraint `unique_scc` UNIQUE (`l1`(255), `sub_l1`(255), `status`(255), `l2`(255))
) ENGINE=InnoDB;


create TABLE `sprinklr_cult_custom_field_mapping`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT,
    `sprinklr_custom_field`        text,
    `cult_custom_field`            text,
    `deleted_on`       timestamp DEFAULT NULL,
    `active`           bool DEFAULT TRUE,
    `created_on`       timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `created_by`       varchar(255),
    `last_modified_by` varchar(255),
    `version`          bigint(20),
    PRIMARY KEY (`id`),
    constraint `unique_sccfm` UNIQUE (`sprinklr_custom_field`(255), `cult_custom_field`(255))
) ENGINE=InnoDB;


create TABLE `sprinklr_ticket`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT,
    `user_id`               varchar(255),
    `case_id`               text,
    `case_number`           varchar(255),
    `subject`               text,
    `description`           text,
    `status`                varchar(255),
    `custom_properties`     JSON,
    `contact`               JSON,
    `first_message_id`      text,
    `conversation_id`       text,
    `is_ticket`             bool,
    `deleted_on`            timestamp DEFAULT NULL,
    `active`                bool DEFAULT TRUE,
    `created_on`            timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_modified_on`      timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `created_by`            varchar(255),
    `last_modified_by`      varchar(255),
    `version`               bigint(20),
    PRIMARY KEY (`id`)
) ENGINE=InnoDB;



