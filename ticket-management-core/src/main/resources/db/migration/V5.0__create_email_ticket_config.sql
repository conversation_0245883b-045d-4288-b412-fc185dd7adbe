create table email_ticket_config (
    id bigint not null auto_increment,
    email varchar(128) not null,
    tenant_id bigint not null,
    category_id bigint not null,
    sub_category_id bigint,
    location_id  bigint,
    priority varchar(32),
    active bool,
    created_by varchar(255),
    last_modified_by varchar(255),
    created_on datetime,
    last_modified_on datetime,
    version bigint,
    primary key (id),
    foreign key (tenant_id) references tenant (id),
    foreign key (category_id) references category (id),
    foreign key (sub_category_id) references sub_category (id),
    foreign key (location_id) references location (id)
) engine=InnoDB;

alter table email_ticket_config add constraint uq_email unique (email);