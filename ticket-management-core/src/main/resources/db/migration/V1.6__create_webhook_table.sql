create table webhook_target_config (
  id bigint not null auto_increment,
  type varchar(36), -- HTT<PERSON>, Queue, Email
  config text,
  version bigint,
  active bool,
  created_by varchar(255),
  created_on datetime,
  last_modified_on datetime,
  last_modified_by varchar(255),
  primary key (id)
) engine=InnoDB;

create table webhook_config (
  id bigint not null auto_increment,
  name varchar(128),
  description varchar(512),
  filter text,
  events_meta text,
  exclude_body bit(1) default true,
  webhook_target_config_id bigint(20),
  version bigint,
  active bool,
  created_by varchar(255),
  created_on datetime,
  last_modified_on datetime,
  last_modified_by varchar(255),
  primary key (id),
  foreign key (webhook_target_config_id) references webhook_target_config (id)
) engine=InnoDB;