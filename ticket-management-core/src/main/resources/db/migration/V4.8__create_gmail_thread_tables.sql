create table gmail_thread (
    id bigint not null auto_increment,
    thread_id varchar(128) not null,
    ticket_id bigint,
    active bool,
    created_by varchar(255),
    last_modified_by varchar(255),
    created_on datetime,
    last_modified_on datetime,
    version bigint,
    primary key (id),
    foreign key (ticket_id) references ticket (id)
) engine=InnoDB;

alter table gmail_thread add constraint uq_thread unique (thread_id);