create table queue_location_assignees_map (
  id bigint not null auto_increment,
  assignee_queue_id bigint,
  location_type varchar(36),
  location_code varchar(255),
  assignees JSON,
  version bigint,
  active bool,
  created_by varchar(255),
  created_on datetime,
  last_modified_on datetime,
  last_modified_by varchar(255),
  primary key (id),
  foreign key (assignee_queue_id) references assignee_queue (id)
) engine=InnoDB;