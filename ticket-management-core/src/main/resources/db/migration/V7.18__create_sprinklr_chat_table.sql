create TABLE `sprinklr_chat_v2`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT,
    `text`                    text,
    `brand_post`              tinyint(1),
    `conversation_id`         text,
    `parent_message_id`       text,
    `message_id`              text,
    `associated_case_number`  bigint(20),
    `message`                 JSON,
    `deleted_on`              timestamp DEFAULT NULL,
    `active`                tinyint(1) DEFAULT 1,
    `created_on`            timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_modified_on`      timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `created_by`            varchar(255),
    `last_modified_by`      varchar(255),
    `version`               bigint(20),
    PRIMARY KEY (`id`)
) ENGINE=InnoDB;
