create table namespace_config(id bigint not null auto_increment, name varchar(50) not null, base_url varchar(255) not null,
created_by varchar(255),last_modified_by varchar(255),created_on datetime, last_modified_on datetime,version bigint, active bool ,
primary key (id))engine=InnoDB;

alter table namespace_config add constraint uq_namespace unique(name);

alter table namespace_tenant_mapping add foreign key (name) REFERENCES namespace_config(name);