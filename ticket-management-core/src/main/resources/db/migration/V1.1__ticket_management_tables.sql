create table if not exists hibernate_sequence(next_val bigint(20) default null)engine=InnoDB;

insert into hibernate_sequence values(1);

create table if not exists tenant(id bigint not null auto_increment,name varchar(64) not null,
neo_code varchar(255),created_by varchar(255),last_modified_by varchar(255),
created_on datetime,last_modified_on datetime,version bigint,active bool ,primary key (id))engine=InnoDB;
alter table tenant add constraint uq_tenant unique(name);
ALTER TABLE tenant ADD CONSTRAINT uq_neo_code unique(neo_code);

create table if not exists category(id bigint not null auto_increment,category_name varchar(64) not null,
tenant_id bigint not null,active bool ,created_by varchar(255),last_modified_by varchar(255),created_on datetime,
last_modified_on datetime,version bigint,primary key (id),foreign key (tenant_id) references tenant (id))engine=InnoDB;
alter table category add constraint uq_category unique(category_name,tenant_id);

create table if not exists sub_category(id bigint not null auto_increment,sub_category_name varchar(64) not null,
sla_in_hours int ,category_id bigint not null,active bool ,created_by varchar(255),last_modified_by varchar(255),
created_on datetime,last_modified_on datetime,version bigint,primary key (id),foreign key (category_id)
references category (id))engine=InnoDB;
alter table sub_category add constraint uq_sub_category unique(sub_category_name, category_id);

create table if not exists field(id bigint not null auto_increment,name varchar(62) not null,data_type varchar(64) not null,mandatory bool ,
tenant_id bigint,category_id bigint,sub_category_id bigint,active bool ,
created_by varchar(255),last_modified_by varchar(255),created_on datetime,last_modified_on datetime,version bigint,primary key (id),
foreign key (category_id) references category (id),foreign key (sub_category_id)
references sub_category (id),foreign key (tenant_id) references tenant (id))engine=InnoDB;
alter table field add constraint uq_field_cat unique(name, category_id);
alter table field add constraint uq_field_subcat unique(name, sub_category_id);
alter table field add constraint uq_field_tenant unique(name, tenant_id);

create table if not exists role(id bigint not null auto_increment,name varchar(64) not null,
tenant_id bigint not null,created_by varchar(255),last_modified_by varchar(255),created_on datetime,last_modified_on datetime,
version bigint,active bool ,primary key (id),
foreign key (tenant_id) references tenant (id))engine=InnoDB;
alter table role add constraint uq_role unique(name,tenant_id);

create table if not exists default_assignee(id bigint not null auto_increment,role_id bigint not null,
category_id bigint,sub_category_id bigint,created_by varchar(255),last_modified_by varchar(255),created_on datetime,last_modified_on datetime,version bigint,
active bool ,primary key (id),foreign key (sub_category_id) references sub_category (id),
foreign key (category_id) references category (id),
foreign key (role_id) references role (id))engine=InnoDB;
alter table default_assignee add constraint uq_default_assignee unique(role_id,sub_category_id);
alter table default_assignee add constraint uq_default_assignee_category unique(role_id, category_id);


create table if not exists user(id bigint not null auto_increment, name varchar(50) not null, email_id varchar(50),
created_by varchar(255),last_modified_by varchar(255),created_on datetime, last_modified_on datetime,version bigint, active bool , primary key (id))engine=InnoDB;
alter table user add constraint uq_user unique(email_id);

create table if not exists ticket(id bigint not null auto_increment, status int not null,
 description text, due_date datetime, title varchar(50), active bool,labels JSON,closed_at datetime,location varchar(64),
 parent_ticket_id bigint, category_id bigint not null, sub_category_id bigint,priority int DEFAULT '0',role_id bigint,assignee varchar(255),
 created_by varchar(255),last_modified_by varchar(255),created_on datetime, last_modified_on datetime,version bigint, primary key (id),
 foreign key(category_id) references category (id), foreign key(sub_category_id) references sub_category (id),
 foreign key(role_id) references role (id),
 foreign key(parent_ticket_id) references ticket (id))engine=InnoDB;

create table if not exists field_data(id bigint not null auto_increment, field_id bigint not null, ticket_id bigint,
value varchar(255),created_by varchar(255),last_modified_by varchar(255),created_on datetime, last_modified_on datetime,version bigint, active bool , primary key (id),
foreign key(ticket_id) references ticket (id), foreign key(field_id) references field (id))engine=InnoDB;
alter table field_data add constraint uq_field_data unique(ticket_id, field_id);

create table if not exists ticket_watcher(id bigint not null auto_increment, ticket_id bigint , user_id varchar(255),created_by varchar(255),
last_modified_by varchar(255),created_on datetime, last_modified_on datetime,version bigint,active bool,
primary key (id), foreign key(ticket_id) references ticket (id))engine=InnoDB;
alter table ticket_watcher add constraint uq_ticket_watcher_data unique(ticket_id, user_id);

create table if not exists comment(id bigint not null auto_increment,comment text,parent_comment_id bigint,
ticket_id bigint not null,created_by varchar(255),last_modified_by varchar(255),created_on datetime, 
last_modified_on datetime,version bigint, active bool ,primary key (id),foreign key(ticket_id) references
ticket (id),foreign key(parent_comment_id) references comment (id))engine=InnoDB;

create table if not exists attachment(id bigint not null auto_increment,url varchar(255),description text,
comment_id bigint,ticket_id bigint,created_by varchar(255),last_modified_by varchar(255),created_on datetime, 
last_modified_on datetime,version bigint, active bool ,name varchar(255),
primary key (id),foreign key(ticket_id) references
ticket (id),foreign key(comment_id) references comment (id))engine=InnoDB;

create table if not exists REVINFO(id bigint not null,timestamp bigint(20) not null,PRIMARY KEY (id))
engine=InnoDB;


alter table category change category_name name varchar(64);
alter table sub_category change sub_category_name name varchar(64);
alter table field add column data_source_id bigint;
ALTER TABLE field add column order_by bigint;

create table if not exists data_source(id bigint not null auto_increment,name varchar(64),
is_static bool,url varchar(255),root_path varchar(64), headers LONGTEXT,key_mapping LONGTEXT,sync_duration_in_hours bigint,created_by varchar(255),last_modified_by varchar(255),
created_on datetime,last_modified_on datetime,version bigint,active bool ,primary key (id))engine=InnoDB;

create table if not exists data_source_value(id bigint not null auto_increment,reference_id varchar(255),
data_source_id bigint,value varchar(255), metadata TEXT,created_by varchar(255),last_modified_by varchar(255),
created_on datetime,last_modified_on datetime,version bigint,active bool ,
primary key (id),foreign key (data_source_id) references data_source (id))engine=InnoDB;
alter table data_source_value add constraint uq_data_source_value1 unique(data_source_id,reference_id);
alter table data_source_value add constraint uq_data_source_value2 unique(data_source_id,value);


ALTER TABLE ticket ADD (fieldJson JSON);
ALTER TABLE field ADD FOREIGN KEY (data_source_id) REFERENCES data_source(id);
ALTER TABLE ticket ADD column tenant_id bigint(20) not null;
ALTER TABLE ticket ADD FOREIGN KEY (tenant_id) REFERENCES tenant(id);

create table if not exists filter(id bigint not null auto_increment,name varchar(128) not null,created_by varchar(255),
created_on datetime,last_modified_on datetime,last_modified_by varchar(255),version bigint,active bool ,value text, primary key (id))engine=InnoDB;

create table if not exists sla_mapping(id bigint not null auto_increment,category_id bigint,sub_category_id bigint,
sla_in_hours int,priority varchar(64),created_by varchar(255),last_modified_by varchar(255),created_on datetime,last_modified_on datetime,
version bigint,active bool ,primary key (id),foreign key (category_id) references category (id))engine=InnoDB;

alter table sub_category drop column sla_in_hours;

ALTER TABLE ticket MODIFY COLUMN status varchar(100) not null;
ALTER TABLE ticket MODIFY COLUMN priority varchar(100) DEFAULT 'OPEN';

create table if not exists location_source(id bigint not null auto_increment,name varchar(64), tenant_id bigint(20), type varchar(64),url varchar(255),headers LONGTEXT,key_mapping LONGTEXT, root_path varchar(64), sync_duration_in_hours bigint,created_by varchar(255),last_modified_by varchar(255),
created_on datetime,last_modified_on datetime,version bigint,active bool, primary key (id), foreign key (tenant_id) references tenant (id))engine=InnoDB;

create table if not exists location(id bigint not null auto_increment,
location_source_id bigint not null,reference_id varchar(255),center_id varchar(255),center_name varchar(255), latitude double, longitude double, address text, 
city_id varchar(255),city_name varchar(255),country_id varchar(255),country_name varchar(255),
cluster_id varchar(255),cluster_name varchar(255),neo_code varchar(255),created_by varchar(255),last_modified_by varchar(255),
created_on datetime,last_modified_on datetime,version bigint,active bool ,
primary key (id),foreign key (location_source_id) references location_source (id))engine=InnoDB;
alter table location add constraint uq_data_source_value1 unique(location_source_id,reference_id);


create table if not exists namespace_tenant_mapping(id bigint not null auto_increment, name varchar(50) not null,tenant_id bigint(20),
created_by varchar(255),last_modified_by varchar(255),created_on datetime, last_modified_on datetime,version bigint, active bool , primary key (id))engine=InnoDB;


