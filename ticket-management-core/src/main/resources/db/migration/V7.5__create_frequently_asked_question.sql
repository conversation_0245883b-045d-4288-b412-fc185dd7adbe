create table frequently_asked_question (
    id bigint not null auto_increment,
    question varchar(50) not null,
    answer varchar(50) not null,
    issue_template_id bigint,
    active bool default true,
    created_by varchar(255),
    last_modified_by varchar(255),
    created_on datetime default CURRENT_TIMESTAMP,
    last_modified_on datetime default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP,
    version bigint default 1,
    primary key (id),
    constraint `question_issue_template_id_index` UNIQUE (`question`, `issue_template_id`),
    index `issue_template_id` (`issue_template_id`),
    foreign key (issue_template_id) references issue_template (id)
) engine=InnoDB;