create table namespace_sub_category_mapping (
    id bigint not null auto_increment,
    name varchar(50) not null,
    sub_category_id bigint,
    active bool default true,
    created_by var<PERSON><PERSON>(255),
    last_modified_by varchar(255),
    created_on datetime default CURRENT_TIMESTAMP,
    last_modified_on datetime default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP,
    version bigint default 1,
    primary key (id),
    constraint `name_subCategoryId_index` UNIQUE (`name`, `sub_category_id`),
    foreign key (sub_category_id) references sub_category (id)
) engine=InnoDB;