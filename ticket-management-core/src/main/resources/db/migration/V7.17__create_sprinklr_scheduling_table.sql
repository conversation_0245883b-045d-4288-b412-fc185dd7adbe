create TABLE `sprinklr_scheduling`
(
    `id`                    bigint(20) NOT NULL AUTO_INCREMENT,
    `center_id`             bigint(20) NOT NULL,
    `workout_id`            bigint(20) NOT NULL,
    `time_slot_id`          bigint(20) NOT NULL,
    `counter`               bigint(20),
    `scheduling_info`         JSO<PERSON>,
    `deleted_on`            timestamp DEFAULT NULL,
    `active`                bool DEFAULT TRUE,
    `created_on`            timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_modified_on`      timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `created_by`            varchar(255),
    `last_modified_by`      varchar(255),
    `version`               bigint(20),
    UNIQUE KEY `uq_center_workout_timeslot` (`center_id`, `workout_id`, `time_slot_id`),
    PRIMARY KEY (`id`)
) ENGINE=InnoDB;
