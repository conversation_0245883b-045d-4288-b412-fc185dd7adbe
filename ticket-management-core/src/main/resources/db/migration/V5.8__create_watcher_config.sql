create table watcher_config (
    id bigint not null auto_increment,
    category_id bigint,
    sub_category_id bigint,
    watchers_queue_id bigint not null,
    active bool,
    created_by varchar(255),
    last_modified_by varchar(255),
    created_on datetime,
    last_modified_on datetime,
    version bigint,
    primary key (id),
    foreign key (category_id) references category (id),
    foreign key (sub_category_id) references sub_category (id)
) engine=InnoDB;

alter table watcher_config add constraint uq_watcher_config_category unique(category_id);
alter table watcher_config add constraint uq_watcher_config_sub_category unique(sub_category_id);