create index status on ticket (status);
create index location on ticket (location);
create index source_key on ticket (source, source_ref_id);
create index due_date on ticket (due_date);
create index created_on on ticket (created_on);

create index neo_code on tenant (neo_code);

create index priority on sla_mapping (priority);
create index category_priority_key on sla_mapping (category_id, priority);
create index sub_category_priority_key on sla_mapping (sub_category_id, priority);

create index queue_location_key on queue_location_assignees_map (assignee_queue_id, location_code, location_type);

create index country_key on location (location_source_id, country_id);
create index city_key on location (location_source_id, city_id);
create index cluster_key on location (location_source_id, cluster_id);
create index neo_code_key on location (location_source_id, neo_code);
create index center_id_key on location (location_source_id, center_id);

alter table sla_mapping add foreign key (sub_category_id) REFERENCES sub_category(id);
