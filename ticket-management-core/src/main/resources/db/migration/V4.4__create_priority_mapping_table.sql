create table priority_mapping (
    id bigint not null auto_increment,
    name varchar(32) not null,
    tenant_id bigint,
    category_id bigint,
    sub_category_id bigint,
    order_by bigint,
    active bool,
    created_by varchar(255),
    last_modified_by varchar(255),
    created_on datetime,
    last_modified_on datetime,
    version bigint,
    primary key (id),
    foreign key (category_id) references category (id),
    foreign key (sub_category_id) references sub_category (id),
    foreign key (tenant_id) references tenant (id)
) engine=InnoDB;

alter table priority_mapping add constraint uq_priority_category unique (name, category_id);
alter table priority_mapping add constraint uq_priority_sub_category unique (name, sub_category_id);
alter table priority_mapping add constraint uq_priority_tenant unique (name, tenant_id);