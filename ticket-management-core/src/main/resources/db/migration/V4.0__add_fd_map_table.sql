create table odin_type_fd_type_map (
  id bigint not null auto_increment,
  tenant_id bigint,
  category_id bigint,
  sub_category_id bigint,
  fd_cf_type varchar(255),
  fd_level1 varchar(255),
  fd_level2 varchar(255),
  fd_level3 varchar(255),
  version bigint,
  active bool,
  created_by varchar(255),
  created_on datetime,
  last_modified_on datetime,
  last_modified_by varchar(255),
  primary key (id)
) engine=InnoDB;


create table fd_type_odin_type_map (
  id bigint not null auto_increment,
  fd_cf_type varchar(255),
  fd_level1 varchar(255),
  fd_level2 varchar(255),
  fd_level3 varchar(255),
  tenant_id bigint,
  category_id bigint,
  sub_category_id bigint,
  priority varchar(64),
  version bigint,
  active bool,
  created_by varchar(255),
  created_on datetime,
  last_modified_on datetime,
  last_modified_by varchar(255),
  primary key (id)
) engine=InnoDB;