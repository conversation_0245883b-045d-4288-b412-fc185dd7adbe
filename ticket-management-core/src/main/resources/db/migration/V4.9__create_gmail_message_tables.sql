create table gmail_message (
    id bigint not null auto_increment,
    message_id varchar(128) not null,
    entity_id bigint,
    entity_name varchar(32),
    active bool,
    created_by varchar(255),
    last_modified_by varchar(255),
    created_on datetime,
    last_modified_on datetime,
    version bigint,
    primary key (id)
) engine=InnoDB;

alter table gmail_message add constraint uq_message_entity unique (message_id, entity_id);