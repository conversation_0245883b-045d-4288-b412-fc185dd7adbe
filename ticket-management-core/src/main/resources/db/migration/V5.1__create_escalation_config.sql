create table escalation_config (
    id bigint not null auto_increment,
    category_id bigint,
    sub_category_id bigint,
    escalation_rules JSON not null,
    active bool,
    created_by varchar(255),
    last_modified_by varchar(255),
    created_on datetime,
    last_modified_on datetime,
    version bigint,
    primary key (id),
    foreign key (category_id) references category (id),
    foreign key (sub_category_id) references sub_category (id)
) engine=InnoDB;

alter table escalation_config add constraint uq_escalation_config_category unique(category_id);
alter table escalation_config add constraint uq_escalation_config_sub_category unique(sub_category_id);