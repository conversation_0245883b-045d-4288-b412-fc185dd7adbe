<?xml version="1.0" encoding="UTF-8"?>
<configuration>

  <springProperty name="logfile.path" source="logfile.path" defaultValue="logs/"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>
                [%date] [%-5level] [%t] [%logger{0}:%L] [%X{X-B3-TraceId:-}] [%X{X-B3-SpanId:-}] [rID=%X{X-Request-Id}] - %msg%n
            </Pattern>
        </layout>
    </appender>

    <appender name="ROLLING-FILE"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>
                [%date] [%-5level] [%t] [%logger{0}:%L] [%X{X-B3-TraceId:-}] [%X{X-B3-SpanId:-}] [rID=%X{X-Request-Id}] - %msg%n
            </Pattern>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${logfile.path}/ticket-management.%d.log</fileNamePattern>
        </rollingPolicy>

    </appender>

    <logger name="com.curefit" level="DEBUG" additivity="false">
        <appender-ref ref="ROLLING-FILE" />
        <appender-ref ref="STDOUT" />
    </logger>

    <root level="info">
        <appender-ref ref="ROLLING-FILE" />
        <appender-ref ref="STDOUT" />
    </root>

</configuration>
