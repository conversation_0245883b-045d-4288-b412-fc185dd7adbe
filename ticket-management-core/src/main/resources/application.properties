ENVIRONMENT=local
LOG_LEVEL=DEBUG
FRESHDESK_API_KEY=********************
HR_FRESHDESK_API_KEY=1ZuRRnRGZld39B4tGt
API_KEY=556e736d-0499-4aa2-95f8-1cb6e5786829
GOOGLE_LOC_API_KEY=AIzaSyB0qwp9lC-3weu6KuBxlBu_9I37sr6tSk0

spring.profiles.active=${ENVIRONMENT}
app.environment=${ENVIRONMENT}

spring.datasource.url=****************************************************
spring.datasource.username=root
spring.datasource.password=root
spring.jpa.hibernate.ddl-auto=create
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true

management.endpoints.web.exposure.include=*

logging.level.org.springframework.web: DEBUG
logging.level.org.springframework.web.filter.CommonsRequestLoggingFilter=DEBUG
spring.jackson.time-zone: UTC

spring.jpa.generate-ddl=true
spring.jpa.properties.javax.persistence.schema-generation.create-source=metadata
spring.jpa.properties.javax.persistence.schema-generation.scripts.action=create
spring.jpa.properties.javax.persistence.schema-generation.scripts.create-target=create.sql
hibernate.hbm2ddl.delimiter=";"
spring.jpa.database-platform=org.hibernate.dialect.MySQL5InnoDBDialect
spring.jpa.hibernate.ddl-auto=update

#spring.jpa.show-sql=true
#spring.jpa.properties.hibernate.format_sql=true
rollbar.disabled=true
rollbar.accessToken=905dca2

spring.cache.redis.key-prefix=THOR:
spring.cache.type=redis
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.ttl=60

aws.region=ap-south-1

management.metrics.export.prometheus.enabled=true
management.metrics.export.prometheus.descriptions=true
management.metrics.enable.root=true
management.ssl.enabled=false
management.address=127.0.0.1

spring.jpa.properties.org.hibernate.envers.default_schema=audit
spring.jpa.properties.org.hibernate.envers.audit_table_suffix=_audit

spring.sleuth.propagation-keys=X-Request-Id
spring.sleuth.log.slf4j.whitelisted-mdc-keys=X-Request-Id

neo.url=http://neo.stage.curefit.co:8080

maverick.url=http://maverick.stage.curefit.co/platform/neo_employee_catalogue

mozart.base.url=http://mozart.stage.curefit.co/v1

#notification properties TODO: need to move to different file ?
email.enable=false
notification.default.emails=
iris.baseUrl=http://iris.stage.curefit.co
email.ticket.assign.creative=EMAIL_ODIN_TICKET_ASSIGN
email.ticket.update.creative=EMAIL_ODIN_TICKET_UPDATE
email.ticket.comment.creative=EMAIL_ODIN_TICKET_COMMENT
email.ticket.attachment.creative=EMAIL_ODIN_TICKET_ATTACHMENT
email.ticket.notification.campaign=CUREFIT_ODIN_TICKET_NOTIFICATION

attachment.s3.bucket=cf-platform-odin-stage

email.dl.refresh.cron.expression=0 55 11 ? * *
email.dl.refresh.enable=false
google.oauth.callback.url=http://localhost:8080/google/oauth/callback
google.oauth.redirect.url=http://odin.stage.curefit.co/

freshdesk.baseUrl=https://curefit.freshdesk.com
freshdesk.apiKey=********************
freshdesk.source.email=<EMAIL>
google.location.url=https://maps.googleapis.com/maps/api/geocode/json
google.location.api.key=AIzaSyB0qwp9lC-3weu6KuBxlBu_9I37sr6tSk0
email.read.cron.expression=0 0/2 * 1/1 * ?
email.read.enable=false

escalation.events.queue.name=stage-platforms-odin-escalation
escalation.events.queue.batchSize=10
escalation.events.queue.waitTime=1
ticketing-system.employee.update.queue=stage-ticketing-system-employee-update
neo.employee.update.events.queue.batchSize=10
neo.employee.update.events.queue.waitTime=2
mozart.job.config.id=f6edb8c7-c4e8-41d0-a901-df877e8d69e8
mozart.sla.breach.in.24.hours.job.config.id=ticketing-system-sla-breach-in-next-24-hours
email.ticket.escalation.creative=EMAIL_ODIN_TICKET_ESCALATION
google.oauth.token.directory=tokens
customer.support.email=<EMAIL>
notification.ignored.emails=<EMAIL>

crypto.key=
hr.freshdesk.baseUrl=https://curefithr.freshdesk.com
hr.freshdesk.apiKey=1ZuRRnRGZld39B4tGt

partner.freshdesk.baseUrl=
partner.freshdesk.apiKey=

external.neo.baseUrl: http://localhost:5037
external.cult-api.baseUrl: http://localhost:5005
retry.maxAttempts: 3
retry.delay: 2000


spring.autoconfigure.exclude= org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration,org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration

ticketingSystem.baseUrl=http://localhost:13000
ticketingSystem.user=<EMAIL>

services.report-issues.baseUrl=http://report-issues.production.cure.fit.internal
services.report-issues.apiKey=gauzietheiTua1fu5os0Eehoh0eigheejahcoh1i
crypto.cipher.transformation=AES/CTR/NoPadding

iris.requestQueue=stage-iris-campaign
rashi.event.prefix=TICKETING_SYSTEM
rashi.sns.topicArn=arn:aws:sns:ap-south-1:035243212545:stage-rashi-user-events