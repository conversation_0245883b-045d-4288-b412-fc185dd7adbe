package com.curefit.ticket.management.service.test;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.ResourceNotFoundException;
import com.curefit.commons.sf.repository.BaseMySQLRepository;
import com.curefit.odin.admin.service.TenantService;
import com.curefit.odin.exceptions.InvalidDataException;
import com.curefit.odin.user.models.Comment;
import com.curefit.odin.user.pojo.AttachmentEntry;
import com.curefit.odin.user.pojo.CommentEntry;
import com.curefit.odin.user.repositories.CommentDAO;
import com.curefit.odin.user.service.AttachmentService;
import com.curefit.odin.user.service.CommentService;
import com.curefit.odin.user.service.TicketService;
import com.curefit.ticket.management.test.helper.PojoGenerator;

@RunWith(MockitoJUnitRunner.class)
public class MessageServiceTest {
	private static final long TICKET_ID = 4L;

	private static final long SUB_CATEGORY_ID = 3L;

	private static final long CATEGORY_ID = 2L;

	private static final long MESSAGE_ID = 8L;

	private static final long ATTACHMENT_ID = 6L;

	private static final long ROLE_ID = 4L;
	private static final long ENTITY_ID = 4L;

	@Mock
	private CommentDAO commentDao;
	@Mock
	private AttachmentService attachmentService;
	@Mock
	private TicketService ticketService;
	@Mock
	private BaseMySQLRepository<Comment> baseMySQLRepository;
	@Mock
	private TenantService tenantService;
	@InjectMocks
	private CommentService messageService;

	@Before
	public void initMocks() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void convertToEntryTest() {
		List<AttachmentEntry> attachments = PojoGenerator.getAttachmentEntries(TICKET_ID, MESSAGE_ID, ATTACHMENT_ID);
		// when(attachmentService.findByMessageId(any())).thenReturn(attachments);
		Comment message = PojoGenerator.getMessage(MESSAGE_ID);
		message.setTicket(PojoGenerator.getTicket(SUB_CATEGORY_ID, CATEGORY_ID, TICKET_ID,ROLE_ID,ENTITY_ID));
		message.setAttachments(PojoGenerator.getAttachments(TICKET_ID, MESSAGE_ID, ATTACHMENT_ID));
		CommentEntry expectedMessageEntry = PojoGenerator.getMessageEntry(MESSAGE_ID);
		expectedMessageEntry.setAttachments(attachments);
		expectedMessageEntry.setTicketId(TICKET_ID);
		expectedMessageEntry.setRelatedComments(new ArrayList<>());

		CommentEntry messageEntry = messageService.convertToEntry(message);
		// verify(attachmentService, times(1)).findByMessageId(MESSAGE_ID);
		// verifyNoMoreInteractions(attachmentService);
		assertTrue(expectedMessageEntry.equals(messageEntry));
	}

	@Test
	public void convertToEntityTest() throws ResourceNotFoundException, BaseException {
		when(ticketService.fetchEntityById(any()))
				.thenReturn(PojoGenerator.getTicket(SUB_CATEGORY_ID, CATEGORY_ID, TICKET_ID,ROLE_ID,ENTITY_ID));
		Comment message = PojoGenerator.getMessage(MESSAGE_ID);
		message.setTicket(PojoGenerator.getTicket(SUB_CATEGORY_ID, CATEGORY_ID, TICKET_ID,ROLE_ID,ENTITY_ID));
		CommentEntry messageEntry = PojoGenerator.getMessageEntry(MESSAGE_ID);
		messageEntry.setTicketId(TICKET_ID);
		Comment actualMessage = messageService.convertToEntity(messageEntry);
		verify(ticketService, times(1)).fetchEntityById(TICKET_ID);
		verifyNoMoreInteractions(ticketService);
		assertTrue(message.equals(actualMessage));
	}

	@Test
	public void findByIdTest() throws BaseException {
		CommentEntry expected = PojoGenerator.getMessageEntry(MESSAGE_ID);
		expected.setRelatedComments(new ArrayList<>());
		when(commentDao.findByTicketId(any()))
				.thenReturn(Collections.singletonList(PojoGenerator.getMessage(MESSAGE_ID)));
		List<CommentEntry> entry = messageService.findByTicketId(TICKET_ID);
		verify(commentDao, times(1)).findByTicketId(TICKET_ID);
		verify(commentDao, times(1)).findAllByParentCommentId(MESSAGE_ID);
		verifyNoMoreInteractions(commentDao);
		assertTrue(expected.equals(entry.get(0)));
	}

//	@Test
//	public void createTicketTest() throws BaseException {
//		CommentEntry entry = PojoGenerator.getMessageEntry(MESSAGE_ID);
//		CommentService service = Mockito.spy(new CommentService(commentDao));
//		when(((BaseMySQLService) service).create(entry)).thenReturn(entry);
//		CommentEntry actual = service.create(entry);
//		assertTrue(entry.equals(actual));
//	}

//	@Test
//	public void deleteMessagesOfTicketTest() {
//		Comment message = Mockito.spy(PojoGenerator.getMessage(MESSAGE_ID));
//		when(commentDao.findByTicketId(any())).thenReturn(Collections.singletonList(message));
//		List<Comment> singletonList = Collections.singletonList(message);
//		when(commentDao.saveAll(any())).thenReturn(singletonList);
//		// Mockito.doNothing().when(attachmentService).deleteAttachments(any(), any());
//		messageService.deleteComments(TICKET_ID);
//		assertTrue(message.getActive() == false);
//		verify(commentDao, times(1)).findByTicketId(TICKET_ID);
//		verify(commentDao, times(1)).saveAll(singletonList);
//		verifyNoMoreInteractions(commentDao);
//		// verify(attachmentService, times(1)).deleteAttachments(null,
//		// Collections.singletonList(MESSAGE_ID));
//	}
//
//	@Test
//	public void deleteNoMessagesOfTicketTest() {
//		when(commentDao.findByTicketId(any())).thenReturn(Collections.EMPTY_LIST);
//		messageService.deleteComments(TICKET_ID);
//		verify(commentDao, times(1)).findByTicketId(TICKET_ID);
//		verifyNoMoreInteractions(commentDao);
//	}

	@Test
	public void deleteByIdTest() throws BaseException {
		Comment message = Mockito.spy(PojoGenerator.getMessage(MESSAGE_ID));
		when(commentDao.findById(any())).thenReturn(java.util.Optional.of(message));
		when(commentDao.save(any())).thenReturn(message);
		// Mockito.doNothing().when(attachmentService).deleteAttachments(any(), any());
		messageService.deleteById(MESSAGE_ID);
		assertTrue(message.getActive() == false);
		verify(commentDao, times(1)).findById(MESSAGE_ID);
		verify(commentDao, times(1)).save(message);
		verify(commentDao,times(1)).findAllByParentCommentId(MESSAGE_ID);
		verifyNoMoreInteractions(commentDao);
		// verify(attachmentService, times(1)).deleteAttachments(null,
		// Collections.singletonList(MESSAGE_ID));
	}

	@Test(expected = ResourceNotFoundException.class)
	public void deleteByIdNegativeTest() throws BaseException {
		when(commentDao.findById(any())).thenReturn(java.util.Optional.empty());
		messageService.deleteById(MESSAGE_ID);
		verify(commentDao, times(1)).findById(MESSAGE_ID);
		verifyNoMoreInteractions(commentDao);
	}

	@Test(expected = InvalidDataException.class)
	public void findByIDNegativeTest() throws ResourceNotFoundException, BaseException {
		when(commentDao.findById(any())).thenReturn(java.util.Optional.empty());
		messageService.fetchEntityById(MESSAGE_ID);
		verify(commentDao, times(1)).findById(MESSAGE_ID);
		verifyNoMoreInteractions(commentDao);
	}

	@Test
	public void findByIDTest() throws ResourceNotFoundException, BaseException {
		Comment message = Mockito.spy(PojoGenerator.getMessage(MESSAGE_ID));
		when(commentDao.findById(any())).thenReturn(java.util.Optional.of(message));
		Comment actualMessage = messageService.fetchEntityById(MESSAGE_ID);
		assertTrue(message.equals(actualMessage));
		verify(commentDao, times(1)).findById(MESSAGE_ID);
		verifyNoMoreInteractions(commentDao);
	}

//	@Test
//	public void updateAttachmentsWithOverrideTest() throws ResourceNotFoundException {
//		TicketAttachmentsUpdateEntry ticketAttachmentsUpdateEntry = PojoGenerator.getTicketAttachmentsUpdateEntry(true);
//		List<Attachment> attachments = new ArrayList<>();
//		attachments.add(PojoGenerator.getAttachment(null, 1L, 1L));
//		attachments.add(PojoGenerator.getAttachment(null, 1L, 2L));
//		List<Attachment> expectedAttachments = new ArrayList<>();
//		expectedAttachments.add(PojoGenerator.getAttachment(null, 1L, 1L));
//		expectedAttachments.add(PojoGenerator.getAttachment(null, 1L, 2L));
//		Comment comment = Mockito.spy(PojoGenerator.getMessage(MESSAGE_ID));
//		comment.setAttachments(attachments);
//		Comment expectedComment = PojoGenerator.getMessage(MESSAGE_ID);
//		expectedComment.setAttachments(expectedAttachments);
//		when(commentDao.findById(any())).thenReturn(Optional.of(comment));
//		when(attachmentService.convertToEntity(ticketAttachmentsUpdateEntry.getAttachments())).thenReturn(attachments);
//		when(commentDao.save(any())).thenReturn(expectedComment);
//		CommentEntry response = messageService.updateAttachments(MESSAGE_ID, ticketAttachmentsUpdateEntry);
//		assertTrue(response.getAttachments().size() == 2);
//
//	}

//	@Test
//	public void updateAttachmentsWithoutOverrideTest() throws ResourceNotFoundException {
//		TicketAttachmentsUpdateEntry ticketAttachmentsUpdateEntry = PojoGenerator
//				.getTicketAttachmentsUpdateEntry(false);
//		List<Attachment> attachments = new ArrayList<>();
//		attachments.add(PojoGenerator.getAttachment(null, 1L, 1L));
//		attachments.add(PojoGenerator.getAttachment(null, 1L, 2L));
//		List<Attachment> expectedAttachments = new ArrayList<>();
//		expectedAttachments.add(PojoGenerator.getAttachment(null, 1L, 1L));
//		expectedAttachments.add(PojoGenerator.getAttachment(null, 1L, 2L));
//		expectedAttachments.add(PojoGenerator.getAttachment(null, 1L, 1L));
//		expectedAttachments.add(PojoGenerator.getAttachment(null, 1L, 2L));
//		Comment comment = PojoGenerator.getMessage(MESSAGE_ID);
//		comment.setAttachments(attachments);
//		Comment expectedComment = PojoGenerator.getMessage(MESSAGE_ID);
//		expectedComment.setAttachments(expectedAttachments);
//		when(commentDao.findById(any())).thenReturn(Optional.of(comment));
//		when(attachmentService.convertToEntity(ticketAttachmentsUpdateEntry.getAttachments())).thenReturn(attachments);
//		when(commentDao.save(any())).thenReturn(expectedComment);
//		CommentEntry response = messageService.updateAttachments(MESSAGE_ID, ticketAttachmentsUpdateEntry);
//		assertTrue(response.getAttachments().size() == 4);
//	}
//
//	@Test(expected = ResourceNotFoundException.class)
//	public void updateAttachmentsNegativeTest() throws ResourceNotFoundException {
//		TicketAttachmentsUpdateEntry ticketAttachmentsUpdateEntry = PojoGenerator
//				.getTicketAttachmentsUpdateEntry(false);
//		when(commentDao.findById(any())).thenReturn(Optional.empty());
//		messageService.updateAttachments(MESSAGE_ID, ticketAttachmentsUpdateEntry);
//		verify(commentDao, times(1)).findById(MESSAGE_ID);
//		verifyNoMoreInteractions(commentDao);
//	}

}
