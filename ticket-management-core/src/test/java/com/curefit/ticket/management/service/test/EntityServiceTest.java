package com.curefit.ticket.management.service.test;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;
import java.util.Optional;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.ResourceNotFoundException;
import com.curefit.odin.admin.models.Tenant;
import com.curefit.odin.admin.repositories.TenantDAO;
import com.curefit.odin.admin.service.CategoryService;
import com.curefit.odin.admin.service.TenantService;
import com.curefit.odin.exceptions.InvalidDataException;
import com.curefit.ticket.management.test.helper.PojoGenerator;

@RunWith(MockitoJUnitRunner.class)
public class EntityServiceTest {
  @Mock
  private TenantDAO entityDao;
  @Mock
  private CategoryService categoryService;
  @InjectMocks
  private TenantService entityService;

  @Before
  public void initMocks() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void findByIdPositiveTest() throws ResourceNotFoundException, BaseException {
    Long id = 1L;
    when(entityDao.findById(any(Long.class))).thenReturn(Optional.of(PojoGenerator.getTenant(id)));
    Tenant entity = entityService.fetchEntityById(id);
    assertTrue(id.equals(entity.getId()));
    verify(entityDao, times(1)).findById(id);
    verifyNoMoreInteractions(entityDao);
  }

  @Test(expected = InvalidDataException.class)
  public void findByIdInvalidIdTest() throws ResourceNotFoundException, BaseException {
    Long id = 1L;
    when(entityDao.findById(any(Long.class))).thenReturn(Optional.empty());
    entityService.fetchEntityById(id);
    verify(entityDao, times(1)).findById(id);
    verifyNoMoreInteractions(entityDao);
  }



}
