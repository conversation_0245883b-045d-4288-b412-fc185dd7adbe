package com.curefit.ticket.management.service.test;

import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.ResourceNotFoundException;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.service.UserService;
import com.curefit.odin.user.models.TicketWatcher;
import com.curefit.odin.user.pojo.TicketWatcherEntry;
import com.curefit.odin.user.repositories.TicketWatcherDAO;
import com.curefit.odin.user.service.TicketService;
import com.curefit.odin.user.service.TicketWatcherService;
import com.curefit.ticket.management.test.helper.PojoGenerator;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TicketWatcherTest {

    private static final long TICKET_ID = 1L;
    private static final Long WATCHER_ID = 1l;
    private static final String WATCHER_MAIL_ID = "<EMAIL>";
    private static final long TICKETWATCHER_ID = 1L;
    private static final long SUB_CATEGORY_ID = 3L;
    private static final long ROLE_ID = 4L;
    private static final long ENTITY_ID = 4L;


    private static final long CATEGORY_ID = 2L;
    private List<Long> ticketIdList;
    private List<TicketWatcher> ticketWatcherList;
    private List<Long> watcherIdList;
    private List<String> watcherMailIdList;

    @InjectMocks
    private TicketWatcherService ticketWatcherService;
    @Mock
    private UserService watcherService;
    @Mock
    private TicketService ticketService;
    @Mock
    private TicketWatcherDAO ticketWatcherDao;


    @Before
    public void initMocks() {
        MockitoAnnotations.initMocks(this);
        List<Long> id = new ArrayList<>();
        id.add(1l);
        this.ticketIdList = id;
        this.watcherIdList = id;

        List<TicketWatcher> ticketWatcherList = new ArrayList<>();
        ticketWatcherList.add(PojoGenerator.getTicketWatcher(TICKETWATCHER_ID, SUB_CATEGORY_ID, CATEGORY_ID, TICKET_ID, WATCHER_MAIL_ID,ROLE_ID,ENTITY_ID));
        this.ticketWatcherList = ticketWatcherList;

        List<String> mailIds = new ArrayList<>();
        mailIds.add(WATCHER_MAIL_ID);
        this.watcherMailIdList= (mailIds);
    }

    @Test
    public void convertToEntryTest() {
        TicketWatcherEntry entry = ticketWatcherService.convertToEntry(PojoGenerator.getTicketWatcher(TICKETWATCHER_ID, SUB_CATEGORY_ID, CATEGORY_ID, TICKET_ID, WATCHER_MAIL_ID,ROLE_ID,ENTITY_ID));
        assertTrue(PojoGenerator.getTicketWatcherEntry(TICKETWATCHER_ID, TICKET_ID, WATCHER_MAIL_ID).equals(entry));
    }

    @Test
    public void convertToEntityTest() throws ResourceNotFoundException, BaseException, InvalidSeachQueryException {
        when(ticketService.fetchEntityById(TICKET_ID)).thenReturn(PojoGenerator.getTicket(SUB_CATEGORY_ID, CATEGORY_ID, TICKET_ID,ROLE_ID,ENTITY_ID));
        when(watcherService.findUserByMailId(WATCHER_MAIL_ID)).thenReturn(PojoGenerator.getWatcher(WATCHER_MAIL_ID));
        TicketWatcher entity = ticketWatcherService.convertToEntity(PojoGenerator.getTicketWatcherEntry(TICKETWATCHER_ID, TICKET_ID, WATCHER_MAIL_ID));
        assertTrue(PojoGenerator.getTicketWatcher(TICKETWATCHER_ID, SUB_CATEGORY_ID, CATEGORY_ID, TICKET_ID, WATCHER_MAIL_ID,ROLE_ID,ENTITY_ID).equals(entity));
    }

//    @Test
//    public void findAllTicketWatchersTest() {
//        when(ticketWatcherDao.findByTicketIdAndUserId(TICKET_ID, WATCHER_ID)).thenReturn(PojoGenerator.getTicketWatcher(TICKETWATCHER_ID, SUB_CATEGORY_ID, CATEGORY_ID, TICKET_ID, WATCHER_MAIL_ID,ROLE_ID,ENTITY_ID));
//        TicketWatcher ticketWatcher = ticketWatcherService.findTicketWatchersById(TICKET_ID, WATCHER_ID);
//        assertTrue(PojoGenerator.getTicketWatcher(TICKETWATCHER_ID, SUB_CATEGORY_ID, CATEGORY_ID, TICKET_ID, WATCHER_MAIL_ID,ROLE_ID,ENTITY_ID).equals(ticketWatcher));
//    }

//    @Test
//    public void deleteByIdTest() throws BaseException {
//        when(ticketWatcherService.findOneById(TICKETWATCHER_ID)).thenReturn(PojoGenerator.getTicketWatcherEntry(TICKETWATCHER_ID, TICKET_ID, WATCHER_ID));
//        TicketWatcherEntry ticketWatcherEntry= ticketWatcherService.deleteById(WATCHER_ID);
//        assertTrue(PojoGenerator.getTicketWatcherEntry(TICKETWATCHER_ID, TICKET_ID, WATCHER_ID).equals(ticketWatcherEntry));
//    }

//    @Test
//    public void updateAllTicketWatchersListFromWatcherId() {
//        when(ticketWatcherService.findTicketWatchersById(TICKET_ID, WATCHER_ID)).thenReturn(PojoGenerator.getTicketWatcher(TICKETWATCHER_ID, SUB_CATEGORY_ID, CATEGORY_ID, TICKET_ID, WATCHER_MAIL_ID,ROLE_ID,ENTITY_ID));
//        List<TicketWatcher> ticketWatcherList1 = ticketWatcherService.updateAllTicketWatchersListFromWatcherId(ticketIdList, WATCHER_ID);
//        assertTrue(ticketWatcherList.equals(ticketWatcherList1));
//    }
//
//    @Test
//    public void create() throws BaseException, InvalidSeachQueryException {
//        when(ticketService.fetchEntityById(TICKET_ID)).thenReturn(PojoGenerator.getTicket(SUB_CATEGORY_ID,CATEGORY_ID,TICKET_ID,ROLE_ID,ENTITY_ID));
//        when(watcherService.findUserByMailId(Mockito.any())).thenReturn(PojoGenerator.getWatcher(WATCHER_MAIL_ID));
//        when(ticketWatcherDao.findAllByTicketIdAndActiveTrue(TICKET_ID)).thenReturn(ticketWatcherList);
//        TicketWatcherEntry ticketWatcherEntry= ticketWatcherService.create(PojoGenerator.getTicketWatcherEntry(TICKETWATCHER_ID, TICKET_ID, WATCHER_MAIL_ID));
//        assertTrue(PojoGenerator.getTicketWatcherEntry(TICKETWATCHER_ID, TICKET_ID, WATCHER_MAIL_ID).equals(ticketWatcherEntry));
//    }

//    @Test
//    public void deactivateAllTicketWatchersListFromTicketIdTest(){
//        when(ticketWatcherService.findTicketWatchersById(TICKET_ID, WATCHER_ID)).thenReturn(PojoGenerator.getTicketWatcher(TICKETWATCHER_ID, SUB_CATEGORY_ID, CATEGORY_ID, TICKET_ID, WATCHER_MAIL_ID,ROLE_ID,ENTITY_ID));
//        when(watcherService.fetchEntityById(WATCHER_ID)).thenReturn(PojoGenerator.getWatcher(WATCHER_MAIL_ID));
//        TicketWatcherEntry ticketWatcherEntry= ticketWatcherService.deactivateAllTicketWatchersListFromTicketId(TICKET_ID,watcherIdList);
//        assertTrue(PojoGenerator.getTicketWatcherEntry(TICKETWATCHER_ID, TICKET_ID, WATCHER_MAIL_ID).equals(ticketWatcherEntry));
//    }


//    @Test
//    public void findAllWatchersTest() {
//        when(ticketWatcherDao.findAllByTicketId(TICKET_ID)).thenReturn(ticketWatcherList);
//        List<String> watcherIds= ticketWatcherService.findAllWatchers(TICKET_ID);
//        assertTrue(watcherIdList.equals(watcherIds));
//    }
//
//    @Test
//    public void findAllWatchersTest1() {
//        when(ticketWatcherDao.findAllByTicketId(TICKET_ID)).thenReturn(Collections.emptyList());
//        List<String> watcherIds= ticketWatcherService.findAllWatchers(TICKET_ID);
//        assertTrue(watcherIds.size() == 0);
//    }

//    @Test
//    public void saveTest() throws SQLException, BaseException {
//        when(ticketWatcherDao.save(PojoGenerator.getTicketWatcher(TICKETWATCHER_ID, SUB_CATEGORY_ID, CATEGORY_ID, TICKET_ID, WATCHER_ID))).thenReturn(PojoGenerator.getTicketWatcher(TICKETWATCHER_ID, SUB_CATEGORY_ID, CATEGORY_ID, TICKET_ID, WATCHER_ID));
//        TicketWatcher ticketWatcher= ticketWatcherService.save(PojoGenerator.getTicketWatcher(TICKETWATCHER_ID, SUB_CATEGORY_ID, CATEGORY_ID, TICKET_ID, WATCHER_ID));
//        assertTrue(PojoGenerator.getTicketWatcher(TICKETWATCHER_ID, SUB_CATEGORY_ID, CATEGORY_ID, TICKET_ID, WATCHER_ID).equals(ticketWatcher));
//    }

}
