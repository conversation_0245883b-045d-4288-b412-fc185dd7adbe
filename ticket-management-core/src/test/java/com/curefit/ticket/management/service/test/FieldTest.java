package com.curefit.ticket.management.service.test;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.ResourceNotFoundException;
import com.curefit.odin.admin.models.Category;
import com.curefit.odin.admin.models.CustomField;
import com.curefit.odin.admin.pojo.CustomFieldEntry;
import com.curefit.odin.admin.pojo.CustomFieldFilterRequestEntry;
import com.curefit.odin.admin.repositories.CustomFieldDAO;
import com.curefit.odin.admin.service.CategoryService;
import com.curefit.odin.admin.service.CustomFieldService;
import com.curefit.odin.admin.service.SubCategoryService;
import com.curefit.odin.admin.service.TenantService;
import com.curefit.odin.exceptions.InvalidDataException;
import com.curefit.odin.user.service.FieldDataService;
import com.curefit.ticket.management.test.helper.PojoGenerator;

@RunWith(MockitoJUnitRunner.class)
public class FieldTest {
  private static final long FIELD_ID = 4L;

  private static final long SUB_CATEGORY_ID = 3L;

  private static final long CATEGORY_ID = 2L;

  private static final long TENANT_ID = 1L;

  @Mock
  private CategoryService ticketCategoryService;
  @Mock
  private SubCategoryService ticketSubCategoryService;
  @Mock
  private CustomFieldDAO fieldDao;
  @Mock
  private FieldDataService fieldDataService;
  @Mock
  private TenantService tenantService;
  @InjectMocks
  private CustomFieldService fieldService;

  @Before
  public void initMocks() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void convertToEntryTest() {
    CustomField field = PojoGenerator.getField(SUB_CATEGORY_ID, CATEGORY_ID, FIELD_ID);
    field.setTenant(PojoGenerator.getTenant(TENANT_ID));
    CustomFieldEntry entry = fieldService.convertToEntry(field);
    CustomFieldEntry fieldEntry = PojoGenerator.getFieldEntry(SUB_CATEGORY_ID, CATEGORY_ID, FIELD_ID);
    fieldEntry.setTenantId(TENANT_ID);
    assertTrue(fieldEntry.equals(entry));
  }

  @Test
  public void convertToEntryTest2() {
    CustomFieldEntry entry = fieldService.convertToEntry(PojoGenerator.getField(null, null, FIELD_ID));
    System.out.println(entry);
    System.out.println(PojoGenerator.getFieldEntry(null, null, FIELD_ID));
    assertTrue(PojoGenerator.getFieldEntry(null, null, FIELD_ID).equals(entry));
  }

  @Test
  public void convertToEntityTest() throws ResourceNotFoundException, BaseException {
    when(ticketCategoryService.fetchEntityById(any(Long.class)))
        .thenReturn(PojoGenerator.getTicketCategory(CATEGORY_ID));
    when(ticketSubCategoryService.fetchEntityById(any(Long.class)))
        .thenReturn(PojoGenerator.getTicketSubCategory(SUB_CATEGORY_ID, CATEGORY_ID));
    when(tenantService.fetchEntityById(any(Long.class)))
        .thenReturn(PojoGenerator.getTenant(TENANT_ID));
    CustomFieldEntry fieldEntry = PojoGenerator.getFieldEntry(SUB_CATEGORY_ID, CATEGORY_ID, FIELD_ID);
    fieldEntry.setTenantId(TENANT_ID);
    CustomField entity = fieldService.convertToEntity(fieldEntry);

    CustomField field = PojoGenerator.getField(SUB_CATEGORY_ID, CATEGORY_ID, FIELD_ID);
    field.setTenant(PojoGenerator.getTenant(TENANT_ID));
    assertTrue(field.equals(entity));
  }

  @Test
  public void convertToEntityWithoutCategoryAndSubCategoryTest() {
    CustomField entity = fieldService.convertToEntity(PojoGenerator.getFieldEntry(null, null, FIELD_ID));
    assertTrue(PojoGenerator.getField(null, null, FIELD_ID).equals(entity));
  }


  @Test
  public void findByIdTest() throws ResourceNotFoundException, BaseException {
    when(fieldDao.findById(any(Long.class)))
        .thenReturn(Optional.of(PojoGenerator.getField(SUB_CATEGORY_ID, CATEGORY_ID, FIELD_ID)));
    CustomField entry = fieldService.fetchEntityById(FIELD_ID);
    assertTrue(PojoGenerator.getField(SUB_CATEGORY_ID, CATEGORY_ID, FIELD_ID).equals(entry));
  }

  @Test(expected = InvalidDataException.class)
  public void findByIdNegativeTest() throws ResourceNotFoundException, BaseException {
    when(fieldDao.findById(any(Long.class))).thenReturn(Optional.empty());
    CustomField entry = fieldService.fetchEntityById(FIELD_ID);
  }


  @Test
  public void findByFieldsBySubCategoryTest() throws ResourceNotFoundException, BaseException {
    when(ticketSubCategoryService.fetchEntityById(any()))
        .thenReturn(PojoGenerator.getTicketSubCategory(SUB_CATEGORY_ID, CATEGORY_ID));
    Category ticketCategory = PojoGenerator.getTicketCategory(CATEGORY_ID);
    ticketCategory.setTenant(PojoGenerator.getTenant(TENANT_ID));
    when(ticketCategoryService.fetchEntityById(any())).thenReturn(ticketCategory);
    CustomFieldEntry fieldEntry = PojoGenerator.getFieldEntry(SUB_CATEGORY_ID, CATEGORY_ID, FIELD_ID);
    CustomField field = PojoGenerator.getField(SUB_CATEGORY_ID, CATEGORY_ID, FIELD_ID);
    List<CustomField> singletonFieldList = Collections.singletonList(field);
    when(fieldDao.findByCategoryIdOrSubCategoryIdOrTenantIdOrderByOrder(any(), any(), any()))
        .thenReturn(singletonFieldList);
    assertTrue(fieldEntry.equals(
        fieldService.filter(new CustomFieldFilterRequestEntry(null, null, SUB_CATEGORY_ID)).get(0)));
    verify(ticketSubCategoryService, times(1)).fetchEntityById(SUB_CATEGORY_ID);
    verify(ticketCategoryService, times(1)).fetchEntityById(CATEGORY_ID);
    verify(fieldDao, times(1)).findByCategoryIdOrSubCategoryIdOrTenantIdOrderByOrder(CATEGORY_ID,
        SUB_CATEGORY_ID, TENANT_ID);
  }

  @Test
  public void findByFieldsByCategoryTest() throws ResourceNotFoundException, BaseException {
    Category ticketCategory = PojoGenerator.getTicketCategory(CATEGORY_ID);
    ticketCategory.setTenant(PojoGenerator.getTenant(TENANT_ID));
    when(ticketCategoryService.fetchEntityById(any())).thenReturn(ticketCategory);
    CustomFieldEntry fieldEntry = PojoGenerator.getFieldEntry(SUB_CATEGORY_ID, CATEGORY_ID, FIELD_ID);
    CustomField field = PojoGenerator.getField(SUB_CATEGORY_ID, CATEGORY_ID, FIELD_ID);
    List<CustomField> singletonFieldList = Collections.singletonList(field);
    when(fieldDao.findByCategoryIdOrTenantIdOrderByOrder(any(), any()))
        .thenReturn(singletonFieldList);
    assertTrue(fieldEntry
        .equals(fieldService.filter(new CustomFieldFilterRequestEntry(null, CATEGORY_ID, null)).get(0)));
    verifyNoInteractions(ticketSubCategoryService);
    verify(ticketCategoryService, times(1)).fetchEntityById(CATEGORY_ID);
    verify(fieldDao, times(1)).findByCategoryIdOrTenantIdOrderByOrder(CATEGORY_ID, TENANT_ID);
  }

  @Test
  public void findByFieldsByTenantTest() {
    CustomFieldEntry fieldEntry = PojoGenerator.getFieldEntry(SUB_CATEGORY_ID, CATEGORY_ID, FIELD_ID);
    CustomField field = PojoGenerator.getField(SUB_CATEGORY_ID, CATEGORY_ID, FIELD_ID);
    List<CustomField> singletonFieldList = Collections.singletonList(field);
    when(fieldDao.findByTenantIdOrderByOrder(any())).thenReturn(singletonFieldList);
    assertTrue(fieldEntry
        .equals(fieldService.filter(new CustomFieldFilterRequestEntry(TENANT_ID, null, null)).get(0)));
    verifyNoInteractions(ticketSubCategoryService);
    verifyNoInteractions(ticketCategoryService);
    verify(fieldDao, times(1)).findByTenantIdOrderByOrder(TENANT_ID);
  }

}
