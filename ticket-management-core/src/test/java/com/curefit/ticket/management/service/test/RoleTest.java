package com.curefit.ticket.management.service.test;

import com.curefit.odin.admin.service.DefaultAssigneeService;
import com.curefit.odin.admin.service.TenantService;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class RoleTest {
	private static final long ROLE_ID = 7L;

	private static final long ENTITY_ID = 1L;

	private static final long SUB_CATEGORY_ID = 3L;

	private static final long DEFAULT_ASSIGNEE_ID = 8L;

	@Mock
	private TenantService entityService;
	@Mock
	private DefaultAssigneeService defaultAssigneeService;

	@Before
	public void initMocks() {
		MockitoAnnotations.initMocks(this);
	}



}
