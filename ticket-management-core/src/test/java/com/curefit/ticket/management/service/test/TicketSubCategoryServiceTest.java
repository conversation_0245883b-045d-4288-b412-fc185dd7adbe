package com.curefit.ticket.management.service.test;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.ResourceNotFoundException;
import com.curefit.odin.admin.models.Category;
import com.curefit.odin.admin.models.SubCategory;
import com.curefit.odin.admin.pojo.SubCategoryEntry;
import com.curefit.odin.admin.repositories.SubCategoryDAO;
import com.curefit.odin.admin.service.CategoryService;
import com.curefit.odin.admin.service.DefaultAssigneeService;
import com.curefit.odin.admin.service.CustomFieldService;
import com.curefit.odin.admin.service.SLAService;
import com.curefit.odin.admin.service.SubCategoryService;
import com.curefit.odin.admin.service.UserService;
import com.curefit.odin.enums.Priority;
import com.curefit.odin.exceptions.InvalidDataException;
import com.curefit.odin.user.service.TicketService;
import com.curefit.ticket.management.test.helper.PojoGenerator;

@RunWith(MockitoJUnitRunner.class)
public class TicketSubCategoryServiceTest {
	private static final long FIELD_ID = 4L;

	private static final long SUB_CATEGORY_ID = 3L;

	private static final long CATEGORY_ID = 2L;

	private static final long ROLE_ID = 5L;

	private static final long DEFAULT_ASSIGNEE_ID = 6L;

	@Mock
	private CategoryService ticketCategoryService;
	@Mock
	private SubCategoryDAO ticketSubCategoryDao;
	@Mock
	private CustomFieldService fieldService;
	@Mock
	private DefaultAssigneeService defaultAssigneeService;
	@Mock
	private TicketService ticketService;
	@Mock
	private UserService userService;
	@Mock
	private SLAService slaService;
//	@Mock
//	private BaseMySQLService mysqlService;
//	@Mock
//	private BaseMySQLRepository<TicketSubCategory> baseMySQLRepository;
	@InjectMocks
	private SubCategoryService ticketSubCategoryService;

	@Before
	public void initMocks() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void convertToEntryTest() {
		SubCategory subCategory = PojoGenerator.getTicketSubCategory(SUB_CATEGORY_ID, CATEGORY_ID);
		SubCategoryEntry entry = ticketSubCategoryService.convertToEntry(subCategory);
		SubCategoryEntry expected = PojoGenerator.getTicketSubCategoryEntry(SUB_CATEGORY_ID, CATEGORY_ID);
		assertTrue(expected.equals(entry));
	}

	@Test
	public void convertToEntityTest() throws ResourceNotFoundException, BaseException {
		SubCategoryEntry entry = PojoGenerator.getTicketSubCategoryEntry(SUB_CATEGORY_ID, CATEGORY_ID);
		SubCategory expected = PojoGenerator.getTicketSubCategory(SUB_CATEGORY_ID, CATEGORY_ID);
		when(ticketCategoryService.fetchEntityById(CATEGORY_ID)).thenReturn(PojoGenerator.getTicketCategory(CATEGORY_ID));
		SubCategory actual = ticketSubCategoryService.convertToEntity(entry);
		assertTrue(expected.equals(actual));
		verify(ticketCategoryService, times(1)).fetchEntityById(CATEGORY_ID);
	}

	@Test
  public void convertToEntityWithSlaTest() throws ResourceNotFoundException, BaseException {
		SubCategoryEntry entry = PojoGenerator.getTicketSubCategoryEntry(SUB_CATEGORY_ID, CATEGORY_ID);
		SubCategory expected = PojoGenerator.getTicketSubCategory(SUB_CATEGORY_ID, CATEGORY_ID);
		expected.setSlaMappings(
				Collections.singletonList(PojoGenerator.getSlaMapping(null, SUB_CATEGORY_ID, Priority.P0)));
		Category ticketCategory = PojoGenerator.getTicketCategory(CATEGORY_ID);
		when(slaService.convertToSlaMapping(any(), any(), any()))
				.thenReturn(Collections.singletonList(PojoGenerator.getSlaMapping(null, SUB_CATEGORY_ID, Priority.P0)));
		when(ticketCategoryService.fetchEntityById(CATEGORY_ID)).thenReturn(ticketCategory);
		SubCategory actual = ticketSubCategoryService.convertToEntity(entry);
		assertTrue(expected.equals(actual));
		verify(ticketCategoryService, times(1)).fetchEntityById(CATEGORY_ID);
	}

	@Test
	public void findByIdTest() throws ResourceNotFoundException, BaseException {
		when(ticketSubCategoryDao.findById(any(Long.class)))
				.thenReturn(Optional.of(PojoGenerator.getTicketSubCategory(SUB_CATEGORY_ID, CATEGORY_ID)));
		SubCategory ticketCategory = ticketSubCategoryService.fetchEntityById(CATEGORY_ID);
		assertTrue(PojoGenerator.getTicketSubCategory(SUB_CATEGORY_ID, CATEGORY_ID).equals(ticketCategory));
		verify(ticketSubCategoryDao, times(1)).findById(CATEGORY_ID);
		verifyNoMoreInteractions(ticketSubCategoryDao);
	}

	@Test(expected = InvalidDataException.class)
  public void findByIdInvalidIdTest() throws ResourceNotFoundException, BaseException {
		when(ticketSubCategoryDao.findById(any(Long.class))).thenReturn(Optional.empty());
		ticketSubCategoryService.fetchEntityById(CATEGORY_ID);
		verify(ticketSubCategoryDao, times(1)).findById(CATEGORY_ID);
		verifyNoMoreInteractions(ticketSubCategoryDao);
	}


	@Test
	public void findByCategoryIdTest() {
		when(ticketSubCategoryDao.findByCategoryId(any(Long.class))).thenReturn(
				Collections.singletonList(PojoGenerator.getTicketSubCategory(SUB_CATEGORY_ID, CATEGORY_ID)));
		List<SubCategoryEntry> ticketCategories = ticketSubCategoryService.findByCategoryId(CATEGORY_ID);
		SubCategoryEntry ticketSubCategoryEntry = PojoGenerator.getTicketSubCategoryEntry(SUB_CATEGORY_ID, CATEGORY_ID);
		assertTrue(ticketSubCategoryEntry.equals(ticketCategories.get(0)));
		verify(ticketSubCategoryDao, times(1)).findByCategoryId(CATEGORY_ID);
		verifyNoMoreInteractions(ticketSubCategoryDao);
	}



	@Test
	public void findActiveByCategoryIdTest() {
		when(ticketSubCategoryDao.findByCategoryIdAndActive(any(), any())).thenReturn(
				Collections.singletonList(PojoGenerator.getTicketSubCategory(SUB_CATEGORY_ID, CATEGORY_ID)));
		List<SubCategoryEntry> entry = ticketSubCategoryService.findActiveByCategoryId(CATEGORY_ID);
		SubCategoryEntry ticketSubCategoryEntry = PojoGenerator.getTicketSubCategoryEntry(SUB_CATEGORY_ID, CATEGORY_ID);
		assertTrue(ticketSubCategoryEntry.equals(entry.get(0)));
	}
}
