package com.curefit.ticket.management.service.test;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.ResourceNotFoundException;
import com.curefit.odin.admin.models.Category;
import com.curefit.odin.admin.pojo.CategoryEntry;
import com.curefit.odin.admin.repositories.CategoryDAO;
import com.curefit.odin.admin.service.CategoryService;
import com.curefit.odin.admin.service.DefaultAssigneeService;
import com.curefit.odin.admin.service.CustomFieldService;
import com.curefit.odin.admin.service.SLAService;
import com.curefit.odin.admin.service.SubCategoryService;
import com.curefit.odin.admin.service.TenantService;
import com.curefit.odin.exceptions.InvalidDataException;
import com.curefit.ticket.management.test.helper.PojoGenerator;

@RunWith(MockitoJUnitRunner.class)
public class TicketCategoryTest {
  private static final long FIELD_ID = 4L;

  private static final long SUB_CATEGORY_ID = 3L;

  private static final long CATEGORY_ID = 2L;

  private static final long ENTITY_ID = 1L;

  private static final long ROLE_ID = 5L;

  @Mock
  private TenantService entityService;

  @Mock
  private CategoryDAO ticketCategoryDao;

  @Mock
  private SubCategoryService ticketSubCategoryService;

  @Mock
  private CustomFieldService fieldService;

  @Mock
  private SLAService slaService;
  @Mock
  private DefaultAssigneeService defaultAssigneeService;

  @InjectMocks
  private CategoryService ticketCategoryService;

  @Before
  public void initMocks() {
    MockitoAnnotations.initMocks(this);
  }

//  @Test
//  public void convertToEntryTest() throws ResourceNotFoundException, BaseException {
//    Category ticketCategory = PojoGenerator.getTicketCategory(CATEGORY_ID);
//    ticketCategory.setTenant(PojoGenerator.getTenant(ENTITY_ID));
//    when(defaultAssigneeService.findByCategoryId(CATEGORY_ID))
//        .thenReturn(PojoGenerator.getDefaultAssigneeEntry(SUB_CATEGORY_ID, ROLE_ID, 1l));
//    when(roleService.fetchEntityById(ROLE_ID)).thenReturn(PojoGenerator.getRole(ROLE_ID, ENTITY_ID));
//    CategoryEntry entry = ticketCategoryService.convertToEntry(ticketCategory);
//    CategoryEntry ticketCategoryEntryExpected =
//        PojoGenerator.getTicketCategoryEntry(ENTITY_ID, CATEGORY_ID);
//    ticketCategoryEntryExpected.setTenantId(ENTITY_ID);
//    ticketCategoryEntryExpected.setDefaultAssigneeRoleId(ROLE_ID);
//    ticketCategoryEntryExpected.setPrioritySlaInHoursMapping(new HashMap<>());
//    ticketCategoryEntryExpected.setRoleName(PojoGenerator.getRole(ROLE_ID, ENTITY_ID).getName());
//    assertTrue(ticketCategoryEntryExpected.equals(entry));
//  }

  @Test
  public void convertToEntryWithoutEntityTest() {
    Category ticketCategory = PojoGenerator.getTicketCategory(CATEGORY_ID);
    CategoryEntry entry = ticketCategoryService.convertToEntry(ticketCategory);
    CategoryEntry ticketCategoryEntryExpected =
        PojoGenerator.getTicketCategoryEntry(null, CATEGORY_ID);
    assertTrue(ticketCategoryEntryExpected.equals(entry));
  }

  @Test
  public void convertToEntityTest() throws ResourceNotFoundException, BaseException {
    CategoryEntry ticketCategory = PojoGenerator.getTicketCategoryEntry(ENTITY_ID, CATEGORY_ID);
    when(entityService.fetchEntityById(any(Long.class)))
        .thenReturn(PojoGenerator.getTenant(ENTITY_ID));
    Category entity = ticketCategoryService.convertToEntity(ticketCategory);
    Category ticketCategoryExpected = PojoGenerator.getTicketCategory(CATEGORY_ID);
    ticketCategoryExpected.setTenant(PojoGenerator.getTenant(ENTITY_ID));
    assertTrue(ticketCategoryExpected.equals(entity));
    verify(entityService, times(1)).fetchEntityById(ENTITY_ID);
    verifyNoMoreInteractions(entityService);
  }

  @Test
  public void convertToTicketCategoryWithoutEntityTest() {
    CategoryEntry ticketCategory = PojoGenerator.getTicketCategoryEntry(null, CATEGORY_ID);
    Category entity = ticketCategoryService.convertToEntity(ticketCategory);
    assertTrue(PojoGenerator.getTicketCategory(CATEGORY_ID).equals(entity));
    verifyNoInteractions(entityService);
    verifyNoMoreInteractions(entityService);
  }

  @Test
  public void findByIdPositiveTest() throws ResourceNotFoundException, BaseException {
    Category ticketCategoryExpected = PojoGenerator.getTicketCategory(CATEGORY_ID);
    when(ticketCategoryDao.findById(any(Long.class)))
        .thenReturn(Optional.of(ticketCategoryExpected));
    Category ticketCategory = ticketCategoryService.fetchEntityById(CATEGORY_ID);
    assertTrue(ticketCategoryExpected.equals(ticketCategory));
    verify(ticketCategoryDao, times(1)).findById(CATEGORY_ID);
    verifyNoMoreInteractions(ticketCategoryDao);
  }

  @Test(expected = InvalidDataException.class)
  public void findByIdInvalidIdTest() throws ResourceNotFoundException, BaseException {
    Long id = 1L;
    when(ticketCategoryDao.findById(any(Long.class))).thenReturn(Optional.empty());
    ticketCategoryService.fetchEntityById(id);
    verify(ticketCategoryDao, times(1)).findById(id);
    verifyNoMoreInteractions(ticketCategoryDao);
  }

  @Test
  public void findByEntityIdTest() {
    Category ticketCategoryActual = PojoGenerator.getTicketCategory(CATEGORY_ID);
    List<Category> singletonList = Collections.singletonList(ticketCategoryActual);
    when(ticketCategoryDao.findByTenantId(any(Long.class))).thenReturn(singletonList);
    List<CategoryEntry> ticketCategories = ticketCategoryService.findByTenantId(ENTITY_ID);
    assertTrue(validateEntryAndEntity(ticketCategoryActual, ticketCategories.get(0)));
    verify(ticketCategoryDao, times(1)).findByTenantId(ENTITY_ID);
    verifyNoMoreInteractions(ticketCategoryDao);
  }


  @Test

  private boolean validateEntryAndEntity(Category entity, CategoryEntry entry) {
    if ((entity.getName() == entry.getName() || entry.getName().equals(entity.getName()))
        && entity.getId() == entry.getId()
        && ((entity.getTenant() == null && entry.getTenantId() == null)
            || entity.getTenant().getId() == entry.getTenantId())) {
      return true;
    }
    return false;
  }

  @Test
  public void fetchActiveByTenantIdTest() {
    when(ticketCategoryDao.findByTenantIdAndActive(any(), any()))
        .thenReturn(Collections.singletonList(PojoGenerator.getTicketCategory(CATEGORY_ID)));
    List<CategoryEntry> categoryEntry = ticketCategoryService.fetchActiveByTenantId(ENTITY_ID);
    CategoryEntry ticketCategoryEntry = PojoGenerator.getTicketCategoryEntry(null, CATEGORY_ID);
    assertTrue(ticketCategoryEntry.equals(categoryEntry.get(0)));
  }
}
