package com.curefit.ticket.management.service.test;

import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.ResourceNotFoundException;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.odin.admin.models.User;
import com.curefit.odin.admin.pojo.UserEntry;
import com.curefit.odin.admin.repositories.UserDAO;
import com.curefit.odin.admin.service.UserService;
import com.curefit.odin.exceptions.InvalidDataException;
import com.curefit.odin.user.service.TicketWatcherService;
import com.curefit.ticket.management.test.helper.PojoGenerator;

@RunWith(MockitoJUnitRunner.class)
public class UserTest {

  private static final long TICKET_ID = 1L;
  private static final long WATCHER_ID = 1l;
  private static final String WATCHER_MAIL_ID = "<EMAIL>";
  private static final long TICKETWATCHER_ID = 1L;
  private static final long SUB_CATEGORY_ID = 3L;

  private static final long CATEGORY_ID = 2L;

  private List<Long> ticketIdList;
  private List<Long> watcherIdList;
  private List<UserEntry> watcherList;
  private List<UserEntry> watchersEntryList;

  @Mock
  private UserDAO userDao;
  @Mock
  private TicketWatcherService ticketWatcherService;
  @InjectMocks
  private UserService userService;

  @Before
  public void initMocks() {
    MockitoAnnotations.initMocks(this);
    List<Long> id = new ArrayList<>();
    id.add(1l);
    this.ticketIdList = id;
    this.watcherIdList = id;

    List<UserEntry> watchersList = new ArrayList<>();
    watchersList.add(PojoGenerator.getWatcher(WATCHER_MAIL_ID));
    this.watcherList = watchersList;

    List<UserEntry> watchersEntryList = new ArrayList<>();
    watchersEntryList.add(PojoGenerator.getWatcherEntry(WATCHER_MAIL_ID));
    this.watchersEntryList = watchersEntryList;
  }


  @Test
  public void convertToEntityTest() {
    User entity = userService
            .convertToEntity(PojoGenerator.getWatcherEntry(WATCHER_MAIL_ID));
    assertTrue(PojoGenerator.getWatcher(WATCHER_MAIL_ID).equals(entity));
  }


  @Test(expected = InvalidDataException.class)
  public void findAllWatchersByMailIdTest1()
          throws ResourceNotFoundException, InvalidSeachQueryException, BaseException {
    when(userService.findUserByMailId("<EMAIL>")).thenReturn(null);
    UserEntry watcher = userService.findUserByMailId("<EMAIL>");
    assertTrue(PojoGenerator.getWatcher(WATCHER_MAIL_ID).equals(watcher));
  }

}
