package com.curefit.ticket.management.service.test;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.ResourceNotFoundException;
import com.curefit.odin.user.repositories.AttachmentDAO;
import com.curefit.odin.user.service.AttachmentService;
import com.curefit.odin.user.service.CommentService;
import com.curefit.odin.user.service.TicketService;

@RunWith(MockitoJUnitRunner.class)
public class AttachmentServiceTest {
	private static final long TICKET_ID = 4L;
	private static final long MESSAGE_ID = 8L;
	private static final long ATTACHMENT_ID = 6L;

	@Mock
	private AttachmentDAO attachmentDao;
	@Mock
	private CommentService messageService;
	@Mock
	private TicketService ticketService;
	@InjectMocks
	private AttachmentService attachmentService;

	@Before
	public void initMocks() {
		MockitoAnnotations.initMocks(this);
	}


	@Test(expected = ResourceNotFoundException.class)
	public void deleteByInvalidIdTest() throws BaseException {
		attachmentService.deleteById(ATTACHMENT_ID);
		verify(attachmentDao, times(1)).findById(ATTACHMENT_ID);
		verifyNoMoreInteractions(attachmentDao);
	}

}
