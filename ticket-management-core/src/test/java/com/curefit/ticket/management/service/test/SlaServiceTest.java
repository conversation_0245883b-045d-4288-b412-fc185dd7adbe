package com.curefit.ticket.management.service.test;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import com.curefit.odin.admin.models.SLAMapping;
import com.curefit.odin.admin.repositories.SlaMappingDao;
import com.curefit.odin.admin.service.SLAService;
import com.curefit.odin.enums.Priority;
import com.curefit.ticket.management.test.helper.PojoGenerator;

@RunWith(MockitoJUnitRunner.class)
public class SlaServiceTest {
	private static final long SUB_CATEGORY_ID = 3L;

	private static final long CATEGORY_ID = 2L;
	@Mock
	private SlaMappingDao slaMappingDao;
	@InjectMocks
	private SLAService slaService;

	@Before
	public void initMocks() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void convertToSlaMappingTest() {
		List<SLAMapping> mappings = slaService.convertToSlaMapping(CATEGORY_ID, SUB_CATEGORY_ID,
				PojoGenerator.getSlaMap(Priority.P0));
		assertTrue(PojoGenerator.getSlaMapping(CATEGORY_ID, SUB_CATEGORY_ID, Priority.P0).equals(mappings.get(0)));
	}

	@Test
	public void convertToMapTest() {
		List<SLAMapping> slaMappings = Collections
				.singletonList(PojoGenerator.getSlaMapping(CATEGORY_ID, SUB_CATEGORY_ID, Priority.P0));
		Map<Priority, Integer> map = slaService.convertToMap(slaMappings);
		assertTrue(PojoGenerator.getSlaMap(Priority.P0).equals(map));
	}

	@Test
	public void getSlaMappingsForCategory() {
		List<SLAMapping> slaMappings = Collections
				.singletonList(PojoGenerator.getSlaMapping(CATEGORY_ID, SUB_CATEGORY_ID, Priority.P0));
		when(slaMappingDao.findAllByCategoryId(any())).thenReturn(slaMappings);
		assertTrue(slaMappings.equals(slaService.getSlaMappings(CATEGORY_ID, null)));
	}

	@Test
	public void getSlaMappingsForSubCategory() {
		List<SLAMapping> slaMappings = Collections
				.singletonList(PojoGenerator.getSlaMapping(CATEGORY_ID, SUB_CATEGORY_ID, Priority.P0));
		when(slaMappingDao.findAllBySubCategoryId(any())).thenReturn(slaMappings);
		assertTrue(slaMappings.equals(slaService.getSlaMappings(null, SUB_CATEGORY_ID)));
	}

	@Test
	public void updateSlaMappingTest() {
		Map<Priority, Integer> map = PojoGenerator.getSlaMap(Priority.P0);
		List<SLAMapping> slaMappings = Collections
				.singletonList(PojoGenerator.getSlaMapping(CATEGORY_ID, SUB_CATEGORY_ID, Priority.P0));
		when(slaMappingDao.findAllByCategoryId(any())).thenReturn(slaMappings);
		when(slaMappingDao.saveAll(any())).thenReturn(Collections.EMPTY_LIST);
		slaService.updateSlaMapping(CATEGORY_ID, null, map);
		verify(slaMappingDao, times(1)).findAllByCategoryId(CATEGORY_ID);
		verify(slaMappingDao, times(1)).saveAll(slaMappings);

	}

	@Test
	public void addSlaMappingTest() {
		Map<Priority, Integer> map = PojoGenerator.getSlaMap(Priority.P2);
		List<SLAMapping> slaMappings = new ArrayList<>();
		slaMappings.add(PojoGenerator.getSlaMapping(CATEGORY_ID, null, Priority.P0));
		List<SLAMapping> expected = new ArrayList<>();
		expected.add(PojoGenerator.getSlaMapping(CATEGORY_ID, null, Priority.P0));
		expected.add(PojoGenerator.getSlaMapping(CATEGORY_ID, null, Priority.P2));
		when(slaMappingDao.findAllByCategoryId(any())).thenReturn(slaMappings);
		when(slaMappingDao.saveAll(any())).thenReturn(Collections.EMPTY_LIST);
		slaService.updateSlaMapping(CATEGORY_ID, null, map);
		verify(slaMappingDao, times(1)).findAllByCategoryId(CATEGORY_ID);
		verify(slaMappingDao, times(1)).saveAll(expected);
	}

	@Test
	public void getDueDateTest() {
		SLAMapping sla = PojoGenerator.getSlaMapping(CATEGORY_ID, null, Priority.P0);
		when(slaMappingDao.findAllByCategoryIdAndPriority(any(), any()))
				.thenReturn(Optional.of(sla));
		Long actual = slaService.getDueDate(CATEGORY_ID, null, Priority.P2);
//		assertTrue(ChronoUnit.HOURS.between(LocalDateTime.now(),actual)==3);
	}

}
