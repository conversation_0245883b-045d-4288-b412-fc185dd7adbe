package com.curefit.ticket.management.test.helper;

import com.curefit.odin.admin.models.*;
import com.curefit.odin.admin.pojo.*;
import com.curefit.odin.enums.DataType;
import com.curefit.odin.enums.Priority;
import com.curefit.odin.enums.Status;
import com.curefit.odin.user.models.*;
import com.curefit.odin.user.pojo.*;

import java.util.*;

public final class PojoGenerator {
  public static Tenant getTenant(Long id) {
    Tenant entity = new Tenant();
    entity.setId(id);
    entity.setName("entity");
    return entity;
  }

  public static CategoryEntry getTicketCategoryEntry(Long entityId, Long categoryId) {
    CategoryEntry category = new CategoryEntry();
    category.setId(categoryId);
    category.setTenantId(entityId);
    category.setName("cat1");
    return category;
  }

  public static Category getTicketCategory(Long categoryId) {
    Category ticketCategory = new Category();
    ticketCategory.setName("cat1");
    ticketCategory.setActive(true);
    ticketCategory.setId(categoryId);
    return ticketCategory;
  }

  public static SubCategory getTicketSubCategory(Long subCategoryId, Long categoryId) {
    SubCategory ticketSubCategory = new SubCategory();
    ticketSubCategory.setName("sub_cat1");
    ticketSubCategory.setActive(true);
    ticketSubCategory.setId(subCategoryId);
    ticketSubCategory.setCategory(getTicketCategory(categoryId));
    return ticketSubCategory;
  }

  public static SubCategoryEntry getTicketSubCategoryEntry(Long subCategoryId, Long categoryId) {
    SubCategoryEntry ticketSubCategory = new SubCategoryEntry();
    ticketSubCategory.setName("sub_cat1");
    ticketSubCategory.setId(subCategoryId);
    ticketSubCategory.setCategoryId(categoryId);
    return ticketSubCategory;
  }

  public static CustomFieldEntry getFieldEntry(Long subCategoryId, Long categoryId, Long fieldId) {
    CustomFieldEntry fieldEntry = new CustomFieldEntry();
    fieldEntry.setName("field1");
    fieldEntry.setId(fieldId);
    fieldEntry.setCategoryId(categoryId);
    fieldEntry.setSubCategoryId(subCategoryId);
    fieldEntry.setVersion(0l);
    fieldEntry.setDataType(DataType.TEXT);
    return fieldEntry;
  }

  public static CustomField getField(Long subCategoryId, Long categoryId, Long fieldId) {
    CustomField field = new CustomField();
    field.setName("field1");
    field.setId(fieldId);
    field.setDataType(DataType.TEXT);
    if (null != categoryId) {
      field.setCategory(PojoGenerator.getTicketCategory(categoryId));
    }
    if (null != subCategoryId) {
      field.setSubCategory(PojoGenerator.getTicketSubCategory(subCategoryId, categoryId));
    }
    field.setVersion(0l);
    return field;
  }

  // public static FieldDataEntry getFieldDataEntry(Long ticketId, Long fieldId, Long fieldDataId) {
  // FieldDataEntry fieldDataEntry = new FieldDataEntry();
  // fieldDataEntry.setValue("value");
  // fieldDataEntry.setId(fieldDataId);
  // fieldDataEntry.setTicketId(ticketId);
  // fieldDataEntry.setFieldId(fieldId);
  // return fieldDataEntry;
  // }

  public static FieldDataEntry getCustomFieldDataEntry(Long id, Long ticketId) {
    FieldDataEntry customFieldDataEntry = new FieldDataEntry();
    customFieldDataEntry.setId(id);
    FieldEntry customFieldEntry = new FieldEntry();
    customFieldEntry.setId(id);
    customFieldEntry.setValue("field1");
    customFieldEntry.setDataType(DataType.TEXT);
    customFieldDataEntry.setKey(customFieldEntry);
    customFieldDataEntry.setValue("value");
    customFieldDataEntry.setTicketId(ticketId);
    return customFieldDataEntry;
  }

  public static FieldData getFieldData(Long subCategoryId, Long categoryId, Long fieldDataId,
      Long ticketId, Long fieldId, Long roleId, Long entityId) {
    FieldData fieldData = new FieldData();
    fieldData.setValue("value");
    fieldData.setId(fieldDataId);
    if (null != categoryId) {
      // fieldData.setTicketId(getTicket(subCategoryId, categoryId, ticketId, roleId, entityId));
      fieldData.setTicket(new Ticket());
    }
    if (null != subCategoryId) {
      fieldData.setField(getField(subCategoryId, categoryId, fieldId));
    }
    return fieldData;
  }

  public static TicketEntry getTicketEntry(Long subCategoryId, Long categoryId, Long ticketId,
      Long fieldId, Long fieldDataId, Long roleId) {
    TicketEntry ticketEntry = new TicketEntry();
    ticketEntry.setId(ticketId);
    List<Long> id = new ArrayList<>();
    id.add(1l);
    ticketEntry.setAssignedUser(new UserEntry());
    ticketEntry.setSubCategoryId(subCategoryId);
    ticketEntry.setCategoryId(categoryId);
    ticketEntry.setCreatedBy("author");
    ticketEntry.setStatus(Status.OPEN);
    ticketEntry.setAttachments(new ArrayList<>());
    ticketEntry.setPriority(Priority.P2);
    ticketEntry.setVersion(0l);
    ticketEntry.setAssignedQueueId(roleId);
    // ticketEntry.setFields(Collections.EMPTY_LIST);
    ticketEntry.setCategoryName("cat1");
    ticketEntry.setSubCategoryName("sub_cat1");
    return ticketEntry;
  }

  public static List<FieldDataEntry> createFieldDataEntryList(Long fieldDataId, Long ticketId) {
    List<FieldDataEntry> fieldDataEntries = new ArrayList<>();
    fieldDataEntries.add(getCustomFieldDataEntry(fieldDataId, ticketId));
    return fieldDataEntries;
  }

  public static Ticket getTicket(Long subCategoryId, Long categoryId, Long ticketId, Long roleId,
      Long entityId) {
    Ticket ticket = new Ticket();
    ticket.setStatus(Status.OPEN);
    ticket.setId(ticketId);
    ticket.setCreatedBy("author");
    if (null != categoryId) {
      ticket.setCategory(getTicketCategory(categoryId));
    }
    if (null != subCategoryId) {
      ticket.setSubCategory(getTicketSubCategory(subCategoryId, categoryId));
    }
    ticket.setAssignee("<EMAIL>");
    ticket.setPriority(Priority.P2);
    ticket.setAttachments(new ArrayList<>());
    return ticket;
  }

  public static FieldEntry getCustomFieldEntry(Long id, DataType dataType, String value) {
    FieldEntry customFieldEntry = new FieldEntry(id, dataType, value);
    return customFieldEntry;
  }

  // public static CustomFieldValueEntry getCustomFieldValueEntry(Long id, String value) {
  // CustomFieldValueEntry customFieldValueEntry = new CustomFieldValueEntry(id, value);
  // return customFieldValueEntry;
  // }
  //
  // public static CustomFieldDataEntry getCustomFieldDataEntry(Long id, DataType dataType,
  // String value) {
  // CustomFieldDataEntry customFieldDataEntry = new CustomFieldDataEntry();
  // customFieldDataEntry.setKey(PojoGenerator.getCustomFieldEntry(id, dataType, value));
  // customFieldDataEntry.setValue("value");
  // return customFieldDataEntry;
  // }

  public static UserEntry getWatcher(String mailId) {
    UserEntry watcher = new UserEntry();
    watcher.setId(1l);
    watcher.setEmailId(mailId);
    watcher.setName("watcher");
    watcher.setVersion(0l);
    return watcher;
  }

  public static UserEntry getWatcherEntry(String emailId) {
    UserEntry watchersEntry = new UserEntry();
    List<Long> ticketId = new ArrayList<>();
    ticketId.add(1l);
    watchersEntry.setId(1l);
    watchersEntry.setEmailId(emailId);
    watchersEntry.setName("watcher");
    watchersEntry.setVersion(0l);
    return watchersEntry;
  }

  public static TicketWatcher getTicketWatcher(Long id, Long subCategoryId, Long categoryId,
      Long ticketId, String watcherId, Long roleId, Long entityId) {
    TicketWatcher ticketWatcher = new TicketWatcher();
    ticketWatcher.setId(id);
    ticketWatcher
        .setTicket(PojoGenerator.getTicket(subCategoryId, categoryId, ticketId, roleId, entityId));
    List<UserEntry> user = new ArrayList<>();
    user.add(PojoGenerator.getWatcher(watcherId));
    ticketWatcher.setUserId(PojoGenerator.getWatcher(watcherId).getEmailId());
    return ticketWatcher;
  }

  public static TicketWatcherEntry getTicketWatcherEntry(Long id, Long ticketId, String watcherId) {
    TicketWatcherEntry ticketWatcherEntry = new TicketWatcherEntry();
    List<String> watcherIds = new ArrayList<>();
    watcherIds.add(watcherId);
    ticketWatcherEntry.setUserId(watcherId);
    ticketWatcherEntry.setTicketId(ticketId);
    ticketWatcherEntry.setId(id);
    return ticketWatcherEntry;
  }

  public static DefaultAssigneeEntry getDefaultAssigneeEntry(long subCategoryId, long roleId,
      long relationId) {
    DefaultAssigneeEntry entry = new DefaultAssigneeEntry();
    entry.setSubCategoryId(subCategoryId);
    entry.setId(relationId);
    return entry;
  }

  public static List<AttachmentEntry> getAttachmentEntries(Long ticketId, Long messageId,
      Long attachmentId) {
    AttachmentEntry entry = getAttachmentEntry(ticketId, messageId, attachmentId);
    return Collections.singletonList(entry);
  }

  public static List<Attachment> getAttachments(Long ticketId, Long messageId, Long attachmentId) {
    Attachment entry = getAttachment(ticketId, messageId, attachmentId);
    return Collections.singletonList(entry);
  }

  public static AttachmentEntry getAttachmentEntry(Long ticketId, Long messageId,
      Long attachmentId) {
    AttachmentEntry entry = new AttachmentEntry();
    entry.setId(attachmentId);
    entry.setTicketId(ticketId);
    entry.setCommentId(messageId);
    entry.setUrl("http://url1");
    entry.setDescription("description");
    return entry;
  }

  public static Attachment getAttachment(Long ticketId, Long messageId, Long attachmentId) {
    Attachment entry = new Attachment();
    entry.setId(attachmentId);
    entry.setTicket(new Ticket());
    entry.setComment(new Comment());
    entry.setUrl("http://url1");
    entry.setDescription("description");
    entry.setActive(true);
    return entry;
  }

  public static Comment getMessage(Long messageId) {
    Comment message = new Comment();
    message.setId(messageId);
    message.setComment("message");
    message.setAttachments(new ArrayList<>());
    return message;
  }

  public static CommentEntry getMessageEntry(Long messageId) {
    CommentEntry entry = new CommentEntry();
    entry.setId(messageId);
    entry.setComment("message");
    entry.setAttachments(new ArrayList<AttachmentEntry>());
    return entry;
  }

  public static TicketAttachmentsUpdateEntry getTicketAttachmentsUpdateEntry(boolean override) {
    TicketAttachmentsUpdateEntry entry = new TicketAttachmentsUpdateEntry();
    entry.setOverride(override);
    List<AttachmentEntry> attachments = new ArrayList<>();
    attachments.add(getAttachmentEntry(null, 1L, 1L));
    attachments.add(getAttachmentEntry(null, 1L, 2L));
    entry.setAttachments(attachments);
    return entry;
  }

  public static DataSource getDynamicDataSource(Long id) {
    DataSource source = new DataSource();
    source.setActive(true);
    source.setId(id);
    source.setUrl("url");
    source.setKeyMapping("{test:dest}");
    source.setName("name");
    return source;
  }

  public static DataSourceEntry getDynamicDataSourceEntry(Long id) {
    DataSourceEntry source = new DataSourceEntry();
    source.setActive(true);
    source.setId(id);
    source.setUrl("url");
    //source.setMappingObject("{test:dest}");
    source.setName("name");
    return source;
  }

  public static DataSource getStaticDataSource(Long id) {
    DataSource source = new DataSource();
    source.setActive(true);
    source.setId(id);
    source.setStatic(true);
    source.setName("name");
    return source;
  }

  public static DataSourceEntry getStaticDataSourceEntry(Long id, List<String> values) {
    DataSourceEntry source = new DataSourceEntry();
    source.setActive(true);
    source.setId(id);
    source.setStaticList(true);
    source.setName("name");
    return source;
  }

  public static DataSourceValue getDataSourceValue(Long dataSourceId, String value, String valId) {
    DataSourceValue sourceValue = new DataSourceValue();
    sourceValue.setActive(true);
    sourceValue.setValue(value);
    sourceValue.setReferenceId(valId);
    return sourceValue;
  }

  public static DataSourceValueEntry getDataSourceValueEntry(Long dataSourceId, String value,
      String valId) {
    DataSourceValueEntry sourceValue = new DataSourceValueEntry();
    sourceValue.setActive(true);
    sourceValue.setValue(value);
    sourceValue.setReferenceId(valId);
    return sourceValue;
  }

  public static SLAMapping getSlaMapping(Long categoryId, Long subCategoryId, Priority priority) {
    return new SLAMapping(categoryId, subCategoryId, priority, 4);
  }

  public static Map<Priority, Integer> getSlaMap(Priority priority) {
    Map<Priority, Integer> map = new HashMap<>();
    map.put(priority, 4);
    return map;
  }

}
