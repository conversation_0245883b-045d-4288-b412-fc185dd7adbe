package com.curefit.ticket.management.service.test;

import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

import java.util.Collections;
import java.util.Optional;

import org.dozer.DozerBeanMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.ResourceNotFoundException;
import com.curefit.odin.admin.models.DataSource;
import com.curefit.odin.admin.pojo.DataSourceEntry;
import com.curefit.odin.admin.repositories.DataSourceDAO;
import com.curefit.odin.admin.service.DataSourceService;
import com.curefit.odin.admin.service.DataSourceValueService;
import com.curefit.odin.exceptions.InvalidDataException;
import com.curefit.ticket.management.test.helper.PojoGenerator;

@RunWith(MockitoJUnitRunner.class)
public class DataSourceServiceTest {
	private static final long SOURCE_ID = 4L;

	@Mock
	private DataSourceDAO baseMySQLRepository;
	@Mock
	private DataSourceValueService dataSourceValueService;
	@Mock
	private DozerBeanMapper mapper;
	@InjectMocks
	private DataSourceService dataSourceService;

	@Before
	public void initMocks() {
		MockitoAnnotations.initMocks(this);
	}

//	@Test
//	public void getDynamicDataSourcesTest() throws BaseException {
//		when(baseMySQLRepository.findAllByStaticListFalseAndActiveTrue())
//				.thenReturn(Collections.singletonList(PojoGenerator.getDynamicDataSource(SOURCE_ID)));
//		assertTrue(
//				dataSourceService.getActiveDynamicDataSources().contains(PojoGenerator.getDynamicDataSourceEntry(SOURCE_ID)));
//	}

//	@Test
//	public void createDataSourceTest() throws BaseException {
//		when(dataSourceDao.save(Mockito.any())).thenReturn(PojoGenerator.getDynamicDataSource(1L));
//		DataSourceEntry entry = dataSourceService.create(PojoGenerator.getDynamicDataSourceEntry(1L));
//		Mockito.doAnswer(new Answer<DataSourceEntry>() {
//			@Override
//			public DataSourceEntry answer(InvocationOnMock invocation) throws Throwable {
//				return PojoGenerator.getDynamicDataSourceEntry(1L);
//			}
//		}).when(dataSourceService).create(Mockito.any());
//
//		entry = dataSourceService.create(PojoGenerator.getDynamicDataSourceEntry(1L));
//
//		assertTrue(entry.equals(PojoGenerator.getDynamicDataSourceEntry(1L)));
//	}

	@Test
	public void findByIdTest() throws ResourceNotFoundException, BaseException {
		when(baseMySQLRepository.findById(Mockito.any())).thenReturn(Optional.of(PojoGenerator.getStaticDataSource(1L)));
		DataSource datasource = dataSourceService.fetchEntityById(1L);
		assertTrue(datasource.equals(PojoGenerator.getStaticDataSource(1L)));
	}

	@Test(expected = InvalidDataException.class)
	public void findByIdNegativeTest() throws ResourceNotFoundException, BaseException {
		when(baseMySQLRepository.findById(Mockito.any())).thenReturn(Optional.empty());
		dataSourceService.fetchEntityById(1L);
	}

	@Test
	public void deleteByIdTest() throws ResourceNotFoundException, BaseException {
		DataSource staticDataSource = Mockito.spy(PojoGenerator.getStaticDataSource(1L));
		staticDataSource
				.setDataSourceValues(Collections.singletonList(PojoGenerator.getDataSourceValue(1L, "val", "2L")));
		when(baseMySQLRepository.findById(Mockito.any())).thenReturn(Optional.of(staticDataSource));
		when(baseMySQLRepository.save(Mockito.any())).thenReturn(staticDataSource);
		dataSourceService.fetchEntityById(1L);
		assertTrue(staticDataSource.getActive() == false);
	}

//	@Test
//	public void overrideDataSourceValuesTest() throws ResourceNotFoundException, BaseException {
//		when(baseMySQLRepository.findById(Mockito.any())).thenReturn(Optional.of(PojoGenerator.getStaticDataSource(1L)));
//		DataSourceEntry entry = dataSourceService.overrideDataSourceValues(
//				PojoGenerator.getStaticDataSourceEntry(1L, Collections.singletonList("val")), 1L);
//	}

}
