package com.curefit.ticket.management.service.test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.List;

import com.curefit.odin.utils.service.FalconService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import com.curefit.common.data.exception.BaseException;
import com.curefit.falcon.Filter;
import com.curefit.falcon.FilterType;
import com.curefit.falcon.Location;
import com.curefit.falcon.SelectFilterValue;
import com.curefit.falcon.client.SearchClient;
import com.curefit.falcon.request.SearchRequest;
import com.curefit.falcon.response.SearchResponse;

@RunWith(MockitoJUnitRunner.class)
public class FalconServiceTest {

    private static final String TEST_EMAIL = "<EMAIL>";
    private static final String TEST_QUERY = "test query";

    @Mock
    private SearchClient searchClient;

    @InjectMocks
    private FalconService falconService;

    @Before
    public void initMocks() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testFetchUserByEmailId_Success() throws BaseException {
        // Arrange
        SearchResponse expectedResponse = new SearchResponse();
        when(searchClient.search(any(SearchRequest.class), eq("odin"), anyString(), isNull(), isNull(), eq(false)))
                .thenReturn(expectedResponse);

        // Act
        SearchResponse actualResponse = falconService.fetchUserByEmailId(TEST_EMAIL);

        // Assert
        assertNotNull(actualResponse);
        assertEquals(expectedResponse, actualResponse);
        verify(searchClient, times(1)).search(any(SearchRequest.class), eq("odin"), anyString(), isNull(), isNull(), eq(false));
    }

    @Test(expected = RuntimeException.class)
    public void testFetchUserByEmailId_SearchClientThrowsException() throws BaseException {
        // Arrange
        when(searchClient.search(any(SearchRequest.class), eq("odin"), anyString(), isNull(), isNull(), eq(false)))
                .thenThrow(new RuntimeException("Search client error"));

        // Act
        falconService.fetchUserByEmailId(TEST_EMAIL);

        // Assert - Exception should be thrown
    }

    @Test
    public void testFetchUserByEmailId_VerifySearchRequestParameters() throws BaseException {
        // Arrange
        SearchResponse expectedResponse = new SearchResponse();
        when(searchClient.search(any(SearchRequest.class), eq("odin"), anyString(), isNull(), isNull(), eq(false)))
                .thenReturn(expectedResponse);

        // Act
        falconService.fetchUserByEmailId(TEST_EMAIL);

        // Assert - Verify the method was called with correct parameters
        verify(searchClient, times(1)).search(any(SearchRequest.class), eq("odin"), anyString(), isNull(), isNull(), eq(false));
    }

    @Test
    public void testGetSearchRequest_ValidQuery() {
        SearchRequest searchRequest = falconService.getSearchRequest(TEST_QUERY);
        System.out.println("SearchRequest applied filters: "+searchRequest.getAppliedFilters());
        assertNotNull(searchRequest);
        assertEquals(TEST_QUERY, searchRequest.getQuery());

        assertNotNull(searchRequest.getLocations());
        assertEquals(1, searchRequest.getLocations().size());
        assertEquals("IN", searchRequest.getLocations().getFirst().getCountryId());
        assertEquals("Bangalore", searchRequest.getLocations().getFirst().getCityId());
        assertNotNull(searchRequest.getAppliedFilters());
        assertFalse(searchRequest.getAppliedFilters().isEmpty());
    }

    @Test
    public void testGetSearchRequest_NullQuery() {
        SearchRequest searchRequest = falconService.getSearchRequest(null);
        assertNotNull(searchRequest);
        assertNull(searchRequest.getQuery());
        assertEquals(1,searchRequest.getPageNumber().intValue());
        assertEquals(20, searchRequest.getCount().intValue());
    }

    @Test
    public void testGetSearchRequest_EmptyQuery() {
        // Act
        SearchRequest searchRequest = falconService.getSearchRequest("");

        // Assert
        assertNotNull(searchRequest);
        assertEquals("", searchRequest.getQuery());
        assertEquals(1,searchRequest.getPageNumber().intValue());
        assertEquals(20, searchRequest.getCount().intValue());
    }

    @Test
    public void testGetAppliedFilters_Structure() {
        // Act
        List<Filter> appliedFilters = falconService.getAppliedFilters();

        // Assert
        assertNotNull(appliedFilters);
        assertEquals(1, appliedFilters.size());

        // Verify top-level vertical filter
        Filter verticalFilter = appliedFilters.getFirst();
        assertNotNull(verticalFilter);
        assertEquals("vertical", verticalFilter.getKey());
        assertEquals("Vertical", verticalFilter.getTitle());
        assertEquals(FilterType.SELECT, verticalFilter.getType());
        assertNotNull(verticalFilter.getValues());
        assertEquals(1, verticalFilter.getValues().size());

        // Verify ODIN value
        SelectFilterValue odinValue = (SelectFilterValue) verticalFilter.getValues().getFirst();
        assertEquals("ODIN", odinValue.getValue());
        assertEquals("ODIN", odinValue.getTitle());
        assertNotNull(odinValue.getNestedFilters());
    }

    @Test
    public void testGetAppliedFilters_NestedStructure() {
        // Act
        List<Filter> appliedFilters = falconService.getAppliedFilters();

        // Assert - Verify nested structure
        Filter verticalFilter = appliedFilters.getFirst();
        SelectFilterValue odinValue = (SelectFilterValue) verticalFilter.getValues().getFirst();

        // Verify SubVertical filter
        Filter subVerticalFilter = odinValue.getNestedFilters().getFirst();
        assertEquals("subVertical", subVerticalFilter.getKey());
        assertEquals("SubVertical", subVerticalFilter.getTitle());
        assertEquals(FilterType.SELECT, subVerticalFilter.getType());
        assertNotNull(subVerticalFilter.getValues());
        assertEquals(1, subVerticalFilter.getValues().size());

        // Verify TICKETING_SYSTEM value
        SelectFilterValue ticketingSystemValue = (SelectFilterValue) subVerticalFilter.getValues().getFirst();
        assertEquals("TICKETING_SYSTEM", ticketingSystemValue.getValue());
        assertEquals("TICKETING_SYSTEM", ticketingSystemValue.getTitle());
        assertNotNull(ticketingSystemValue.getNestedFilters());
        assertEquals(1, ticketingSystemValue.getNestedFilters().size());

        // Verify Entity filter
        Filter entityFilter = ticketingSystemValue.getNestedFilters().getFirst();
        assertEquals("attributes.entityDisplayName", entityFilter.getKey());
        assertEquals("Entity", entityFilter.getTitle());
        assertEquals(FilterType.SELECT, entityFilter.getType());
        assertNotNull(entityFilter.getValues());
        assertEquals(1, entityFilter.getValues().size());

        // Verify User value
        SelectFilterValue userValue = (SelectFilterValue) entityFilter.getValues().getFirst();
        assertEquals("User", userValue.getValue());
        assertEquals("User", userValue.getTitle());
    }

    @Test
    public void testGetAppliedFilters_Consistency() {
        // Act - Call multiple times
        List<Filter> filters1 = falconService.getAppliedFilters();
        List<Filter> filters2 = falconService.getAppliedFilters();

        // Assert - Should return consistent structure
        assertNotNull(filters1);
        assertNotNull(filters2);
        assertEquals(filters1.size(), filters2.size());

        // Verify structure is the same
        assertEquals(filters1.getFirst().getKey(), filters2.getFirst().getKey());
        assertEquals(filters1.getFirst().getTitle(), filters2.getFirst().getTitle());
        assertEquals(filters1.getFirst().getType(), filters2.getFirst().getType());
    }

    @Test
    public void testFetchUserByEmailId_WithNullEmail() throws BaseException {
        // Arrange
        SearchResponse expectedResponse = new SearchResponse();
        when(searchClient.search(any(SearchRequest.class), eq("odin"), anyString(), isNull(), isNull(), eq(false)))
                .thenReturn(expectedResponse);

        // Act
        SearchResponse actualResponse = falconService.fetchUserByEmailId(null);

        // Assert
        assertNotNull(actualResponse);
        assertEquals(expectedResponse, actualResponse);
        verify(searchClient, times(1)).search(any(SearchRequest.class), eq("odin"), anyString(), isNull(), isNull(), eq(false));
    }

    @Test
    public void testFetchUserByEmailId_WithEmptyEmail() throws BaseException {
        // Arrange
        SearchResponse expectedResponse = new SearchResponse();
        when(searchClient.search(any(SearchRequest.class), eq("odin"), anyString(), isNull(), isNull(), eq(false)))
                .thenReturn(expectedResponse);

        // Act
        SearchResponse actualResponse = falconService.fetchUserByEmailId("");

        // Assert
        assertNotNull(actualResponse);
        assertEquals(expectedResponse, actualResponse);
        verify(searchClient, times(1)).search(any(SearchRequest.class), eq("odin"), anyString(), isNull(), isNull(), eq(false));
    }
}
