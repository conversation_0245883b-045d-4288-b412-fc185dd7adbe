package com.curefit.ticket.management.service.test;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import com.curefit.odin.admin.models.DataSourceValue;
import com.curefit.odin.admin.pojo.DataSourceValueEntry;
import com.curefit.odin.admin.repositories.DataSourceValueDAO;
import com.curefit.odin.admin.service.DataSourceValueService;
import com.curefit.odin.exceptions.InvalidDataException;
import com.curefit.odin.user.pojo.FieldEntry;
import com.curefit.ticket.management.test.helper.PojoGenerator;

@RunWith(MockitoJUnitRunner.class)
public class DataSourceValueTest {
	private static final long SOURCE_ID = 4L;
	@Mock
	private DataSourceValueDAO dataSourceValueDao;
	@InjectMocks
	private DataSourceValueService dataSourceValueService;

	@Before
	public void initMocks() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void bulkCreateTest() {
		DataSourceValue dataSourceValue = PojoGenerator.getDataSourceValue(SOURCE_ID, "val1", "1L");
		// when(dataSourceValueDao.findByValueIdAndDataSourceId(2L,
		// SOURCE_ID)).thenReturn(Optional.empty());
		// when(dataSourceValueDao.findByValueIdAndDataSourceId(1L,
		// SOURCE_ID)).thenReturn(Optional.of(dataSourceValue));
		// when(dataSourceValueDao.findByValueIdAndDataSourceId(3L,
		// SOURCE_ID)).thenReturn(Optional.empty());
		// when(dataSourceValueDao.saveAll(any()))
		// .thenReturn(Collections.singletonList(PojoGenerator.getDataSourceValue(SOURCE_ID,
		// "", 1L)));
//		List<DataSourceValue> sourceValues = Mockito.spy(new ArrayList<>());
//		sourceValues.add(dataSourceValue);
//		DataSourceValue dataSourceValue2 = PojoGenerator.getDataSourceValue(SOURCE_ID, "val2", 2L);
//		sourceValues.add(dataSourceValue2);
//		DataSourceValue dataSourceValue3 = PojoGenerator.getDataSourceValue(SOURCE_ID, "val3", 3L);
//		sourceValues.add(dataSourceValue3);
//		// dataSourceValueService.bulkCreateOrUpdate(sourceValues);
//		List<DataSourceValue> newValuesToBeSaved = new ArrayList<>();
//		newValuesToBeSaved.add(dataSourceValue2);
//		newValuesToBeSaved.add(dataSourceValue3);
//		verify(dataSourceValueDao, times(1)).saveAll(newValuesToBeSaved);
	}
}
