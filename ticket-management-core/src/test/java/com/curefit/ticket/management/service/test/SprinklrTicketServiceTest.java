package com.curefit.ticket.management.service.test;

import com.curefit.odin.sprinklr.pojo.SprinklrTicketEntry;
import com.curefit.odin.sprinklr.service.SprinklrTicketService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.util.*;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class SprinklrTicketServiceTest {

    @InjectMocks
    private SprinklrTicketService sprinklrTicketService;

    private Method mergeCustomPropertiesMethod;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        
        // Get the private method using reflection
        mergeCustomPropertiesMethod = SprinklrTicketService.class.getDeclaredMethod(
                "mergeCustomProperties", SprinklrTicketEntry.class, SprinklrTicketEntry.class);
        mergeCustomPropertiesMethod.setAccessible(true);
    }

    // Note: Removed parameter null tests since entries are never null in actual usage

    @Test
    public void testMergeCustomProperties_ExistingCustomPropertiesNull() throws Exception {
        // Test when existing entry has null custom properties
        SprinklrTicketEntry newEntry = createSprinklrTicketEntry();
        SprinklrTicketEntry existingEntry = createSprinklrTicketEntry();
        
        Map<String, List<Object>> newProperties = createSampleCustomProperties();
        newEntry.setCustomProperties(newProperties);
        existingEntry.setCustomProperties(null);
        
        mergeCustomPropertiesMethod.invoke(sprinklrTicketService, newEntry, existingEntry);
        
        // New entry should retain its properties
        assertEquals(newProperties, newEntry.getCustomProperties());
    }

    @Test
    public void testMergeCustomProperties_NewCustomPropertiesNull() throws Exception {
        // Test when new entry has null custom properties
        SprinklrTicketEntry newEntry = createSprinklrTicketEntry();
        SprinklrTicketEntry existingEntry = createSprinklrTicketEntry();
        
        Map<String, List<Object>> existingProperties = createSampleCustomProperties();
        newEntry.setCustomProperties(null);
        existingEntry.setCustomProperties(existingProperties);
        
        mergeCustomPropertiesMethod.invoke(sprinklrTicketService, newEntry, existingEntry);
        
        // New entry should get existing properties
        assertEquals(existingProperties, newEntry.getCustomProperties());
    }

    @Test
    public void testMergeCustomProperties_MatchingKeys_LatestValuePreserved() throws Exception {
        // Test that matching keys preserve latest values (from newEntry)
        SprinklrTicketEntry newEntry = createSprinklrTicketEntry();
        SprinklrTicketEntry existingEntry = createSprinklrTicketEntry();
        
        Map<String, List<Object>> newProperties = new HashMap<>();
        newProperties.put("status", Arrays.asList("LATEST_STATUS"));
        newProperties.put("priority", Arrays.asList("HIGH"));
        
        Map<String, List<Object>> existingProperties = new HashMap<>();
        existingProperties.put("status", Arrays.asList("OLD_STATUS"));
        existingProperties.put("assignee", Arrays.asList("john.doe"));
        
        newEntry.setCustomProperties(newProperties);
        existingEntry.setCustomProperties(existingProperties);
        
        mergeCustomPropertiesMethod.invoke(sprinklrTicketService, newEntry, existingEntry);
        
        // Verify latest values are preserved for matching keys
        assertEquals(Arrays.asList("LATEST_STATUS"), newEntry.getCustomProperties().get("status"));
        assertEquals(Arrays.asList("HIGH"), newEntry.getCustomProperties().get("priority"));
        
        // Verify older values are preserved for non-matching keys
        assertEquals(Arrays.asList("john.doe"), newEntry.getCustomProperties().get("assignee"));
    }

    @Test
    public void testMergeCustomProperties_NonMatchingKeys_OlderValuesPreserved() throws Exception {
        // Test that non-matching keys preserve older values from existing entry
        SprinklrTicketEntry newEntry = createSprinklrTicketEntry();
        SprinklrTicketEntry existingEntry = createSprinklrTicketEntry();
        
        Map<String, List<Object>> newProperties = new HashMap<>();
        newProperties.put("newField", Arrays.asList("newValue"));
        
        Map<String, List<Object>> existingProperties = new HashMap<>();
        existingProperties.put("oldField", Arrays.asList("oldValue"));
        existingProperties.put("anotherField", Arrays.asList("anotherValue"));
        
        newEntry.setCustomProperties(newProperties);
        existingEntry.setCustomProperties(existingProperties);
        
        mergeCustomPropertiesMethod.invoke(sprinklrTicketService, newEntry, existingEntry);
        
        // Verify new properties are preserved
        assertEquals(Arrays.asList("newValue"), newEntry.getCustomProperties().get("newField"));
        
        // Verify older properties are added
        assertEquals(Arrays.asList("oldValue"), newEntry.getCustomProperties().get("oldField"));
        assertEquals(Arrays.asList("anotherValue"), newEntry.getCustomProperties().get("anotherField"));
    }


    @Test
    public void testMergeCustomProperties_EmptyMaps() throws Exception {
        // Test with empty maps
        SprinklrTicketEntry newEntry = createSprinklrTicketEntry();
        SprinklrTicketEntry existingEntry = createSprinklrTicketEntry();
        
        newEntry.setCustomProperties(new HashMap<>());
        existingEntry.setCustomProperties(new HashMap<>());
        
        mergeCustomPropertiesMethod.invoke(sprinklrTicketService, newEntry, existingEntry);
        
        // Should remain empty
        assertTrue(newEntry.getCustomProperties().isEmpty());
    }

    @Test
    public void testMergeCustomProperties_ComplexScenario() throws Exception {
        // Test complex scenario with mixed conditions
        SprinklrTicketEntry newEntry = createSprinklrTicketEntry();
        SprinklrTicketEntry existingEntry = createSprinklrTicketEntry();
        
        Map<String, List<Object>> newProperties = new HashMap<>();
        newProperties.put("status", Arrays.asList("NEW_STATUS")); // matching key - should preserve latest
        newProperties.put("priority", Arrays.asList("HIGH")); // new key
        
        Map<String, List<Object>> existingProperties = new HashMap<>();
        existingProperties.put("status", Arrays.asList("OLD_STATUS")); // matching key - should be overridden
        existingProperties.put("assignee", Arrays.asList("john.doe")); // old key - should be preserved
        
        newEntry.setCustomProperties(newProperties);
        existingEntry.setCustomProperties(existingProperties);
        
        mergeCustomPropertiesMethod.invoke(sprinklrTicketService, newEntry, existingEntry);
        
        Map<String, List<Object>> result = newEntry.getCustomProperties();
        
        // Verify latest value for matching key
        assertEquals(Arrays.asList("NEW_STATUS"), result.get("status"));
        
        // Verify new key is preserved
        assertEquals(Arrays.asList("HIGH"), result.get("priority"));
        
        // Verify old key is preserved
        assertEquals(Arrays.asList("john.doe"), result.get("assignee"));
        
        // Verify total number of keys
        assertEquals(3, result.size());
    }

    // Helper methods
    private SprinklrTicketEntry createSprinklrTicketEntry() {
        return SprinklrTicketEntry.builder()
                .caseId("CASE123")
                .caseNumber("12345")
                .userId("user123")
                .build();
    }

    private Map<String, List<Object>> createSampleCustomProperties() {
        Map<String, List<Object>> properties = new HashMap<>();
        properties.put("status", Arrays.asList("OPEN"));
        properties.put("priority", Arrays.asList("MEDIUM"));
        properties.put("assignee", Arrays.asList("test.user"));
        return properties;
    }
}
