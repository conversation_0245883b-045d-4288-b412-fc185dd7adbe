package com.curefit.ticket.management.service.test;

import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;
import java.util.ArrayList;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.ResourceNotFoundException;
import com.curefit.odin.admin.pojo.CustomFieldEntry;
import com.curefit.odin.admin.service.CustomFieldService;
import com.curefit.odin.enums.DataType;
import com.curefit.odin.exceptions.InvalidDataException;
import com.curefit.odin.user.models.FieldData;
import com.curefit.odin.user.pojo.FieldDataEntry;
import com.curefit.odin.user.repositories.FieldDataDAO;
import com.curefit.odin.user.service.FieldDataService;
import com.curefit.odin.user.service.TicketService;
import com.curefit.ticket.management.test.helper.PojoGenerator;

@RunWith(MockitoJUnitRunner.class)
public class FieldDataTest {

  private static final long TICKET_ID = 1L;
  private static final long FIELD_ID = 4L;
  private static final long FIELD_DATA_ID = 4L;
  private static final long ROLE_ID = 4L;
  private static final long ENTITY_ID = 4L;

  private static final long SUB_CATEGORY_ID = 3L;

  private static final long CATEGORY_ID = 2L;

  @Mock
  private FieldDataDAO fieldDataDao;
  @InjectMocks
  private FieldDataService fieldDataService;
  @Mock
  private TicketService ticketService;
  @Mock
  private CustomFieldService fieldService;

  private List<FieldData> fieldDataList;
  private List<FieldDataEntry> fieldDataEntryList;
  private List<FieldDataEntry> updatefieldDataEntryList;

  @Before
  public void initMocks() {
    MockitoAnnotations.initMocks(this);
    List<FieldData> fieldDataList = new ArrayList<>();
    fieldDataList.add(PojoGenerator.getFieldData(SUB_CATEGORY_ID, CATEGORY_ID, FIELD_DATA_ID,
        TICKET_ID, FIELD_ID, ROLE_ID, ENTITY_ID));
    this.fieldDataList = fieldDataList;

    List<FieldDataEntry> fieldDataEntryList = new ArrayList<>();
    fieldDataEntryList.add(PojoGenerator.getCustomFieldDataEntry(FIELD_DATA_ID, TICKET_ID));
    this.fieldDataEntryList = fieldDataEntryList;

    List<FieldDataEntry> updatefieldDataEntryList = new ArrayList<>();
    updatefieldDataEntryList.add(PojoGenerator.getCustomFieldDataEntry(FIELD_DATA_ID, TICKET_ID));
    this.updatefieldDataEntryList = updatefieldDataEntryList;

  }

  @Test
  public void convertToEntryTest() {
    FieldDataEntry entry =
        fieldDataService.convertToEntry(PojoGenerator.getFieldData(SUB_CATEGORY_ID, CATEGORY_ID,
            FIELD_DATA_ID, TICKET_ID, FIELD_ID, ROLE_ID, ENTITY_ID));
    assertTrue(PojoGenerator.getCustomFieldDataEntry(FIELD_DATA_ID, TICKET_ID).equals(entry));
  }

  @Test(expected = NullPointerException.class)
  public void convertToEntryTest2() {
    FieldDataEntry entry = fieldDataService.convertToEntry(
        PojoGenerator.getFieldData(null, null, FIELD_DATA_ID, null, null, null, null));
    assertTrue(PojoGenerator.getCustomFieldDataEntry(FIELD_DATA_ID, TICKET_ID).equals(entry));
  }

  @Test
  public void convertToEntityTest() throws ResourceNotFoundException, BaseException {
    // when(ticketService.findById(1l)).thenReturn(PojoGenerator.getTicket(SUB_CATEGORY_ID,
    // CATEGORY_ID,TICKET_ID,ROLE_ID,ENTITY_ID));
    when(fieldService.fetchEntityById(4l))
        .thenReturn(PojoGenerator.getField(SUB_CATEGORY_ID, CATEGORY_ID, FIELD_ID));
    FieldData entity = fieldDataService
        .convertToEntity(PojoGenerator.getCustomFieldDataEntry(FIELD_DATA_ID, TICKET_ID));
    assertTrue(PojoGenerator.getFieldData(SUB_CATEGORY_ID, CATEGORY_ID, FIELD_DATA_ID, TICKET_ID,
        FIELD_ID, ROLE_ID, ENTITY_ID).equals(entity));
  }

  @Test
  public void findFieldDataByTicketIdTest() {
    when(fieldDataDao.findAllByTicketId(1l)).thenReturn(fieldDataList);
    List<FieldDataEntry> fieldDataEntries = fieldDataService.findFieldDataByTicketId(TICKET_ID);
    assertTrue(
        PojoGenerator.createFieldDataEntryList(FIELD_DATA_ID, TICKET_ID).equals(fieldDataEntries));
  }
  //
  // @Test
  // public void deactivateByTicketIdTest() {
  // when(fieldDataDao.findAllByTicketId(TICKET_ID)).thenReturn(fieldDataList);
  // when(fieldDataDao.save(any())).thenReturn(PojoGenerator.getFieldData(SUB_CATEGORY_ID,CATEGORY_ID,FIELD_DATA_ID,TICKET_ID,FIELD_ID,ROLE_ID,ENTITY_ID));
  // List<FieldDataEntry> fieldDataEntries= fieldDataService.deactivateByTicketId(TICKET_ID);
  // assertTrue(PojoGenerator.createFieldDataEntryList(FIELD_DATA_ID,TICKET_ID).equals(fieldDataEntries));
  // }

  // @Test
  // public void findByTicketAndFieldId() {
  // when(fieldDataDao.findByTicketIdAndFieldId(TICKET_ID,
  // FIELD_ID)).thenReturn(PojoGenerator.getFieldData(SUB_CATEGORY_ID,CATEGORY_ID,FIELD_DATA_ID,TICKET_ID,FIELD_ID,ROLE_ID,ENTITY_ID));
  // FieldData fieldData= fieldDataService.findByTicketAndFieldId(TICKET_ID,FIELD_ID);
  // assertTrue(PojoGenerator.getFieldData(SUB_CATEGORY_ID,CATEGORY_ID,FIELD_DATA_ID,TICKET_ID,FIELD_ID,ROLE_ID,ENTITY_ID).equals(fieldData));
  // }

  // @Test
  // public void updateFieldDataTest() throws BaseException {
  // FieldDataEntry fieldDataEntry =
  // PojoGenerator.getFieldDataEntry(TICKET_ID,FIELD_ID,FIELD_DATA_ID);
  // Field field = PojoGenerator.getField(SUB_CATEGORY_ID, CATEGORY_ID, FIELD_ID);
  // when(fieldDataService.findFieldDataByTicketId(TICKET_ID)).thenReturn(fieldDataEntryList);
  // //when(fieldDataDao.findAllByTicketId(TICKET_ID)).thenReturn(fieldDataList);
  // when(fieldService.findById(FIELD_ID)).thenReturn(field);
  // //fieldDataService.updateFieldData(TICKET_ID,fieldDataList);
  // }


//  @Test
//  public void validationTest() throws InvalidDataException {
//    CustomFieldEntry field = PojoGenerator.getFieldEntry(SUB_CATEGORY_ID, CATEGORY_ID, FIELD_ID);
//    field.setDataType(DataType.NUMBER);
//    boolean result = fieldDataService.validateDataType("33", field);
//    assert (result);
//    field.setDataType(DataType.DATETIME);
//    boolean result1 = fieldDataService.validateDataType("2019-09-09 44:44:44", field);
//    assert (result1 == true);
//    field.setDataType(DataType.TEXT);
//    boolean result2 = fieldDataService.validateDataType("abc", field);
//    assert (result2 == true);
//  }

  @Test(expected = InvalidDataException.class)
  public void validationTest1() throws InvalidDataException {
    CustomFieldEntry field = PojoGenerator.getFieldEntry(SUB_CATEGORY_ID, CATEGORY_ID, FIELD_ID);
    field.setDataType(DataType.DATETIME);
    fieldDataService.validateDataType("kjnlkjsndfl", field);
  }

//  @Test
//  public void validationTest2() throws InvalidDataException {
//    CustomFieldEntry field = PojoGenerator.getFieldEntry(SUB_CATEGORY_ID, CATEGORY_ID, FIELD_ID);
//    field.setDataType(DataType.NUMBER);
//    boolean result = fieldDataService.validateDataType("cc", field);
//    assert (result == false);
//  }
//
//  @Test
//  public void validationTest4() throws InvalidDataException {
//    CustomFieldEntry field = PojoGenerator.getFieldEntry(SUB_CATEGORY_ID, CATEGORY_ID, FIELD_ID);
//    field.setDataType(DataType.NUMBER);
//    boolean result = fieldDataService.validateDataType("!!", field);
//    assert (result == false);
//  }
//
//  @Test(expected = InvalidDataException.class)
//  public void validationTest3() throws ClassNotFoundException, InvalidDataException {
//    CustomFieldEntry field = PojoGenerator.getFieldEntry(SUB_CATEGORY_ID, CATEGORY_ID, FIELD_ID);
//    boolean result = fieldDataService.validateDataType(new Object(), field);
//    assert (result == false);
//  }


  // @Test(expected = InvalidDataException.class)
  // public void checkMandatoryAndValidation2() {
  // FieldData fieldData =
  // PojoGenerator.getFieldData(SUB_CATEGORY_ID,CATEGORY_ID,FIELD_DATA_ID,TICKET_ID,FIELD_ID,ROLE_ID,ENTITY_ID);
  // Field field= PojoGenerator.getField(SUB_CATEGORY_ID,CATEGORY_ID,2l);
  // field.setMandatory(true);
  // fieldData.setValue(null);
  // fieldDataService.checkMandatoryAndValidation(field,fieldData);
  // }


  // @Test
  // public void deactivateByFieldIdTest() {
  // when(fieldDataDao.findAllByFieldId(FIELD_ID)).thenReturn(fieldDataList);
  // fieldDataService.deactivateByFieldId(FIELD_ID);
  // }

}
