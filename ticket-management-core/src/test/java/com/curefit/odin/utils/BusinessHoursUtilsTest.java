package com.curefit.odin.utils;

import com.curefit.odin.admin.pojo.BusinessHours;
import junit.framework.TestCase;
import lombok.Data;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.List;

public class BusinessHoursUtilsTest extends TestCase {
    @Data
    private static class BusinessHoursTestcase {
        private LocalDateTime evaluationTime;
        private long taskMinutes;
        private BusinessHours businessHours;
        private LocalDateTime expectedDueTime;

        public BusinessHoursTestcase(String evaluationTime, long taskMinutes, String businessHoursStartTime, String businessHoursEndTime, String expectedDueTime) {
            this.evaluationTime = LocalDateTime.parse(evaluationTime).truncatedTo(ChronoUnit.MINUTES);
            this.businessHours = new BusinessHours();
            this.businessHours.setStartTime(businessHoursStartTime);
            this.businessHours.setEndTime(businessHoursEndTime);
            this.taskMinutes = taskMinutes;
            this.expectedDueTime = LocalDateTime.parse(expectedDueTime).truncatedTo(ChronoUnit.MINUTES);
        }
    }

    private static void runTestcase(BusinessHoursTestcase businessHoursTestcase) {
        LocalDateTime dueDateTime = BusinessHoursUtils.calculateDueDateTime(businessHoursTestcase.getEvaluationTime(), businessHoursTestcase.getTaskMinutes(), businessHoursTestcase.getBusinessHours());
        assertEquals(businessHoursTestcase.getExpectedDueTime().truncatedTo(ChronoUnit.MINUTES), dueDateTime.truncatedTo(ChronoUnit.MINUTES));

        long evaluationTimeInMilli = businessHoursTestcase.getEvaluationTime().atZone(DateUtils.IST_ZONE).toInstant().toEpochMilli();
        long dueDateTimeInMilli = BusinessHoursUtils.calculateDueDateTime(evaluationTimeInMilli, businessHoursTestcase.getTaskMinutes(), businessHoursTestcase.getBusinessHours(), DateUtils.IST_ZONE);
        assertEquals(Duration.between(businessHoursTestcase.getEvaluationTime(), dueDateTime), Duration.ofMillis(dueDateTimeInMilli - evaluationTimeInMilli));
    }

    public void testShiftsWithInvalidBusinessHours() {
        List<BusinessHoursTestcase> testcases = Arrays.asList(
                new BusinessHoursTestcase("2024-01-01T06:30:00", 12 * 60, null, null, "2024-01-01T18:30:00"),
                new BusinessHoursTestcase("2024-01-01T06:30:00", 12 * 60, null, "0900", "2024-01-01T18:30:00"),
                new BusinessHoursTestcase("2024-01-01T06:30:00", 12 * 60, "0900", "0900", "2024-01-01T18:30:00")
        );
        for (BusinessHoursTestcase businessHoursTestcase : testcases) {
            runTestcase(businessHoursTestcase);
        }
    }

    public void testDayShiftsBeforeBusinessStartTime() {
        List<BusinessHoursTestcase> testcases = Arrays.asList(
                new BusinessHoursTestcase("2024-01-01T06:00:00", 2 * 60, "0800", "1800", "2024-01-01T10:00:00"),
                new BusinessHoursTestcase("2024-01-01T06:00:00", 12 * 60, "0800", "1800", "2024-01-02T10:00:00"),
                new BusinessHoursTestcase("2024-01-01T06:00:00", 12 * 60, "0800", "1800", "2024-01-02T10:00:00"),
                new BusinessHoursTestcase("2024-01-01T00:00:00", 53, "0800", "1800", "2024-01-01T08:53:00")
        );
        for (BusinessHoursTestcase businessHoursTestcase : testcases) {
            runTestcase(businessHoursTestcase);
        }
    }

    public void testDayShiftsInsideBusinessStartTime() {
        List<BusinessHoursTestcase> testcases = Arrays.asList(
                new BusinessHoursTestcase("2024-01-01T12:15:30", 2 * 60, "0600", "1800", "2024-01-01T14:15"),
                new BusinessHoursTestcase("2024-01-01T06:30:00", 1, "0600", "0800", "2024-01-01T06:31:00"),
                new BusinessHoursTestcase("2024-01-01T06:30:00", 12 * 60 + 53, "0600", "0800", "2024-01-07T07:23:00")
        );
        for (BusinessHoursTestcase businessHoursTestcase : testcases) {
            runTestcase(businessHoursTestcase);
        }
    }

    public void testDayShiftsAfterBusinessStartTime() {
        List<BusinessHoursTestcase> testcases = Arrays.asList(
                new BusinessHoursTestcase("2024-01-01T12:15:30", 2 * 60, "0600", "0800", "2024-01-02T08:00"),
                new BusinessHoursTestcase("2024-01-01T23:30:00", 12 * 60, "0800", "2000", "2024-01-02T20:00:00"),
                new BusinessHoursTestcase("2024-01-01T23:30:00", 12 * 60 + 1, "0800", "2000", "2024-01-03T08:01:00")
        );
        for (BusinessHoursTestcase businessHoursTestcase : testcases) {
            runTestcase(businessHoursTestcase);
        }
    }

    public void testNightShiftsInsideBusinessHours() {
        List<BusinessHoursTestcase> testcases = Arrays.asList(
                new BusinessHoursTestcase("2024-01-01T23:30:00", 2 * 60, "2000", "0800", "2024-01-02T01:30:00"),
                new BusinessHoursTestcase("2024-01-01T23:30:00", 12 * 60, "2000", "0800", "2024-01-02T23:30:00"),
                new BusinessHoursTestcase("2024-01-01T23:30:00", 5 * 21 * 60 + 30, "0900", "0600", "2024-01-07T00:00:00"),
                new BusinessHoursTestcase("2024-01-01T23:30:00", 5 * 21 * 60 + 60, "0900", "0600", "2024-01-07T00:30:00")
        );
        for (BusinessHoursTestcase businessHoursTestcase : testcases) {
            runTestcase(businessHoursTestcase);
        }
    }

    public void testNightShiftsOutsideBusinessHours() {
        List<BusinessHoursTestcase> testcases = Arrays.asList(
                new BusinessHoursTestcase("2024-01-01T07:00:00", 1, "0900", "0600", "2024-01-01T09:01:00"),
                new BusinessHoursTestcase("2024-01-01T07:00:00", 24 * 60, "0900", "0600", "2024-01-02T12:00:00"),
                new BusinessHoursTestcase("2024-01-01T07:00:00", 26 * 60, "0900", "0600", "2024-01-02T14:00:00")
        );
        for (BusinessHoursTestcase businessHoursTestcase : testcases) {
            runTestcase(businessHoursTestcase);
        }
    }
}
