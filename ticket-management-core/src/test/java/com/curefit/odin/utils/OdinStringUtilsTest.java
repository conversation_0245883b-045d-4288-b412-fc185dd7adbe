package com.curefit.odin.utils;

import junit.framework.TestCase;

public class OdinStringUtilsTest extends TestCase {
    public void testStringWithEmojis() {
        String input = "👌How are you?\uD83D\uDCF1";
        String expectedOutput = " How are you? ";
        String output = OdinStringUtils.replaceIncompatibleCharactersWithSpace(input);
        assertEquals(expectedOutput, output);
    }

    public void testStringWithDigits() {
        String input = "123";
        String expectedOutput = "123";
        String output = OdinStringUtils.replaceIncompatibleCharactersWithSpace(input);
        assertEquals(expectedOutput, output);
    }

    public void testStringWithSpaceCharacters() {
        String input = "123\n\t👌👌👌How are you";
        String expectedOutput = "123\n\t   How are you";
        String output = OdinStringUtils.replaceIncompatibleCharactersWithSpace(input);
        assertEquals(expectedOutput, output);
    }

    public void testStringWithDate() {
        String input = "2024-01-01";
        String expectedOutput = "2024-01-01";
        String output = OdinStringUtils.replaceIncompatibleCharactersWithSpace(input);
        assertEquals(expectedOutput, output);
    }

    public void testStringRandomKeyboardStrokes() {
        String input = "khg26887`   sk0=']\564/-+0xW&&&**-_=+";
        String output = OdinStringUtils.replaceIncompatibleCharactersWithSpace(input);
        assertEquals(input, output);
    }

    public void testEmptyString() {
        String input = "";
        String expectedOutput = "";
        String output = OdinStringUtils.replaceIncompatibleCharactersWithSpace(input);
        assertEquals(expectedOutput, output);
    }

    public void testNullString() {
        String output = OdinStringUtils.replaceIncompatibleCharactersWithSpace(null);
        assertNull(output);
    }
}
