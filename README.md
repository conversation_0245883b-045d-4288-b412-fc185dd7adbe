# Ticketing System

Ticketing system is our in-houses ticket/issue management service. Its frontend counterpart is odin.

## Table of Contents

1. [Running application on local](#running-application-on-local)
2. [Health Check](#health-check)
3. [Steps to publish new java client version](#steps-to-publish-new-java-client-version)

## Running application on local

1. Forward ports needed for running ticketing-system on local. See [local-forward-ports.sh](local-forward-ports.sh).
   ```shell
   sh local-forward-ports.sh
   ```
   Keep this running. No need to restart this on application code changes.

   **NOTE:**
   *You need to have `stage-config` file in `~/.kube` folder present for this to work. If you don't have, then
   download [kube-config.conf](https://github.com/curefit/port-forward/blob/master/kube-config.conf) file and paste
   at `~/.kube` with name `stage-config`  in your local.*

   *Also ensure to set `KUBECONFIG`. You can do this by adding this in `.zshrc` file at the top.*
    ```shell
    export KUBECONFIG=~/.kube/stage-config
    ```

2. Connect to redis over jumpbox. This will forward port to redis. See [local-connect-redis.sh](local-connect-redis.sh).
   ```shell
   sh local-connect-redis.sh
   ```

3. Connect to Mysql DB: **curefit** using teleport over port 62003.
   Refer [this](https://curefit.atlassian.net/wiki/spaces/DevOps/pages/2490761294/Accessing+RDS+Database+Instance+using+Teleport)
   on how to use teleport. If using another port, then replace in local yaml file.

4. Run/Debug/Re-run Application configuration. Use these environment variables:
   ```shell
   ENVIRONMENT=local;LOG_LEVEL=DEBUG;FRESHDESK_API_KEY=5wb9gwadP9ozl4PUTRoA;HR_FRESHDESK_API_KEY=1ZuRRnRGZld39B4tGt;API_KEY=556e736d-0499-4aa2-95f8-1cb6e5786829;GOOGLE_LOC_API_KEY=AIzaSyB0qwp9lC-3weu6KuBxlBu_9I37sr6tSk0
   ```

**💡 TIP:** Create intellij shell configurations for 1 & 2. Uncheck _Execute in the terminal_ in the configuration.

## Health Check

You can use this API to check heath status after service has started:

   ```shell
   curl --location --request GET 'http://localhost:13000/actuator/health'
   ```

## Steps to publish new java client version

1. First check your current java version.
   ```shell
   java --version
    ```
   Ensure your java version is same as mentioned in [Dockerfile](Dockerfile)(currently Java 11).

2. Use this to check current maven project version.
   ```shell
   mvn help:evaluate -q -DforceStdout -Dexpression=project.version
   ```

3. To update all version in all modules at once, use this. This will update versions in all pom.xml files at once.
   ```shell
   mvn versions:set -DgenerateBackupPoms=false -DnewVersion=1.1.2-SNAPSHOT
   ```

4. Use this to deploy the new version
   ```shell
   mvn clean deploy -Dmaven.test.skip=true
   ```
