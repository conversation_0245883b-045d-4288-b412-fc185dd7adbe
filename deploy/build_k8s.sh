#!/usr/bin/env bash
set -x
set -e

BUILD_DIR_PATH=$1
ENV=$2
BUILD_ARG=$3

if [[ -z "${BUILD_DIR_PATH}" ]]
then
    echo "Build Directory Path is Mandatory"
    exit 1
fi

if [[ -z "${ENV}" ]]
then
    echo "Environment is necessary for build"
    exit 1
fi

if [ -z "${BUILD_ARG}" ];then
    echo "Building App"
    mvn clean compile package -Dmaven.test.skip=true
else
    echo "Building App with custom arg!"
    mvn clean compile package -Dmaven.test.skip=true "${BUILD_ARG}"
fi

mkdir -p "${BUILD_DIR_PATH}"
curl -kL https://dtdg.co/latest-java-tracer -o dd-java-agent.jar
cp dd-java-agent.jar "${BUILD_DIR_PATH}"

cp ticket-management-core/target/ticket-management-core-*.jar "${BUILD_DIR_PATH}"
cp deploy/entrypoint.sh  "${BUILD_DIR_PATH}"

set +e
set +x
