#!/bin/bash
# PROD Setting : The Heap Memory settings are based on the assumptions that the application will run mostly on 8Gb Ram Box.  (For fine tuning Heap size look into : https://support.cloudbees.com/hc/en-us/articles/204859670-Java-Heap-settings-best-practice )
# PROD Setting : The G1GC Garbage collector is used initially, but based on the metrics and performance we can try -XX:+UseConcMarkSweepGC and keep whichever workds better.

set -x
set -e

echo "Ticketing Service starting in ENVIRONMENT : $ENVIRONMENT"

if [[ $ENVIRONMENT == "prod" ]]; then
  echo "PRODUCTION RUN!. STARTING WITH DATADOG."
  java -javaagent:dd-java-agent.jar -Xss512k -Xmx2g  -XX:+UnlockExperimentalVMOptions -XX:+UseG1GC -XX:MaxDirectMemorySize=256m -XX:MaxNewSize=512m -XX:MaxMetaspaceSize=512m -XX:G1MaxNewSizePercent=20 -XX:MaxHeapFreeRatio=30 -XX:MinHeapFreeRatio=10 -XX:InitiatingHeapOccupancyPercent=20 -XX:+UseStringDeduplication -XX:NativeMemoryTracking=summary  -Dserver.port=13000  -Ddd.trace.global.tags=env:$ENVIRONMENT -Ddd.service=ticketing-system -Ddd.service.mapping=sns:ticketing-system-sns,sqs:ticketing-system-sqs,mongo:ticketing-system-mongo,mysql:ticketing-system-mysql,redis:ticketing-system-redis -jar ticket-management*.jar
else
  java -Dserver.port=13000 -Ddd.trace.global.tags=env:$ENVIRONMENT -Ddd.service=ticketing-system -Ddd.service.mapping=sns:ticketing-system-sns,sqs:ticketing-system-sqs,mongo:ticketing-system-mongo,mysql:ticketing-system-mysql,redis:ticketing-system-redis -jar ticket-management*.jar
fi

set +e
set +x
